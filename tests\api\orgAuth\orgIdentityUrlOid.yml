name: 获取组织机构实名认证地址api
variables:
  appId: ${ENV(appId)}
request:
    url: /v2/identity/auth/web/$accountId/orgIdentityUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: $appId
        X-Tsign-Open-Auth-Mode: simple
    json:
      authType: $authType
      availableAuthTypes: $availableAuthTypes
      agentAuthAdvancedEnabled: $agentAuthAdvancedEnabled
      agentAccountId: $agentAccountId
      contextInfo:
        contextId: "测试"
        notifyUrl: ${ENV(notifyUrl)}
        origin: "BROWSER"
        redirectUrl: "http://baidu.com"
        showResultPage: "true"
      orgEntity:
        certNo: $certNo
        organizationType: $organizationType
        name: $name
        legalRepCertType: $legalRepCertType
        legalRepCertNo: $legalRepCertNo
        legalRepName: $legalRepName
      repeatIdentity: $repeatIdentity
