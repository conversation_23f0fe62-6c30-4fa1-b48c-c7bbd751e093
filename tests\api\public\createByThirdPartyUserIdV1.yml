name: v1创建机构账号-使用第三方userId(用户中心重构接口)
request:
    url: ${ENV(base_url_user)}/v1/organizations/createByThirdPartyUserId
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        thirdPartyUserId: ${create_randomId()}
        creator: $creatorOid
        name: $name
        idType: $idType
        idNumber: $idNumber
        orgLegalIdNumber: $orgLegalIdNumber
        orgLegalName: $orgLegalName
