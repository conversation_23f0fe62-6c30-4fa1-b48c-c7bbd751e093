name: 获取个人核身认证地址
request:
    url: /v2/identity/auth/web/indivAuthUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        authType: "PSN_BANK4_AUTHCODE"
        availableAuthTypes:
          - "PSN_TELECOM_AUTHCODE"
          - "PSN_BANK4_AUTHCODE"
          - "PSN_FACEAUTH_BYURL"
        contextInfo:
          contextId: "test"
          notifyUrl: ""
          redirectUrl: "https://www.baidu.com"
          showResultPage: "true"
        indivInfo:
          name: $name
          certNo: $certNo
        configParams:
          indivUneditableInfo:
            - "name"
            - "certNo"
            - "mobileNo"
            - "bankCardNo"