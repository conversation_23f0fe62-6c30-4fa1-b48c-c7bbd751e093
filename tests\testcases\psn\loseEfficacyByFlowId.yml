config:
    name: 创建个人账号，发起实名流程，通过flowid失效流程
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建oid
    api: api/public/createPsnAccount.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.accountId, null]

-
    name: 发起银行卡4要素实名认证
    api: api/psnAuth/bankCard4FactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 通过flowid失效流程
    api: api/public/loseEfficacyByFlowId.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]