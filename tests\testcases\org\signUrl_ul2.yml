config:
    name: 获取签署授权链接异常场景
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起企业核身认证4要素校验
    api: api/orgAuth/fourFactors.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 获取签署授权链接
    api: api/orgAuth/signUrl.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, $VALIDATE1]
      - contains: [content.message, $VALIDATE2]