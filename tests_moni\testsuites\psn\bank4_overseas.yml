config:
    name: 个人银行四要素_境外版

testcases:
-
    name: 个人银行四要素_境外版-$Case<PERSON>ame
    testcase: testcases/psn/bank4_overseas.yml
    parameters:
        - CaseName-certType-idNo-name-bankCardNo-mobileNo-VALIDATE1-VALIDATE2:
            - ["正常场景-> 证件类型为台胞证","INDIVIDUAL_CH_TWCARD","********","田欣慈","6222620910051004855","***********",0,"成功"]
            - ["正常场景-> 证件类型为空默认身份证","","******************","武玉华","****************","***********",0,"成功"]
            - ["正常场景-> 证件类型为护照","INDIVIDUAL_PASSPORT","H10561683","韩艾珊","622908393301650316","***********",0,"成功"]
            - ["正常场景-> 证件类型为澳门通行证","INDIVIDUAL_CH_HONGKONG_MACAO","M0215994202","余洁婷","6214620321001228346","***********",0,"成功"]
            - ["正常场景-> 英文名字中间支持空格","INDIVIDUAL_PASSPORT","*********","FAN HON SING","****************","***********",0,"成功"]


    name: 个人银行四要素_境外版-$CaseName->$CaseName
    api: bankCard4Factor($certType,$idNo,$name,$bankCardNo,$mobileNo)
    parameters:
        - CaseName-certType-idNo-name-bankCardNo-mobileNo-VALIDATE1-VALIDATE2:
            - ["异常场景-> certType不正确","INDIVIDUAL_CH_HONGKONG_MACAO","********","田欣慈","6222620910051004855","***********",********,证件号格式错误]
            - ["异常场景-> 中文名字含空格","INDIVIDUAL_CH_TWCARD","********","田 欣慈","6222620910051004855","***********",********,核验失败]
            - ["异常场景-> name不正确","INDIVIDUAL_CH_TWCARD","********","李逸群","6222620910051004855","***********",********,核验失败]
            - ["异常场景-> idNo不正确","INDIVIDUAL_CH_TWCARD","********","李逸群","6222620910051004855","***********",********,核验失败]
            - ["异常场景-> bankCardNo不正确","INDIVIDUAL_CH_TWCARD","********","李逸群","6222620910051004856","***********",********,核验失败]
            - ["异常场景-> mobileNo不正确","INDIVIDUAL_CH_TWCARD","********","李逸群","6222620910051004855","***********",********,核验失败]
            - ["异常场景-> name为空","INDIVIDUAL_CH_TWCARD","********","","6222620910051004855","***********",********,不能为空 ]
            - ["异常场景-> idNo为空","INDIVIDUAL_CH_TWCARD","","李逸群","6222620910051004855","***********",********,不能为空]
            - ["异常场景-> bankCardNo为空","INDIVIDUAL_CH_TWCARD","********","李逸群","","***********",********,不能为空]
            - ["异常场景-> mobileNo为空","INDIVIDUAL_CH_TWCARD","********","李逸群","6222620910051004855","",********,不能为空]