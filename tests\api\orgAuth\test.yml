variables:
    sql: 'select * from realname_reverse_payment_bill limit 1'
    realname_host: ${ENV(realname_host)}
    realname_db: ${ENV(realname_db)}
    realname_user: ${ENV(realname_user)}
    realname_passwd: ${ENV(realname_passwd)}
request:
    url: /v2/identity/auth/web/$accountId/orgIdentityUrl
    method: POST
    headers:
      Content-Type: application/json
      X-Tsign-Open-App-Id: $appId
      X-Tsign-Open-Auth-Mode: simple

    json:
      agentAccountId: $creatorOid
      contextInfo:
        contextId: ${fetch_data_sql($sql,$realname_host,$realname_db,$realname_user,$realname_passwd)}
        origin: "BROWSER"
        redirectUrl: "http://baidu.com"
        showResultPage: "true"
      repeatIdentity: true
