name: 发起企业实名核身4要素校验api-新增场景类型（默认为1-独立认证）
request:
    url: /v2/identity/auth/api/organization/enterprise/fourFactors
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        name: $orgName
        orgCode: $orgCode
        legalRepName: $legalRepName
        legalRepIdNo: $legalRepIdNo
        repetition: "true"
        contextId: ${create_randomId()}
        notifyUrl: ""
        legalRepCertType: $legalRepCertType
        agentFlowId: $agentFlowId
        scenesMode: $scenesMode #1-独立认证，2-印章使用授权

