config:
     name: 错误码整理-信息比对

testcases:
-
     name: 个人二要素比对
     testcase: testcases/checkInfo/psn2_1.yml
     parameters:
         - CaseName-idNo-name-VALIDATE1-VALIDATE2:
             - ["身份证号码不符合规则","******************2","武玉华",********,"参数错误：请输入正确的身份证号码"]
             - ["姓名不符合规则","******************","武",********,"参数错误：请输入正确长度的姓名（2-20位）"]

-
     name: 个人运营商三要素比对
     testcase: testcases/checkInfo/telecom3_1.yml
     parameters:
         - CaseName-name-idNo-mobileNo-VALIDATE1-VALIDATE2:
            - ["手机号非真实有效","武玉华","******************","**********",********,"参数错误：请输入正确的手机号码"]

-
     name: 个人银行卡三要素比对
     testcase: testcases/checkInfo/bank3_1.yml
     parameters:
         - CaseName-idNo-name-cardNo-VALIDATE1-VALIDATE2:
             - ["银行卡号非真实有效","******************","武玉华","62148",********,"参数错误：请输入正确的银行卡号"]
            
-
     name: 个人银行卡四要素比对
     testcase: testcases/checkInfo/bank4_1.yml
     parameters:
         - CaseName-idNo-name-cardNo-mobileNo-VALIDATE1-VALIDATE2:
             - ["手机号非真实有效","******************","武玉华","****************","**********",********,"参数错误：请输入正确的手机号码"]
             - ["银行卡号非真实有效","******************","武玉华","62148","***********",********,"参数错误：请输入正确的银行卡号"]

-
     name: 企业二要素比对
     testcase: testcases/checkInfo/enterprise2_1.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2:
             - ["企业名称为空或长度小于2","杭","************306077",********,"不匹配"]
             - ["企业证件号格式不正确","杭州天谷信息科技有限公司","************",********,"参数错误：请输入正确的组织机构证件号"]

-
     name: 企业三要素比对
     testcase: testcases/checkInfo/enterprise3_1.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
             - ["企业名称为空或长度小于2","杭","何一兵","************306077",********,"不匹配"] 
             - ["企业证件号格式不正确","杭州天谷信息科技有限公司","何一兵","330108",********,"参数错误：请输入正确的组织机构证件号"]
             - ["法定代表人姓名为空或长度小于2","杭州天谷信息科技有限公司","何","330108000003512",********,"正确长度"]

-
     name: 企业四要素比对
     testcase: testcases/checkInfo/enterprise4_1.yml
     parameters:
         - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2:
             - ["企业名称为空","","何一兵","110108196710262291","************306077",30500100,"缺少参数：企业名称不能为空"]

-
     name: 律所三要素比对
     testcase: testcases/checkInfo/lawFirm3_1.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["律所名称为空或长度小于2","北","郭大成","311100004005693352",30504002,"失败"]
             - ["律所统一社会信用代码号格式错误","北京市中银律师事务所","郭大成","31110",********,"参数错误：请输入正确的18位社会统一信用号码"]
             - ["法定代表人姓名小于2位","北京市中银律师事务所","郭","311100004005693352",********,"正确长度"]

-
     name: 组织机构三要素比对
     testcase: testcases/checkInfo/organization3_1.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
             - ["企业名称为空或长度小于2","杭","何一兵","************306077",********,"不匹配"]
             - ["法定代表人姓名错误","杭州天谷信息科技有限公司","何","************306077",********,"正确长度"]

-
     name: 非工商组织三要素比对
     testcase: testcases/checkInfo/orgSocial3_1.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["企业名称为空或长度小于2","天","金东寒","12100000401359321Q",********,"不匹配"]
             - ["法定代表人姓名错误","天津大学","金","12100000401359321Q",********,"正确长度"]   

        



