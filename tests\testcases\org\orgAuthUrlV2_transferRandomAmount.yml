config:
    name: 获取组织机构核身认证地址-通过正向打款完成核身(V2)
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取组织机构核身认证地址
    api: api/orgAuth/orgAuthUrl.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 埋点
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版企业经办人认证状态查询
    api: api/orgAuth/agentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.authorizedInV21, false]
      - eq: [content.data.authorisedInUserCenter, false]
      - eq: [content.data.authorizedInOrgFlow, false]
      - eq: [content.data.status, "AUTHENTICATE_REQUIRED"]

-
    name: 页面版获取企业经办人信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.name, $name]
      - eq: [content.data.oid, null]

-
    name: 页面版获取个人认证流程状态
    api: api/psnAuth/queryIndivFlowidStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.flowStatus, "INIT"]

-
    name: 页面版经办人发起运营商三要素
    api: api/psnAuth/telecom3FactorInfoAuth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版经办人发起运营商三要素验证码
    api: api/psnAuth/telecom3FactorAuthCode_web.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版经办人运营商三要素验证码校验
    api: api/psnAuth/telecom3Factor.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版经办人运营商三要素验证码校验
    api: api/psnAuth/telecom3Factor.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版企业页面实名认证, 结果接口-V1
    api: api/orgAuth/getOrgProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.ifInfoAuthSuccessful, false]
      - eq: [content.data.status, "INIT"]
      - eq: [content.data.type, "ORGANIZATION_INFO_AUTH"]

-
    name: 页面版获取企业信息
    api: api/orgAuth/queryOrganizationInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.agentName, $agentName]
      - eq: [content.data.certNo, $certNo]
      - eq: [content.data.legalRepCertNo, $legalRepCertNo]
      - eq: [content.data.legalRepName, $legalRepName]
      - eq: [content.data.name, $name]
      - eq: [content.data.legalRepCertType, $legalRepCertType]

-
    name: 页面版企业信息比对
    api: api/orgAuth/infoVerify.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.operatorType, 2]

-
    name: 页面版查询反向打款业务进度-进入打款之后查询一次
    api: api/orgAuth/reversePaymentProcessWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]
      - eq: [content.data.message, "子账户未创建"]

-
    name: 页面版查询随机金额业务进度
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]

-
    name: 页面版查询银行信息
    api: api/orgAuth/querySubbranch.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - ne: [content.data.list.0.bank, null]
      - contains: [content.data.list.0.bankName, $keyWord]