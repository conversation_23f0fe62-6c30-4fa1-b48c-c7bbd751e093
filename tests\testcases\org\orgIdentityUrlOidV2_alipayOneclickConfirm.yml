config:
    name: 获取组织机构实名认证地址V2-企业支付宝一键实名
    base_url: ${ENV(base_url_rpc)}

teststeps:
#创建企业oid，发起实名；经办人意愿-创建三种刷脸意愿并轮询结果，最后通过短信验证码完成；企业支付宝一键实名
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserIdV1.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlV2.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.url, null]

-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版获取企业信息
    api: api/orgAuth/queryOrganizationInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.agentName, null]
      - eq: [content.data.certNo, $idNumber]
      - eq: [content.data.legalRepCertNo, $orgLegalIdNumber]
      - eq: [content.data.legalRepName, $orgLegalName]
      - eq: [content.data.name, $name]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.display, null]

-
    name: 查询企业实名流程进度1
    api: api/orgAuth/getOrgProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.ifInfoAuthSuccessful, false]
      - eq: [content.data.status, "INIT"]
      - eq: [content.data.type, "ORGANIZATION_INFO_AUTH"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 企业经办人认证状态查询
    api: api/orgAuth/getAgentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.authorizedInOrgFlow, False]
      - eq: [content.data.authorizedInV21, True]
      - eq: [content.data.authorisedInUserCenter, True]
      - eq: [content.data.status, "WILLINGNESS_REQUIRED"]

-
    name: 获取经办人意愿方式
    api: api/psnAuth/getWillingnessType.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.willAuthType, null]

-
    name: 创建个人意愿刷脸认证-支付宝刷脸
    api: api/psnAuth/createWillingnessFaceauth.yml
    variables:
      - faceAuthMode: 2
      - PC: true
      - from: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.authUrl, null]

-
    name: 轮询个人意愿刷脸结果
    api: api/psnAuth/getWillingnessFaceauthPolling.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - len_eq: [content.data.result, 1]
      - eq: [content.data.result.0.passed, False]
      - eq: [content.data.result.0.status, "DOING"]

-
    name: 查询个人意愿刷脸结果
    api: api/psnAuth/getWillingnessFaceauth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - len_eq: [content.data.result, 1]
      - eq: [content.data.result.0.passed, False]
      - eq: [content.data.result.0.status, "DOING"]
      - eq: [content.data.result.0.message, "刷脸认证未完成"]

-
    name: 创建个人意愿刷脸认证-腾讯云刷脸
    api: api/psnAuth/createWillingnessFaceauth.yml
    variables:
      - faceAuthMode: 1
      - PC: true
      - from: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.authUrl, null]

-
    name: 轮询个人意愿刷脸结果2
    api: api/psnAuth/getWillingnessFaceauthPolling.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - len_eq: [content.data.result, 2]
      - eq: [content.data.result.0.passed, False]
      - eq: [content.data.result.0.status, "DOING"]

-
    name: 查询个人意愿刷脸结果2
    api: api/psnAuth/getWillingnessFaceauth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - len_eq: [content.data.result, 2]
      - eq: [content.data.result.0.passed, False]
      - eq: [content.data.result.0.status, "DOING"]
      - eq: [content.data.result.0.message, "刷脸认证未完成"]

-
    name: 创建个人意愿刷脸认证-自研刷脸
    api: api/psnAuth/createWillingnessFaceauth.yml
    variables:
      - faceAuthMode: 10
      - PC: true
      - from: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.authUrl, null]

-
    name: 轮询个人意愿刷脸结果3
    api: api/psnAuth/getWillingnessFaceauthPolling.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - len_eq: [content.data.result, 3]
      - eq: [content.data.result.0.passed, False]
      - eq: [content.data.result.0.status, "DOING"]

-
    name: 查询个人意愿刷脸结果3
    api: api/psnAuth/getWillingnessFaceauth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - len_eq: [content.data.result, 3]
      - eq: [content.data.result.0.passed, False]
      - eq: [content.data.result.0.status, "DOING"]
      - eq: [content.data.result.0.message, "刷脸认证未完成"]

-
    name: 创建个人意愿刷脸认证-微信小程序刷脸
    api: api/psnAuth/createWillingnessFaceauth.yml
    variables:
      - faceAuthMode: 12
      - PC: true
      - from: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.authUrl, null]

-
    name: 轮询个人意愿刷脸结果4
    api: api/psnAuth/getWillingnessFaceauthPolling.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - len_eq: [content.data.result, 4]
      - eq: [content.data.result.0.passed, False]
      - eq: [content.data.result.0.status, "DOING"]

-
    name: 查询个人意愿刷脸结果4
    api: api/psnAuth/getWillingnessFaceauth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - len_eq: [content.data.result, 4]
      - eq: [content.data.result.0.passed, False]
      - eq: [content.data.result.0.status, "DOING"]
      - eq: [content.data.result.0.message, "刷脸认证未完成"]

-
    name: 查询意愿手机号
    api: api/psnAuth/queryWillingnessMobileNo.yml
    extract:
      - receiver: content.data.mobileNo
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]

-
    name: 发起意愿手机验证码认证
    api: api/psnAuth/sendWillingnessMobileCodeauth.yml
    variables:
      - sendType: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验意愿手机验证码认证
    api: api/psnAuth/verifyWillingnessMobileCodeauth.yml
    variables:
      - authCode: "123456"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 申请企业支付宝一键实名
    api: api/orgAuth/alipayOneclickApply.yml
    variables:
      - orgCode: $idNumber
      - device: "PC"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - ne: [content.data.forwardUrl, null]

-
    name: 查询企业支付宝一键实名结果
    api: api/orgAuth/alipayOneclickResult.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.completed, False]
      - eq: [content.data.success, False]
      - eq: [content.data.message, "认证未完成"]

-
    name: 企业支付宝一键实名
    api: api/orgAuth/alipayOneclickConfirm.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.success, True]
      - eq: [content.data.message, "认证成功"]

-
    name: 查询企业支付宝一键实名结果
    api: api/orgAuth/alipayOneclickResult.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.success, True]
      - eq: [content.data.message, "认证成功"]
