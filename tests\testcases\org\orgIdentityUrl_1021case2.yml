config:
    name: 实名入参中账号信息修改可以发起实名
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建账号
    api: api/public/createByThirdPartyUserId_orgV1.yml
    extract:
      - accountId: content.data.orgId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.orgId, null]

-
    name: 获取企业实名认证地址
    api: api/orgAuth/orgIdentityUrlOid3.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, $VALIDATE1]
      - contains: [content.message, $VALIDATE2]

