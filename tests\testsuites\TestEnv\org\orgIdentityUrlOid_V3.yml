config:
    name: 企业重构集测用例

testcases:
-
    name: 企业实名重构页面版对公打款--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV3_randomAmount.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType-operatorType-keyWord-certType-certNo-authType-bizSource-serviceStatusForQuery-fileKey-email-version-organizationType:
            - ["正常用例","${ENV(appId3)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋","",2,"平安银行","","91440300MA5ERJGK30","","实名集测用","","${getfileKey()}","<EMAIL>","V3","12"]

-
    name: 企业实名重构页面版反向打款--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV3_reverseRandomAmount.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType-operatorType-fileKey-certType-certNo-authType-bizSource-serviceStatusForQuery-version-organizationType:
            - ["正常用例","${ENV(appId3)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋","",2,"${getfileKey()}",11,"91440300MA5ERJGK30",4,"离七集测用","","V3","12"]

-
    name: 企业实名重构页面版法人授权书--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV3_frsign.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType-operatorType-fileKey-mobileNo-certType-version-organizationType:
            - ["正常用例","${ENV(appId3)}","${ENV(creatorOid2)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","******************","武玉华","INDIVIDUAL_CH_IDCARD",2,"","13301053554","ORGANIZATION_USC_CODE","V3","12"]

-
    name: 企业实名重构页面版反向打款new--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV3_reverseRandomAmount2.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType-operatorType-fileKey-certType-certNo-authType-serviceStatusForQuery-version-organizationType:
            - ["正常用例","${ENV(appId3)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋","",2,"${getfileKey()}",11,"91440300MA5ERJGK30",4,"","V3","12"]

-
    name: 企业实名重构页面版对公打款-新增网商银行银企直连--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV3_randomAmount2.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType-operatorType-keyWord-certType-certNo-authType-bizSource-serviceStatusForQuery-fileKey-email-version:
            - ["正常用例","${ENV(appId4)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋","",2,"平安银行",11,"91440300MA5ERJGK30",3,"离七集测用",1,"${getfileKey()}","<EMAIL>","V3"]

#-
#    name: 企业实名法人线下授权书认证--$CaseName
#    testcase: testcases/org/orgIdentity_auth_letter.yml
#    parameters:
#       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType-operatorType-fileKey-mobileNo-certType-version-code-organizationType:
#            - ["91/92/93企业-有限合伙-企三不通过","${ENV(appId3)}","${ENV(creatorOid)}","珠海澳新美企业管理中心（有限合伙）","CRED_ORG_USCC","91440402MA54MKQP45","******************","武玉华","INDIVIDUAL_CH_IDCARD",2,"","13301053554","ORGANIZATION_USC_CODE","V3",30502073,"12"]
#            - ["91/92/93企业-有限合伙-企三通过-法人有多个","${ENV(appId3)}","${ENV(creatorOid)}","esigntest珠海澳新美企业管理中心（有限合伙）","CRED_ORG_USCC","91440402MA54MKQP45","******************","武玉华、测试哈哈","INDIVIDUAL_CH_IDCARD",2,"","13301053554","ORGANIZATION_USC_CODE","V3",0,"12"]
#            - ["11/党政机关-企三通过","${ENV(appId3)}","${ENV(creatorOid)}","esigntest珠海澳新美企业管理中心（有限合伙）","CRED_ORG_USCC","11440402MA54MKQP45","******************","武玉华","INDIVIDUAL_CH_IDCARD",2,"","13301053554","ORGANIZATION_USC_CODE","V3",0,"4"]
#            - ["12事业单位-企三通过","${ENV(appId3)}","${ENV(creatorOid)}","esigntest珠海澳新美企业管理中心（有限合伙）","CRED_ORG_USCC","12440402MA54MKQP45","******************","武玉华","INDIVIDUAL_CH_IDCARD",2,"","13301053554","ORGANIZATION_USC_CODE","V3",0,"3"]
#            - ["其他-企三通过","${ENV(appId3)}","${ENV(creatorOid)}","永康市芝英镇下徐店村股份经济合作社","CRED_ORG_USCC","N2330784MF1116317E","******************","徐伟","INDIVIDUAL_CH_IDCARD",2,"","13301053554","ORGANIZATION_USC_CODE","V3",0,"99"]







#以下功能在20230615迭代时又被去掉
#-
#    name: 企业实名重构页面版企业名称全半角转换（20230330迭代）--$CaseName
#    testcase: testcases/org/orgIdentityUrlOidV3_fullHalfAngle.yml
#    parameters:
#       - CaseName-appId-creatorOid-nameInfoVerity-certNo-legalRepName-VALIDATE:
#            - ["名称中含有中文则把半角都转为全角","${ENV(appId3)}","${ENV(creatorOid)}","阿科玛(苏州）高分子材料有限公司","913205826638113575","蒋宝城","阿科玛（苏州）高分子材料有限公司"]

