name: 发起刷脸认证
request:
    url: /v1/willingness/createFaceAuth
    method: POST
#    headers:
#      Content-Type: application/json
#      X-Tsign-Open-App-Id: ${ENV(appId)}
#      X-Tsign-Open-Auth-Mode: simple
    headers: ${gen_headers2()}
    json:
        appId: ${ENV(appId)}
        accountId: $accountId
        bizType: $bizType
        callbackUrl: "http://www.baidu.com"
        notifyUrl: ${ENV(notifyUrl)}
        faceAuthMode: $faceAuthMode