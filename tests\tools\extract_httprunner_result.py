import re
from bs4 import BeautifulSoup
# import urllib2
import sys
import requests
import json

text = ''
if len(sys.argv) >= 3:
    fd = open(sys.argv[1],'r')
    soup = BeautifulSoup(fd,'html.parser')
    pretty = soup.prettify()

    tables = soup.findAll(name='table')
    tab = tables[0]

    message = '测试通过率: %.2f%%, 用例总数: %d, 成功数: %d, 失败数: %d, 错误数: %d'
    total,success,fail,error = 0,0,0,0
    rate = 0.0
    index = 0

    for tr in tab.findAll('tr'):
        for td in tr.findAll('td'):
            if(index < 5):
                pass
            if(index == 5):
                total = int(td.getText())
            elif(index == 6):
                success = int(td.getText())
            elif(index == 7):
                fail = int(td.getText())
            elif(index == 8):
                error = int(td.getText())
            else:
                pass
            index +=1

    rate = (100 * success) / 1.0 / total
    text = message % (rate,total,success,fail,error)

else:
    print("python extract_httprunner_result.py html_file dingding_token service_name")
    sys.exit(-2)

url = 'https://oapi.dingtalk.com/robot/send?access_token=%s' % sys.argv[2]
body = {}
link = {}
link["text"] = text
link["title"] = "%s接口测试" % sys.argv[3]
link["picUrl"] = ""
link["messageUrl"] = "http://jenkins.test.cn:8080/job/%s/HTML_20Report/" % (sys.argv[3])

body["msgtype"] = "link"
body["link"] = link

headers = {'content-type': "application/json"}


response = requests.post(url, data=json.dumps(body), headers=headers)

print(response.text)

