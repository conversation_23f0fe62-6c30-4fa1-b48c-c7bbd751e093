config:
    name: 获取个人核身认证地址
    base_url: ${ENV(base_url)}
    variables:
      PSN_ACCOUNT: ***********
      PSN_NAME: 测试五五
      PSN_ID_CARD_NUM: ******************  # 使用真实的身份证号或测试用的身份证号

teststeps:
-
    name: 获取个人核身认证地址
    api: api/openApi/psnAuthUrl.yml
    variables:
      - json: {
        "psnAuthConfig": {
          "psnAccount": "${PSN_ACCOUNT}",
          "psnInfo": {
            "psnName": "${PSN_NAME}",
            "psnIDCardNum": "${PSN_ID_CARD_NUM}",
            "psnIDCardType": "CRED_PSN_CH_IDCARD"
          },
          "psnAuthPageConfig": {
            "psnDefaultAuthMode": "PSN_MOBILE3",
            "psnAvailableAuthModes": [ "PSN_BANKCARD4","PSN_MOBILE3","PSN_FACE" ]
          }
        },
        "authorizeConfig": {
          "authorizedScopes": [ "get_psn_identity_info" ]
        },
        "notifyUrl": "http://xx.xx.xx.172:8081/CSTNotify/asyn/notify",
        "clientType": "ALL",
        "redirectConfig": {
          "redirectUrl": "https://www.xxx.cn/"
        }
      }
    extract:
      - authFlowId: content.data.authFlowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.authFlowId, null]
      - ne: [ content.data.authUrl, null ]
      - ne: [ content.data.authShortUrl, null ]

-
    name: 查询认证授权流程详情
    api: api/openApi/auth-flow_authFlowId.yml
    variables:
      authFlowId: ${authFlowId}
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.authFlowId, null]
      - ne: [ content.data.authUrl, null ]


