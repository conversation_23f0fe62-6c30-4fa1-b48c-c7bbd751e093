config:
    name: 其他非对外文档接口

testcases:
-
    name: 企业实名分页查询-$CaseName
    testcase: testcases/org/queryOrganizationList.yml
    parameters:
        - CaseName-appId-certNo-name-flowId-pageIndex-pageSize-status:
            - ["查询一条记录","${ENV(appId)}","91440300MA5ERJGK30","深圳天谷信息科技有限公司","",0,1,0]

-
    name: 通过oid失效流程-$CaseName
    testcase: testcases/org/loseEfficacyByAccountId.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition:
              - ["正常场景","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",True,"",true]

-
    name: 通过flowid失效流程-$CaseName
    testcase: testcases/org/loseEfficacyByFlowId.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition:
              - ["正常场景","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",True,"",true]

-
    name: 获取企业实名认证地址2-失效流程-为下个用例准备数据-$CaseName
    testcase: testcases/org/orgIdentityUrlOid2_loseEfficacyByFlowId.yml
    parameters:
       - CaseName-appId-accountId-agentAccountId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-repeatIdentity-showAgreement-orgUneditableInfo-orgEditableInfo-agentBankCardNo-agentMobile:
            - ["失效flowid","${ENV(appId2)}","${ENV(accountId)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,true,[],[],"",""]

#-
#    name: 临时用例-$CaseName
#    testcase: testcases/org/temp.yml
#    parameters:
#        - CaseName-appId-idNumber-name-legalRepCertType-orgLegalName-orgLegalIdNumber-version-accountId-agentAvailableAuthTypes-agentAccountId:
#            - ["查询一条记录","${ENV(accountId)}","91310104398638847Q","esigntest上海致简科技有限公司","","陈威宇","","v2","${ENV(accountId)}",[],"${ENV(creatorOid)}"]

-
    name: 获取企业实名流程中上传的打款授权书-$CaseName
    testcase: testcases/org/downloadAuthorization.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
            - ["异常场景-> flowid不存在","2583736424692450893",********,"认证流程不存在"]

#该功能开关暂时关闭
#-
#    name: 标准签下创建的企业 校验是否已经存在激活态实名组织（存在则中断实名流程）-$CaseName
#    testcase: testcases/org/infoVerify.yml
#    parameters:
#        - CaseName-appId-flowId-idNumber-name-legalRepCertType-orgLegalName-orgLegalIdNumber-version-VALIDATE1-VALIDATE2:
#            - ["正常场景","${ENV(appId)}","2629061077832831005","9100000021081303RN","esigntest理肤泉四十四","","测试泉十八","","v2",********,"认证失败：该企业已实名，请联系管理员加入该企业"]
