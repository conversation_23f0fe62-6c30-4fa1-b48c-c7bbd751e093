config:
    name: 发起个人刷脸核身认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起个人刷脸核身认证
    api: api/psnAuth/launchFace.yml
    extract:
      - bizToken: content.data.faceToken
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - ne: [content.data.authUrl, null]

-
    name: 新checkToken接口
    api: api/psnAuth/checkBizToken.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.data.completed, false]
