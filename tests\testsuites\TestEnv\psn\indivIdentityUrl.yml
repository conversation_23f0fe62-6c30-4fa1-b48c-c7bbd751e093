config:
    name: 获取个人实名认证地址

testcases:
-
    name: 获取个人实名认证地址2-失效流程-$CaseName
    testcase: testcases/psn/indivIdentityUrl2_loseEfficacyByFlowId.yml
    parameters:
        - CaseName-appId-accountId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-indivEditableInfo-repeatIdentity-showAgreement:
            - ["失效flowid","${ENV(appId2)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],[],true,""]

-
    name: 获取个人实名认证地址-查询应用配置信息-$CaseName
    testcase: testcases/psn/indivIdentityUrl_configAll.yml
    parameters:
        - CaseName-appId-accountId-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-repeatIdentity-VALIDATE1-VALIDATE2-VALIDATE3:
            - ["不指定默认认证类型和页面显示的认证方式","${ENV(appId)}","${ENV(psnOid)}","",[],[],"武玉华","******************","","***********","****************",[],true,8,"INDIVIDUAL_BANKCARD_4_FACTOR",['name', 'certNo']]
            - ["指定默认认证类型为运营商三要素;name不可编辑","${ENV(appId)}","${ENV(psnOid)}","PSN_TELECOM_AUTHCODE",[],[],"武玉华","******************","","***********","****************",["name"],true,8,"INDIVIDUAL_TELECOM_3_FACTOR",['name', 'certNo']]
            - ["指定默认认证类型为刷脸;certNo不可编辑","${ENV(appId)}","${ENV(psnOid)}","PSN_FACEAUTH_BYURL",[],[],"武玉华","******************","","***********","****************",[ "certNo"],true,8,"INDIVIDUAL_FACEAUTH",['name', 'certNo']]
#            - ["指定页面显示的认证方式,只有三要素；mobileNo不可以编辑","${ENV(appId)}","${ENV(psnOid)}","",["PSN_TELECOM_AUTHCODE"],[],"武玉华","******************","","***********","****************",["mobileNo"],true,1,"INDIVIDUAL_TELECOM_3_FACTOR",['name', 'certNo', 'mobileNo']]
            - ["指定页面显示的认证方式,只有三要素和四要素;bankCardNo不可编辑","${ENV(appId)}","${ENV(psnOid)}","",["PSN_TELECOM_AUTHCODE","PSN_BANK4_AUTHCODE"],[],"武玉华","******************","","***********","****************",["bankCardNo"],true,2,"INDIVIDUAL_BANKCARD_4_FACTOR",['name', 'certNo', 'bankCardNo']]
            - ["指定默认认证类型为三要素，但可选方式中没有;全部参数不可编辑","${ENV(appId)}","${ENV(psnOid)}","PSN_TELECOM_AUTHCODE",["PSN_BANK4_AUTHCODE","PSN_FACEAUTH_BYURL"],[],"武玉华","******************","","***********","****************",["name", "certNo", "mobileNo", "bankCardNo"],true,7,"INDIVIDUAL_BANKCARD_4_FACTOR",['name', 'certNo', 'mobileNo', 'bankCardNo']]

-
    name: 获取个人实名认证地址2-失效流程2-$CaseName
    testcase: testcases/psn/indivIdentityUrl2_loseEfficacyByFlowId.yml
    parameters:
        - CaseName-appId-accountId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-indivEditableInfo-repeatIdentity-showAgreement:
            - ["失效flowid","${ENV(appId2)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],[],true,""]

-
    name: 获取个人实名认证地址-$CaseName
    testcase: testcases/psn/indivIdentityUrl.yml
    parameters:
        - CaseName-appId-accountId-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-repeatIdentity:
            - ["证件类型为身份证","${ENV(appId)}","f3161ef149744366b2d5a9e4fa9588bc","",[],[],"武玉华","******************","INDIVIDUAL_CH_IDCARD","***********","****************",[],true]
            - ["证件类型为台胞证","${ENV(appId)}","0516dc1ceca245db87b23e7980e52589","",[],[],"田欣慈","********","INDIVIDUAL_CH_TWCARD","***********","6222620910051004855",[],true]
            - ["证件类型为护照","${ENV(appId)}","f86e8329cfc34693bd366dc2254b5cf8","",[],[],"韩艾珊","H10561683","INDIVIDUAL_PASSPORT","***********","622908393301650316",[],true]
            - ["证件类型为澳门通行证","${ENV(appId)}","bf2e242a80434ff79006ceb3dd71fe47","",[],[],"余洁婷","M0215994202","INDIVIDUAL_CH_HONGKONG_MACAO","***********","6214620321001228346",[],true]
            - ["英文名字中间支持空格","${ENV(appId)}","1b7fee293d6442a58b97aece2a708dd3","",[],[],"FAN HON SING","*********","INDIVIDUAL_PASSPORT","***********","****************",[],true]
            - ["中英文名字中间支持半角逗号","${ENV(appId)}","1b7fee293d6442a58b97aece2a708dd3","",[],[],"FAN,","*********","INDIVIDUAL_PASSPORT","***********","****************",[],true]

-
    name: 获取个人实名认证地址2-失效流程3-$CaseName
    testcase: testcases/psn/indivIdentityUrl2_loseEfficacyByFlowId.yml
    parameters:
        - CaseName-appId-accountId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-indivEditableInfo-repeatIdentity-showAgreement:
            - ["失效flowid","${ENV(appId2)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],[],true,""]

-
    name: 获取个人实名认证地址2-查询应用配置信息showAgreement-$CaseName
    testcase: testcases/psn/indivIdentityUrl2_configAll.yml
    parameters:
        - CaseName-appId-accountId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-indivEditableInfo-repeatIdentity-showAgreement-VALIDATE1:
            - ["appid展示协议页面,参数配置没有传参","${ENV(appId2)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],[],true,"",true]
            - ["appid展示协议页面,参数配置true","${ENV(appId2)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],[],true,true,true]
            - ["appid展示协议页面,参数配置false","${ENV(appId2)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],[],true,false,false]
            - ["appid不展示协议页面,参数配置true","${ENV(appId)}","${ENV(psnOid2)}","武玉华","******************","","***********","****************",[],[],true,true,true]
            - ["appid不展示协议页面,参数配置false","${ENV(appId)}","${ENV(psnOid2)}","武玉华","******************","","***********","****************",[],[],true,false,false]
            - ["appid不展示协议页面,参数配置没有传参","${ENV(appId)}","${ENV(psnOid2)}","武玉华","******************","","***********","****************",[],[],true,"",false]

-
    name: 获取个人实名认证地址2-失效流程4-$CaseName
    testcase: testcases/psn/indivIdentityUrl2_loseEfficacyByFlowId.yml
    parameters:
        - CaseName-appId-accountId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-indivEditableInfo-repeatIdentity-showAgreement:
            - ["失效flowid","${ENV(appId2)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],[],true,""]

-
    name: 获取个人实名认证地址2-查询应用配置信息indivEditableInfo-$CaseName
    testcase: testcases/psn/indivIdentityUrl2_configAll_indivEditableInfo.yml
    parameters:
        - CaseName-appId-accountId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-indivEditableInfo-repeatIdentity-showAgreement-VALIDATE1-VALIDATE2:
            - ["已实名oid,设置姓名、证件号可编辑","${ENV(appId)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],["name","certNo"],true,true,1,['certNo']]
            - ["已实名oid,不设置任何可编辑参数","${ENV(appId)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],[],true,true,2,['name', 'certNo']]
            - ["已实名oid,设置所有参数不可编辑","${ENV(appId)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",["name","certNo","bankCardNo","mobileNo"],[],true,false,4,['name', 'certNo', 'mobileNo', 'bankCardNo']]
            - ["已实名oid,设置所有参数可编辑","${ENV(appId)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",[],["name","certNo","bankCardNo","mobileNo"],true,false,1,['certNo']]
            - ["未实名oid,不设置任何可编辑参数","${ENV(appId)}","${ENV(psnOid2)}","武玉华","******************","","***********","****************",[],[],true,true,2,['name', 'certNo']]
            - ["未实名oid,设置所有参数不可编辑","${ENV(appId)}","${ENV(psnOid2)}","武玉华","******************","","***********","****************",["name","certNo","bankCardNo","mobileNo"],[],true,false,4,['name', 'certNo', 'mobileNo', 'bankCardNo']]
            - ["未实名oid,设置所有参数可编辑","${ENV(appId)}","${ENV(psnOid2)}","武玉华","******************","","***********","****************",[],["name","certNo","bankCardNo","mobileNo"],true,false,1,['certNo']]

-
    name: 获取个人实名认证地址-********迭代1-$CaseName
    testcase: testcases/psn/indivIdentityUrl_1021case1.yml
    parameters:
        - CaseName-name-idType-idNumber-mobile-email-cardNo-name2-certType2-certNo2-mobileNo2-bankCardNo2:
            - ["无GID，oid中无任何信息，实名传参所有信息","","","","","","","武玉华","INDIVIDUAL_CH_IDCARD","******************","***********","****************"]
            - ["无GID，oid中有姓名、手机号、银行卡号信息，实名传参更改姓名、手机号、银行卡号","武玉华","","","***********","","****************","武玉","INDIVIDUAL_CH_IDCARD","******************","***********","****************"]
            - ["无GID，oid中有证件类型、姓名、手机号、银行卡号信息，实名传参修改证件类型","武玉华","INDIVIDUAL_CH_IDCARD","","***********","","****************","","INDIVIDUAL_CH_TWCARD","","",""]
            - ["有GID，oid中有证件类型和证件号，实名传参姓名、手机号、银行卡号","","CRED_PSN_CH_IDCARD","******************","","","","武玉华","","","***********","****************"]
            - ["有GID，oid中有所有信息，实名传参更改姓名、手机号、银行卡号","武玉华","CRED_PSN_CH_IDCARD","******************","***********","","****************","武玉","","","***********","****************"]

-
    name: 获取个人实名认证地址-********迭代2-$CaseName
    testcase: testcases/psn/indivIdentityUrl_1021case2.yml
    parameters:
        - CaseName-name-idType-idNumber-mobile-email-cardNo-name2-certType2-certNo2-mobileNo2-bankCardNo2-VALIDATE1-VALIDATE2:
            - ["有GID，oid中有所有信息，实名传参更改证件类型，其他不传","武玉华","CRED_PSN_CH_IDCARD","******************","***********","","****************","","INDIVIDUAL_CH_TWCARD","","","",0,"成功"]
            - ["有GID，oid中有证件类型和证件号，实名传参更改证件号","","CRED_PSN_CH_IDCARD","******************","","","","","","******************","","",********,"参数错误：输入证件号与账号不匹配"]
            - ["有GID，oid中有所有信息，实名传参更改证件类型和证件号，其他传参不修改","武玉华","CRED_PSN_CH_IDCARD","******************","***********","","****************","武玉华","INDIVIDUAL_CH_TWCARD","******************","***********","****************",********,"参数错误：输入证件号与账号不匹配"]
            - ["有GID，oid中有证件号（会默认身份证），实名传参修改证件号","","","******************","","","","","","******************","","",********,"参数错误：输入证件号与账号不匹配"]


-
    name: 获取个人实名认证地址2-发起多因子认证
    testcase: testcases/psn/indivIdentity_MULTIPLE_AUTH_FACE_TELECOM.yml
    parameters:
        - appId-accountId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-indivEditableInfo-repeatIdentity-showAgreement-VALIDATE1-VALIDATE2:
              - [ "**********","${ENV(psnOid)}","武玉华","******************","","***********","****************",[ ],[ "name","certNo" ],true,true,1,[ 'certNo' ] ]
