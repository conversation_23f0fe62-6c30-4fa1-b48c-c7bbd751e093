config:
    name: 腾讯云sdk刷脸认证
    base_url: ${ENV(base_url)}
    variables:
      appId: "7876668308"

teststeps:
-
    name: 发起个人刷脸核身认证
    api: api/psnAuth/launchFace1.yml
    variables:
      name: "刘志华"
      idNo: "362430199311156921"
      faceauthMode: "TENCENT"
      faceInterfaceType: "SDK"    #SDK
      faceSdkVersion: "BASIC"   #基础版：BASIC   增强版：PLUS
      callbackUrl: "http://www.baidu.com"
    extract:
      - flowId: content.data.flowId
      - faceToken: content.data.faceToken
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - ne: [content.data.faceToken, null]

-
    name: 唤起刷脸
    api: api/psnAuth/wakeupFace.yml
    variables:
      faceAuthCode: $faceToken
      faceAuthMode: 'APP_FACE_SDK'
      esignAppSdkVersion: 2.0.0
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.faceAuthMode, "FACE_TECENT_SDK_BASIC"]

-   name: 查询个人刷脸状态
    api: api/psnAuth/queryFaceStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "ING"]
      - eq: [content.data.message, "刷脸认证未完成"]

-   name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [ content.data.flowStatus, "ING" ]



