config:
    name: 发起企业实名认证4要素校验

testcases:
-
    name: 发起企业实名认证4要素校验-法人授权书认证-$CaseName
    testcase: testcases/org/fourFactorsOid_legalRepSign.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-mobileNo-legalRepIdNo-VALIDATE1:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",True,"",true,"***********","330702198409120432","INDIVIDUAL_CH_IDCARD"]
# 暂时注释，模板不支持这么长的法人名字，发起报错，优化后放开，先用下面mock的数据代替  - ["企业信息都正确(外籍法人-护照)","${ENV(appId)}","${ENV(creatorOid)}","百达晶心精密机械（无锡）有限公司","CRED_ORG_USCC","91320214733305872J","E6409934C","黄亮茳(WEE LIANG KIANG)",True,"INDIVIDUAL_PASSPORT",true,"***********","*********","INDIVIDUAL_PASSPORT"]
            - ["企业信息都正确(外籍法人-护照)","${ENV(appId)}","${ENV(creatorOid)}","百达晶心精密机械（无锡）有限公司","CRED_ORG_USCC","91320214733305872J","E6409934C","黄亮茳（WEE LIANG KIANG）",True,"INDIVIDUAL_PASSPORT",true,"***********","*********","INDIVIDUAL_PASSPORT"]
            - ["企业信息都正确(外籍法人-港澳通行证)","${ENV(appId)}","${ENV(creatorOid)}","深圳市锐玲商贸有限公司","CRED_ORG_USCC","91440300060276033X","H60159767","高英娣",True,"INDIVIDUAL_CH_HONGKONG_MACAO",true,"***********","H60159767","INDIVIDUAL_CH_HONGKONG_MACAO"]
            - ["企业信息都正确(法人证件类型传身份证,且企业名称中支持特殊字符)","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","110108196710262291","何一兵",True,"INDIVIDUAL_CH_IDCARD",true,"***********","110108196710262291","INDIVIDUAL_CH_IDCARD"]

-
    name: 发起企业实名认证4要素校验-随机金额打款认证-$CaseName
    testcase: testcases/org/fourFactorsOid_transferRandomAmount.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-cardNo-subbranch-amount-VALIDATE1-VALIDATE2-bank:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",True,"",true,"**************","平安银行杭州高新支行","0.01","INDIVIDUAL_CH_IDCARD","CRED_PSN_CH_IDCARD","平安银行"]
            - ["企业信息都正确(外籍法人-护照)","${ENV(appId)}","${ENV(creatorOid)}","百达晶心精密机械（无锡）有限公司","CRED_ORG_USCC","91320214733305872J","E6409934C","黄亮茳(WEE LIANG KIANG)",True,"INDIVIDUAL_PASSPORT",true,"**************","平安银行杭州高新支行","0.01","INDIVIDUAL_PASSPORT","CRED_PSN_PASSPORT","平安银行"]
            - ["企业信息都正确(外籍法人-港澳通行证)","${ENV(appId)}","${ENV(creatorOid)}","深圳市锐玲商贸有限公司","CRED_ORG_USCC","91440300060276033X","H60159767","高英娣",True,"INDIVIDUAL_CH_HONGKONG_MACAO",true,"**************","平安银行杭州高新支行","0.01","INDIVIDUAL_CH_HONGKONG_MACAO","CRED_PSN_PASSPORT","平安银行"]
            - ["企业信息都正确(法人证件类型传身份证，企业名称中支持特殊字符)","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","110108196710262291","何一兵",True,"INDIVIDUAL_CH_IDCARD",true,"**************","平安银行杭州高新支行","0.01","INDIVIDUAL_CH_IDCARD","CRED_PSN_CH_IDCARD","平安银行"]

-
    name: 发起企业实名认证4要素校验-反向打款认证-$CaseName
    testcase: testcases/org/fourFactorsOid_reversePayment.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-VALIDATE1:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",True,"",true,"INDIVIDUAL_CH_IDCARD"]
            - ["企业信息都正确(外籍法人-护照)","${ENV(appId)}","${ENV(creatorOid)}","百达晶心精密机械（无锡）有限公司","CRED_ORG_USCC","91320214733305872J","E6409934C","黄亮茳（WEE LIANG KIANG）",True,"INDIVIDUAL_PASSPORT",true,"INDIVIDUAL_PASSPORT"]
            - ["企业信息都正确(外籍法人-港澳通行证)","${ENV(appId)}","${ENV(creatorOid)}","深圳市锐玲商贸有限公司","CRED_ORG_USCC","91440300060276033X","H60159767","高英娣",True,"INDIVIDUAL_CH_HONGKONG_MACAO",true,"INDIVIDUAL_CH_HONGKONG_MACAO"]
            - ["企业信息都正确(法人证件类型传身份证，企业名称中支持特殊字符)","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","110108196710262291","何一兵",True,"INDIVIDUAL_CH_IDCARD",true,"INDIVIDUAL_CH_IDCARD"]

-
    name: 发起企业实名认证4要素校验-企业支付宝一键实名认证-$CaseName
    testcase: testcases/org/fourFactorsOid_alipayOneclickConfirm.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-device-token-authCodeAlipay-orgCode-VALIDATE1-VALIDATE2:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","杭州易签宝网络科技有限公司","CRED_ORG_USCC","91330108MA2GKFYB1X","330821198202051817","程亮",True,"",true,"PC","xingchenAccessTokenAO001","xingchenAuthcodeZbf001","91330108MA2GKFYB1X","INDIVIDUAL_CH_IDCARD","CRED_PSN_CH_IDCARD"]
#            - ["企业信息都正确(外籍法人-护照)","${ENV(appId)}","${ENV(creatorOid)}","百达晶心精密机械（无锡）有限公司","CRED_ORG_USCC","91320214733305872J","E6409934C","黄亮茳（WEE LIANG KIANG）",True,"INDIVIDUAL_PASSPORT",true,"H5","xingchenAccessTokenAO006","xingchenAuthcodeZbf006","91320214733305872J","INDIVIDUAL_PASSPORT","CRED_PSN_PASSPORT"]
            - ["企业信息都正确(特殊字符都支持，全半角括号兼容)","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","330702198409120432","张晋",True,"INDIVIDUAL_CH_IDCARD",true,"PC","xingchenAccessTokenAO005","xingchenAuthcodeZbf005","91330108MA2G042101","INDIVIDUAL_CH_IDCARD","CRED_PSN_CH_IDCARD"]

-
    name: 发起企业实名认证4要素校验-法定代表人认证-$CaseName
    testcase: testcases/org/fourFactorsOid_legalRepAuth.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-mobileNo-legalRepIdNo-VALIDATE1-VALIDATE2:
            - ["企业信息都正确","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","******************","武玉华",True,"INDIVIDUAL_CH_IDCARD",true,"***********","******************","INDIVIDUAL_CH_IDCARD","CRED_PSN_CH_IDCARD"]

-
    name: 发起企业实名认证4要素校验-法定代表人认证-关闭法人认证-$CaseName
    testcase: testcases/org/fourFactorsOid_legalRepAuth_ul.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-mobileNo-legalRepIdNo-VALIDATE1:
            - ["企业信息都正确","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","******************","武玉华",False,"INDIVIDUAL_CH_IDCARD",true,"***********","******************","INDIVIDUAL_CH_IDCARD"]

-
    name: 发起企业实名认证4要素校验-人工审核-$CaseName
    testcase: testcases/org/fourFactorsOid_manual.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-email-serviceTypeName:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",False,"INDIVIDUAL_CH_IDCARD",true,"<EMAIL>","ORG_ARTIFICIAL"]
