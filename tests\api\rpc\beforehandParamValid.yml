name: RPC实名前的参数校验
request:
    url: /beforehandParamValid/request
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        accountId: $accountId
        objectType: $objectType
        indivReq:
          repeatIdentity: true
          indivInfo:
            bankCardNo: $indivBankCardNo
            certNo: $indivCertNo
            certType: $indivCertType
            mobileNo: $indivMobileNo
            name: $indivName
        orgReq:
          repeatIdentity: true
          agentAccountId: $agentAccountId
          orgEntity:
            name: $orgName
            certNo: $orgCertNo
            legalRepCertNo: $orgLegalRepCertNo
            legalRepCertType: $orgLegalRepCertType
            legalRepName: $orgLegalRepName
            agentName: $agentName
            agentIdNo: $agentIdNo
