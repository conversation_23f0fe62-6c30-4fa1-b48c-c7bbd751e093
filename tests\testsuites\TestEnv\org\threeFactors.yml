config:
    name: 发起企业核身认证3要素校验
#三要素不支持外籍法人
testcases:
-
    name: 发起企业核身认证3要素校验-法人授权书认证-$CaseName
    testcase: testcases/org/threeFactors_legalRepSign.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-legalRepIdNo-agentName-agentIdNo-mobileNo:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","武玉华","******************","***********"]
            - ["企业信息都正确(15位工商注册码)","${ENV(appId)}","杭州天谷信息科技有限公司","***************","金宏洲","330722197904110013","武玉华","******************","***********"]
            - ["企业信息都正确(企业名称中支持特殊字符)","${ENV(appId)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","何一兵","110108196710262291","武玉华","******************","***********"]

-
    name: 发起企业核身认证3要素校验-随机金额打款认证-$CaseName
    testcase: testcases/org/threeFactors_transferRandomAmount.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-cardNo-subbranch-amount-bank:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲","**************","************","0.01","平安银行"]
            - ["企业信息都正确(15位工商注册码)","${ENV(appId)}","杭州天谷信息科技有限公司","***************","金宏洲","**************","************","0.01","平安银行"]
            - ["企业信息都正确(企业名称中支持特殊字符)","${ENV(appId)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","何一兵","**************","************","0.01","平安银行"]

-
    name: 发起企业核身认证3要素校验-反向打款认证-$CaseName
    testcase: testcases/org/threeFactors_reversePayment.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲"]
            - ["企业信息都正确(15位工商注册码)","${ENV(appId)}","杭州天谷信息科技有限公司","***************","金宏洲"]
            - ["企业信息都正确(企业名称中支持特殊字符)","${ENV(appId)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","何一兵"]

-
    name: 发起企业核身认证3要素校验-企业支付宝一键实名认证-$CaseName
    testcase: testcases/org/threeFactors_alipayOneclickConfirm.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-device-token-authCodeAlipay:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州易签宝网络科技有限公司","91330108MA2GKFYB1X","程亮","PC","xingchenAccessTokenAO001","xingchenAuthcodeZbf001"]
            - ["企业信息都正确(特殊字符都支持，全半角括号兼容)","${ENV(appId)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","张晋","PC","xingchenAccessTokenAO005","xingchenAuthcodeZbf005"]

#-
#    name: 发起企业核身认证3要素校验-新增场景类型scenesMode-$CaseName
#    testcase: testcases/org/threeFactors_addScenesMode.yml
#    parameters:
#        - CaseName-appId-name-orgCode-legalRepName-agentFlowId-scenesMode-VALIDATE1-VALIDATE2:
#            - ["企业信息都正确-agentId为空","${ENV(appId)}","杭州易签宝网络科技有限公司","91330108MA2GKFYB1X","程亮",'',1,0,'成功']
#            - [ "企业信息都正确-agentId为空","${ENV(appId)}","杭州易签宝网络科技有限公司","91330108MA2GKFYB1X","程亮",'',2,30500101,'参数错误：办理人认证流程ID不能为空' ]
#

