config:
    name: 发起企业实名认证4要素校验-法人认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 发起企业实名认证4要素校验
    api: api/orgAuth/fourFactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.subFlows.0.subFlowType, "ORGANIZATION_INFO4_AUTH"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "LEGAL_REP_AUTH"]
      - eq: [content.data.organInfo.name, $name]
      - eq: [content.data.organInfo.legalRepCertType, $VALIDATE1]

-
    name: 查询存证数据
    api: api/public/getEvidenceData.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.0.extDataMap.companyName, $name]
      - eq: [content.data.0.extDataMap.codeUsc, $idNumber]
      - eq: [content.data.0.extDataMap.legalPersonIdno, $orgLegalIdNumber]
      - eq: [content.data.0.extDataMap.legalPersonName, $orgLegalName]

-
    name: 查询是否同意数字协议证书
    api: api/orgAuth/api_checkUserAgreement.yml
    variables:
          authSourceId: $flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]