config:
    name: 获取组织机构实名认证地址V2-随机打款认证
    base_url: ${ENV(base_url_rpc)}

teststeps:
#创建企业oid，发起实名；经办人意愿-邮箱意愿；企业发起正向打款-打款成功
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserIdV1.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlV2.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.url, null]

-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版获取企业信息
    api: api/orgAuth/queryOrganizationInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.agentName, null]
      - eq: [content.data.certNo, $idNumber]
      - eq: [content.data.legalRepCertNo, $orgLegalIdNumber]
      - eq: [content.data.legalRepName, $orgLegalName]
      - eq: [content.data.name, $name]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.display, null]

-
    name: 查询企业实名流程进度1
    api: api/orgAuth/getOrgProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.ifInfoAuthSuccessful, false]
      - eq: [content.data.status, "INIT"]
      - eq: [content.data.type, "ORGANIZATION_INFO_AUTH"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 企业经办人认证状态查询
    api: api/orgAuth/getAgentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.authorizedInOrgFlow, False]
      - eq: [content.data.authorizedInV21, True]
      - eq: [content.data.authorisedInUserCenter, True]
      - eq: [content.data.status, "WILLINGNESS_REQUIRED"]

-
    name: 获取经办人意愿方式
    api: api/psnAuth/getWillingnessType.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.willAuthType, null]

-
    name: 查询意愿邮箱
    api: api/psnAuth/queryWillingnessEmail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - contains: [content.data.email, "@"]

-
    name: 发起意愿邮箱验证码认证
    api: api/psnAuth/sendWillingnessEmailCodeauth.yml
    extract:
      - psnFlowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验意愿邮箱验证码认证-错误验证码
    api: api/psnAuth/verifyWillingnessEmailCodeauth.yml
    variables:
      - authCode: "123457"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 30503002]
      - eq: [content.message, "验证码校验失败"]

-
    name: 校验意愿邮箱验证码认证-正确验证码
    api: api/psnAuth/verifyWillingnessEmailCodeauth.yml
    variables:
      - authCode: "123456"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 企业名称模糊匹配
    api: api/orgAuth/queryInformation.yml
    variables:
      - enterpriseName: $name
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.0.name, $name]
      - eq: [content.data.0.legalName, $orgLegalName]

-
    name: 页面版企业信息比对接口
    api: api/orgAuth/infoVerify.yml
    variables:
      - version: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]
      - eq: [content.data.operatorType, 2]

-
    name: 页面版查询反向打款进度
    api: api/orgAuth/reversePaymentProcessWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]
      - eq: [content.data.message, "子账户未创建"]

-
    name: 页面版查询随机金额业务进度
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]

-
    name: 支行查询
    api: api/orgAuth/querySubbranch.yml
    variables:
      - keyWord: "平安银行"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.list.0.bankName, null]

-
    name: 页面版随机金额对公打款
    api: api/orgAuth/transferRandomAmountWeb.yml
    variables:
      - cardNo: "*********"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版查询随机金额业务进度2
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "PAID"]
      - eq: [content.data.message, "打款申请成功"]

-
    name: 页面版随机金额校验
    api: api/orgAuth/verifyRandomAmountWeb.yml
    variables:
      - cash: "0.01"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 查询企业实名流程进度2
    api: api/orgAuth/getOrgProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.ifInfoAuthSuccessful, true]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.type, "ORGANIZATION_TRANSFER"]