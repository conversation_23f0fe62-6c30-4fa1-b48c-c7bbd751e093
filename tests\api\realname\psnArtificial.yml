name: 个人实名上传授权书
variables:
  name: ""
  idno: ""
  authMaterialType: ""
  certFront: ""
  authMaterialContent: ""

request:
  url: /psnArtificial/model
  method: POST
  json:
    version: "1.0.0"
    projectId: "1111563841"
    timestamp: 1747208290092
    name: $name
    idno: $idno
    certType: 1
    certFront: $certFront
    bizContextBean:
      bizCode: "bizCode"
      bizScene: "bizScene"
      authMaterialBeans:
        - authMaterialDesc: "授权书1"
          authMaterialType: $authMaterialType
          authMaterialContent: $certFront
          authMaterialContentFormat: "PNG"
        - authMaterialDesc: "授权书2"
          authMaterialType: $authMaterialType
          authMaterialContent: $authMaterialContent
          authMaterialContentFormat: "PDF"
  headers:
    X-Tsign-Open-App-Id: ${ENV(7488appId)}
    Content-Type: application/json
