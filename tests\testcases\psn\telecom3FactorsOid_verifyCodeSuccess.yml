config:
    name: 发起运营商3要素实名认证，校验短信验证码
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起运营商3要素实名认证
    api: api/psnAuth/telecom3FactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验短信验证码-正确验证码
    api: api/psnAuth/telecom3VerifyCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.subFlows.0.subFlowType, "INDIVIDUAL_TELECOM_3_FACTOR"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.authType, "INDIVIDUAL_TELECOM_3_FACTOR"]
      - eq: [content.data.indivInfo.name, $name]

-
    name: 查询存证数据
    api: api/public/getEvidenceData.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.0.extDataMap.phone, $mobileNo]
      - ne: [content.data.0.extDataMap.idCardNo, null]
      - eq: [content.data.0.extDataMap.name, $name]
      - eq: [content.data.0.realNameStep, "PERSON_3FACTOR_CHECK"]
      - eq: [content.data.0.realNameWay, "PERSON_3FACTOR_AUTH"]
      - eq: [content.data.1.extDataMap.smsSend, "123456"]
      - eq: [content.data.1.extDataMap.phoneNo, $mobileNo]
      - eq: [content.data.2.extDataMap.smsFilled, "123456"]
      - eq: [content.data.2.realNameStep, "FILL_VERIFY_CODE"]
      - eq: [content.data.2.realNameWay, "PERSON_3FACTOR_AUTH"]