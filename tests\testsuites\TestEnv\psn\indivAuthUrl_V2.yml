config:
    name: 获取个人核身认证地址-页面版接口完成个人实名
testcases:
-
    name: 获取个人核身认证地址-通过运营商三要素完成实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivAuthUrl_telecom3_V2.yml
    parameters:
        - CaseName-appId-name-certNo-certType-mobileNo-bankCardNo-authcode:
            - ["正常场景","${ENV(appId)}","许华建","342622198905262396","","***********","","123456"]
            - ["mock场景","${ENV(appId)}","测试许华建","342622198905262396","","***********","","123456"]

-
    name: 获取个人核身认证地址-通过运营商三要素完成实名（页面版接口）-$CaseName
    testcase: testcases/psn/telecom3FactorInfoAuth.yml
    parameters:
        - CaseName-appId-name-certNo-certType-mobileNo-bankCardNo:
            - ["异常场景","${ENV(appId)}","许华建哈","342622198905262396","","***********",""]

-
    name: 获取个人核身认证地址-通过银行卡四要素完成实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivAuthUrl_bank4_V2.yml
    parameters:
        - CaseName-appId-name-certNo-certType-mobileNo-bankCardNo-authcode:
            - ["正常场景-身份证号","${ENV(appId)}","测试许华建","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************","123456"]
            - ["正常场景mock-港澳通行证","${ENV(appId)}","测试许华建","M08596583","INDIVIDUAL_CH_HONGKONG_MACAO","***********","****************","123456"]
            - ["正常场景mock-台胞证","${ENV(appId)}","测试许华建","********","INDIVIDUAL_CH_TWCARD","***********","****************","123456"]
            - ["正常场景mock-护照","${ENV(appId)}","测试许华建","H10561683","INDIVIDUAL_PASSPORT","***********","****************","123456"]

-
    name: 获取个人核身认证地址-二要素后指定刷脸实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivAuthUrl_faceauth_identity2_V2.yml
    parameters:
        - CaseName-appId-name-certNo-certType-mobileNo-bankCardNo-faceAuthMode-subjectType-isPC:
            - ["正常场景-腾讯云刷脸","${ENV(appId)}","许华建","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************",1,1,ture]

-
    name: 获取个人核身认证地址-三要素后指定刷脸实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivAuthUrl_faceauth_identity3_V2.yml
    parameters:
        - CaseName-appId-name-certNo-certType-mobileNo-bankCardNo-faceAuthMode-subjectType-isPC:
            - ["正常场景-支付宝刷脸","${ENV(appId)}","许华建","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************",2,1,ture]

-
    name: 获取个人核身认证地址-指定刷脸实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivAuthUrl_faceauth_identity3SendCode_V2.yml
    parameters:
        - CaseName-appId-name-certNo-certType-mobileNo-bankCardNo-faceAuthMode-subjectType-isPC:
            - ["正常场景-自研刷脸","${ENV(appId)}","许华建","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************",10,1,ture]
