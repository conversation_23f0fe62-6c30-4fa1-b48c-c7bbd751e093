config:
    name: 发起企业实名认证4要素校验-法人授权书认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserIdV1.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 发起企业实名认证4要素校验
    api: api/orgAuth/fourFactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 发起授权签署实名认证
    api: api/orgAuth/legalRepSignAuth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 获取签署授权链接
    api: api/orgAuth/signUrl.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.signUrl, null]

-
    name: 查询授权书签署状态
    api: api/orgAuth/legalRepSignResult.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SIGNING"]
      - eq: [content.data.message, "签署中"]
    teardown_hooks:
      - ${teardown_hook_sleep_n_secs(3)}
-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.subFlows.0.subFlowType, "LEGAL_REP_SIGN_AUTHORIZE"]
      - eq: [content.data.subFlows.0.status, "ING"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "LEGAL_REP_SIGN_AUTHORIZE"]
      - eq: [content.data.organInfo.name, $name]
      - eq: [content.data.organInfo.legalRepCertType, $VALIDATE1]


-
    name: 根据flowId查询其认证子service的详情 对内服务GET方式
    api: api/public/queryFlowServiceInnerLineGet.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "进行中"]
      - eq: [content.data.objectType, "企业"]
      - eq: [content.data.subFlows.0.subFlowType, "企业法定代表人签署授权认证"]

-
    name: 根据flowId查询其认证子service的详情 对内服务Post方式
    api: api/public/queryFlowServiceInnerLinePost.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "进行中"]
      - eq: [content.data.objectType, "企业"]
      - eq: [content.data.subFlows.0.subFlowType, "企业法定代表人签署授权认证"]

-
    name: 根据用户OID查询正在进行中的一次认证记录
    api: api/public/processing.yml
    variables:
      - agentOid: $creatorOid
      - oid: $accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]
      - eq: [content.data.status, 1]
