name: 获取组织机构实名认证地址api-********迭代之后新增参数，启用新接口
request:
    url: /v2/identity/auth/web/$accountId/orgIdentityUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: $appId
        X-Tsign-Open-Auth-Mode: simple
    json:
      agentAccountId: $agentAccountId
      contextInfo:
        contextId: "测试"
        notifyUrl: ${ENV(notifyUrl)}
        origin: "BROWSER"
        redirectUrl: "http://baidu.com"
        showResultPage: "true"
        showAgreement: $showAgreement
      orgEntity:
        certNo: $certNo
        organizationType: $organizationType
        name: $name
        legalRepCertType: $legalRepCertType
        legalRepCertNo: $legalRepCertNo
        legalRepName: $legalRepName
        agentBankCardNo: $agentBankCardNo
        agentMobile: $agentMobile
      repeatIdentity: $repeatIdentity
      configParams:
        orgUneditableInfo: $orgUneditableInfo
        orgEditableInfo: $orgEditableInfo
        billIsolationCode: "XESDEWXSAQAZ1"
