config:
    name: 发起企业核身认证3要素校验
#三要素不支持外籍法人
testcases:
-
    name: 发起企业核身认证3要素校验-法人授权书认证-$CaseName
    testcase: testcases/org/threeFactors_legalRepSign.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-legalRepIdNo-agentName-agentIdNo-mobileNo:
            - ["企业信息都正确(常规企业)","杭州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","武玉华","******************","***********"]

-
    name: 发起企业核身认证3要素校验-随机金额打款认证-$CaseName
    testcase: testcases/org/threeFactors_transferRandomAmount.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-cardNo-subbranch-amount-bank:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲","**************","************","0.01","平安银行"]

-
    name: 发起企业核身认证3要素校验-反向打款认证-$CaseName
    testcase: testcases/org/threeFactors_reversePayment.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲"]

-
    name: 发起企业核身认证3要素校验-企业支付宝一键实名认证-$CaseName
    testcase: testcases/org/threeFactors_alipayOneclickConfirm.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-device-token-authCodeAlipay:
            - ["企业信息都正确(常规企业)","杭州易签宝网络科技有限公司","91330108MA2GKFYB1X","程亮","PC","xingchenAccessTokenAO001","xingchenAuthcodeZbf001"]
