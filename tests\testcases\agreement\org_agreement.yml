config:
    name: 企业实名协议
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 获取协议配置列表
    api: api/agreement/userAgreementList.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.userAgreementList, null]
    extract:
      #e签宝CA
      userAgreementId: content.data.userAgreementList.2.userAgreementId
      userAgreementTitle: content.data.userAgreementList.2.userAgreementTitle
      objectType: content.data.userAgreementList.2.objectType

-
    name: 获取协议详情
    api: api/agreement/userAgreementDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.userAgreementId, null]
      - eq: [content.data.userAgreementTitle, "数字证书服务协议（机构）"]

-
    name: 同意协议
    api: api/agreement/userAgreementAgree.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

