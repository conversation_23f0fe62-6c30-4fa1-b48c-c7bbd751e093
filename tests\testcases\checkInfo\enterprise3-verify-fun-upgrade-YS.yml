- config:
    name: 开启高质量供应商二次比对-企业三要素比对-第一次调用供应商为有数
    base_url: ${ENV(base_url)}
    variables:
        operator: "夙瑶"
        Cookie: "access_token=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; TSIGN.SESSION.SANDBOX=9145533d-f7c8-498e-abdf-287105c93605; TSIGN.SESSION.COMMON=a9f448ee-0a6c-43f5-b6db-1f1edccfe392; redirect_referer=aHR0cDovL3N1cHBvcnQtbWljcm9mZS1tYWluZnJvbnQtdmVyaWZ5LWZ1bi11cGdyYWRlLnByb2plY3RrOHMudHNpZ24uY24vbWljcm9mZS9yZWFsbmFtZS9wcm92aWRlcg==; test_access_token=***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"



- test:
    name: 配置高质量供应商为蚂蚁
    api: api/public/providerManagerSave.yml
    variables:
        name: "MAYI"
        url: "https://prodapigw.cloud.alipay.com"
        account: "LTAI9kGTlVr1a7Wx"
        signKey: null
        weight: "1"
        qualityTag: "HIGH"
        desc: "蚂蚁"
        id: 80
        type: 1
        errCode: "{\"PROVIDER_SERVICE_CONNECTION_CLOSED\":\"placeholder\",\"PROVIDER_SERVICE_TIMEOUT\":\"placeholder\"}"
        extendJson: "{\"manager\":{},\"client\":{},\"select\":{},\"retry\":{}}"
    validate:
        - eq: [content.code, 0]
        - contains: [content.message, 成功]
    json: {
     "id": $id,
     "name": $name,
     "type": $type,
     "desc": $desc,
     "url": $url,
     "account": $account,
     "signKey": $signKey,
     "weight": $weight,
     "operator": $operator,
     "qualityTag": $qualityTag,
     "errCode": $errCode,
     "extendJson": $extendJson
    }

- test:
    name: 配置供应商有数的权重为999
    api: api/public/providerManagerSave.yml
    variables:
        name: "YOU_SHU"
        url: "https://open.yscredit.com/api/request"
        account: "A8251114"
        signKey: null
        weight: "999"
        qualityTag: "NORMAL"
        desc: "有数"
        id: 751
        type: 1
        errCode: null,
        extendJson: "{\"manager\":{},\"client\":{},\"select\":{},\"retry\":{}}"
    validate:
        - eq: [content.code, 0]
        - contains: [content.message, 成功]
    json: {
     "id": $id,
     "name": $name,
     "type": $type,
     "desc": $desc,
     "url": $url,
     "account": $account,
     "signKey": $signKey,
     "weight": $weight,
     "operator": $operator,
     "qualityTag": $qualityTag,
     "errCode": $errCode,
     "extendJson": $extendJson
    }



- test:
    name: 企业名称一致，统一社会信用代码一致，法人姓名不一致
    api: api/checkInfo/enterprise3.yml
    variables:
       name: "厦门亿永丰农资有限公司"
       orgCode: "91350212M0000M4E0V"
       legalRepName: "郭国森${create_randomIdStr()}"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
    }

- test:
    name: 企业名称不一致，统一社会信用代码一致，法人姓名一致
    api: api/checkInfo/enterprise3.yml
    variables:
       name: "厦门亿永丰农资有限公司${create_randomIdStr()}"
       orgCode: "91350212M0000M4E0V"
       legalRepName: "郭国森"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
    }

- test:
    name: 企业名称不一致，统一社会信用代码一致，法人姓名不一致
    api: api/checkInfo/enterprise3.yml
    variables:
       name: "厦门亿永丰农资有限公司${create_randomId()}"
       orgCode: "91350212M0000M4E0V"
       legalRepName: "郭国森create_randomIdStr"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
    }

- test:
    name: 企业名称不一致，统一社会信用代码不一致，法人姓名不一致
    api: api/checkInfo/enterprise3.yml
    variables:
       name: "厦门亿永丰农资有限公司${create_randomId()}"
       orgCode: "91350212M0000M4E0T"
       legalRepName: "郭国森create_randomIdStr"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
    }

- test:
    name: 企业名称一致，统一社会信用代码不一致，法人姓名一致
    api: api/checkInfo/enterprise3.yml
    variables:
       name: "厦门亿永丰农资有限公司"
       orgCode: "91${create_randomId_num(16)}"
       legalRepName: "郭国森"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
    }

- test:
    name: 企业名称一致，统一社会信用代码不一致，法人姓名不一致
    api: api/checkInfo/enterprise3.yml
    variables:
       name: "厦门亿永丰农资有限公司"
       orgCode: "91350212M0000M4E0T"
       legalRepName: "郭国森create_randomIdStr"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
    }



- test:
    name: 重置高质量供应商为空
    api: api/public/providerManagerSave.yml
    variables:
        name: "MAYI"
        url: "https://prodapigw.cloud.alipay.com"
        account: "LTAI9kGTlVr1a7Wx"
        signKey: null
        weight: "1"
        qualityTag: "NORMAL"
        desc: "蚂蚁"
        id: 80
        type: 1
        errCode: "{\"PROVIDER_SERVICE_CONNECTION_CLOSED\":\"placeholder\",\"PROVIDER_SERVICE_TIMEOUT\":\"placeholder\"}"
        extendJson: "{\"manager\":{},\"client\":{},\"select\":{},\"retry\":{}}"
    validate:
        - eq: [content.code, 0]
        - contains: [content.message, 成功]
    json: {
     "id": $id,
     "name": $name,
     "type": $type,
     "desc": $desc,
     "url": $url,
     "account": $account,
     "signKey": $signKey,
     "weight": $weight,
     "operator": $operator,
     "qualityTag": $qualityTag,
     "errCode": $errCode,
     "extendJson": $extendJson
    }

- test:
    name: 重置供应商有数的权重为1
    api: api/public/providerManagerSave.yml
    variables:
        name: "YOU_SHU"
        url: "https://open.yscredit.com/api/request"
        account: "A8251114"
        signKey: null
        weight: "1"
        qualityTag: "NORMAL"
        desc: "有数"
        id: 751
        type: 1
        errCode: null,
        extendJson: "{\"manager\":{},\"client\":{},\"select\":{},\"retry\":{}}"
    validate:
        - eq: [content.code, 0]
        - contains: [content.message, 成功]
    json: {
     "id": $id,
     "name": $name,
     "type": $type,
     "desc": $desc,
     "url": $url,
     "account": $account,
     "signKey": $signKey,
     "weight": $weight,
     "operator": $operator,
     "qualityTag": $qualityTag,
     "errCode": $errCode,
     "extendJson": $extendJson
    }






