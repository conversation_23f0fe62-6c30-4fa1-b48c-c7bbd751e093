config:
    name: 使用曾用名，进行随机金额打款
    base_url: ${ENV(base_url_rpc)}
    variables:
      - accountId: '2df62e63196a496d9bf6327020427149'  #企业oid: 微贷（杭州）金融信息服务有限公司
      - appId: '**********'
      - agentAccountId: '934908b5103542ae81ffa80afe3fbcf8'    #经办人oid: ***********登录账号

teststeps:
-
    name: 获取组织机构实名认证地址-默认
    api: api/orgAuth/orgIdentityUrlOid6.yml
    variables:
      - bizScene: 'DEFAULT'
      - bizSceneContext:
          invalidateSealAuth: false
      - dataCollect:
          clientType: 'WEB'
          bizSource: 'saas_proactive_create'
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - startswith: ["content.data.url", "http"]

-
    name: 获取组织曾用名
    api: api/orgAuth/getHistoryNames.yml
    variables:
      - json: {}
    extract:
      - historyName: content.data.historyNames.0
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.historyNames, null]

-
    name: 页面版企业经办人认证状态查询
    api: api/orgAuth/agentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.authorizedInV21, true]
      - eq: [content.data.authorisedInUserCenter, true]




