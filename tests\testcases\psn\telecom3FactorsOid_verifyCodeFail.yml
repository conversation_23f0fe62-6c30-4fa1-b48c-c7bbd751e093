config:
    name: 发起运营商3要素实名认证，校验短信验证码
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起运营商3要素实名认证
    api: api/psnAuth/telecom3FactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验短信验证码-错误验证码
    api: api/psnAuth/telecom3VerifyCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 30503002]
      - contains: [content.message, "验证码校验失败"]

-
    name: 运营商三要素单发送验证码
    api: api/psnAuth/telecom3FactorAuthCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.subFlows.0.subFlowType, "INDIVIDUAL_TELECOM_3_FACTOR"]
      - eq: [content.data.subFlows.0.status, "ING"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.authType, "INDIVIDUAL_TELECOM_3_FACTOR"]
      - eq: [content.data.indivInfo.name, $name]
