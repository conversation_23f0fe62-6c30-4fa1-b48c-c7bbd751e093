config:
     name: 企业信息比对
#重点：错误码根据不同供应商的返回有所不同，以下错误码需指定供应商主通道：
#企业四要素主通道-敬众，企业二要素主通道-企信宝，律所三要素主通道-企信宝
#非工商组织3要素主通道-企信宝，企业三要素主通道-企信宝
testcases:
-
     name: 组织机构二要素比对
     testcase: testcases/checkInfo/enterprise2_1.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州天谷信息科技有限公司","913301087458306077",0,"成功","-"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","330108000003512",0,"成功","-"]
             - ["正常场景-> 34开头内部调用社会组织二要素接口","福建正中司法鉴定所","34350000796081060U",0,"成功","-"]
             - ["正常场景-> 31开头内部调用律所二要素接口","山东明朗律师事务所","313700000973293896",0,"成功","-"]
-
     name: 组织机构二要素比对2
     testcase: testcases/checkInfo/enterprise2_2.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","上海寻梦信息技术有限公司","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode-填18位codeUSC不正确","杭州天谷信息科技有限公司","91310115MA1K3J0A7C",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode-填15位codeREG不正确","杭州天谷信息科技有限公司","440301103097413",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","913301087458306077",30500100,"缺少参数：企业名称不能为空"]
             - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","",30500100,"缺少参数：企业证件号不能为空"]
             - ["异常场景-> name长度小于2","杭","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间含有空格","杭 州天谷信息科技有限公司","913301087458306077",30500101,"参数错误：组织机构名称含有空格，请检查企业名称正确性"]
             - ["异常场景-> 英文name中间含有空格（支持），信息比对不通过","abc d","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","44030110309741",30500101,"参数错误：请输入正确的组织机构证件号"]
             - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","91330108745830607",30500101,"参数错误：请输入正确的组织机构证件号"]
             - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","9133010874583060778",30500101,"参数错误：请输入正确的组织机构证件号"]
-
     name: 企业三要素比对
     testcase: testcases/checkInfo/enterprise3_1.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确，填18位codeUSC","杭州天谷信息科技有限公司","金宏洲","913301087458306077",0,"成功","-"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","金宏洲","330108000003512",0,"成功","-"]
             - ["正常场景-> 92开头，且法人名字和企业名称一致","孔铁矿","孔铁矿","92130183MA7DAFQQ5A",0,"成功","-"]

-
     name: 企业三要素比对2
     testcase: testcases/checkInfo/enterprise3_2.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
            - ["异常场景-> name不正确","上海寻梦信息技术有限公司","何一兵","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> orgCode-填18位codeUSC不正确","杭州天谷信息科技有限公司","金宏洲","91310115MA1K3J0A7C",30504001,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> orgCode-填15位codeREG不正确","杭州天谷信息科技有限公司","金宏洲","440301103097413",30504001,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> name为空","","何一兵","913301087458306077",30500100,"缺少参数：企业名称不能为空"]
            - ["异常场景-> legalRepName为空","杭州天谷信息科技有限公司","","913301087458306077",30500100,"缺少参数：法定代表人姓名不能为空"]
            - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","金宏洲","",30500100,"缺少参数：企业证件号不能为空"]
            - ["异常场景-> name长度小于2","杭","何一兵","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> 中文name中间含有空格","杭 州天谷信息科技有限公司","金宏洲","913301087458306077",30500101,"参数错误：组织机构名称含有空格，请检查企业名称正确性"]
            - ["异常场景-> 英文name中间含有空格","abc d","金宏洲","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","金宏洲","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","金宏洲","44030110309741",30500101,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","金宏洲","91330108745830607",30500101,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","金宏洲","9133010874583060778",30500101,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> legalRepName长度小于2","杭州天谷信息科技有限公司","何","913301087458306077",30500101,"参数错误：请输入正确长度的法定代表人姓名（2-100位）"]
            - ["异常场景-> 企业名称和统信码都是统信码","913301087458306077","金宏洲","913301087458306077",30504024,"企业名称或企业证件号错误，请检查后重新输入"]
            - ["异常场景-> 企业名称和统信码都是企业名称","杭州天谷信息科技有限公司","金宏洲","杭州天谷信息科技有限公司",30500101,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> 非92开头，企业名称和法人名称都是法人名字，报错","杭州天谷信息科技有限公司","杭州天谷信息科技有限公司","913301087458306077",30504039,"企业名称或法人名称错误，请检查后重新输入"]

-
     name: 企业四要素比对
     testcase: testcases/checkInfo/enterprise4_1.yml
     parameters:
         - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州天谷信息科技有限公司","金宏洲","330722197904110013","913301087458306077",0,"成功","-"]
             - ["正常场景-> 92开头，且法人名字和企业名称一致","孔铁矿","孔铁矿","132322197908160715","92130183MA7DAFQQ5A",0,"成功","-"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","金宏洲","330722197904110013","330108000003512",0,"成功","-"]
-
     name: 企业四要素比对2
     testcase: testcases/checkInfo/enterprise4_2.yml
     parameters:
         - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","上海寻梦信息技术有","金宏","410621198702274071","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName不正确","杭州天谷信息科技有限公司","呵呵呵","330722197904110013","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepCertNo不正确","杭州天谷信息科技有限公司","金宏洲","330108000003512","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 填18位codeUSC不正确","杭州天谷信息科技有限公司","金宏洲","330722197904110013","91310115MA1K3J0A7C",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 填15位codeREG不正确","杭州天谷信息科技有限公司","金宏洲","330722197904110013","440301103097413",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","金宏洲","330722197904110013","913301087458306077",30500100,"不能为空"]
             - ["异常场景-> legalRepName为空","杭州天谷信息科技有限公司","","330722197904110013","913301087458306077",30500100,"不能为空"]
             - ["异常场景-> legalRepCertNo为空","杭州天谷信息科技有限公司","金宏洲","","913301087458306077",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","金宏洲","330722197904110013","",30500100,"不能为空"]
             - ["异常场景-> 中文name中间含有空格","杭 州天谷信息科技有限公司","金宏洲","330722197904110013","913301087458306077",30500101,"空格"]
             - ["异常场景-> 英文name中间含有空格","abc de","金宏洲","330722197904110013","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 112","金宏洲","330722197904110013","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","金宏洲","330722197904110013","44030110309741",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","金宏洲","330722197904110013","91330108745830607",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","金宏洲","330722197904110013","9133010874583060778",30500101,"参数错误"]
             - ["异常场景-> orgCode中间有空格","杭州天谷信息科技有限公司","金宏洲","330722197904110013","913301087 4583060778",30500116,"格式校验"]
             - ["异常场景-> legalRepName长度小于2位","杭州天谷信息科技有限公司","何","330722197904110013","913301087458306077",30500101,"参数错误"]
#目前敬众供应商是去掉空格判断    - ["异常场景-> legalRepName中间含有空格","杭州天谷信息科技有限公司","何 一兵","330722197904110013","913301087458306077",30504001,"不通过"]
             - ["异常场景-> legalRepCertNo中间有空格","杭州天谷信息科技有限公司","金宏洲","330722197904110013","91330108745830 6077",30500116,"格式校验"]
             - ["异常场景-> 企业名称和统信码都是统信码","913301087458306077","金宏洲","330722197904110013","913301087458306077",30504024,"企业名称或企业证件号错误，请检查后重新输入"]
             - ["异常场景-> 企业名称和统信码都是企业名称","杭州天谷信息科技有限公司","金宏洲","330722197904110013","杭州天谷信息科技有限公司",30500101,"参数错误：请输入正确的组织机构证件号"]
             - ["异常场景-> 企业名称和法人名称都是法人名称","杭州天谷信息科技有限公司","杭州天谷信息科技有限公司","330722197904110013","913301087458306077",30504039,"企业名称或法人名称错误，请检查后重新输入"]

-
     name: 律所三要素比对
     testcase: testcases/checkInfo/lawFirm3_1.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","山东明朗律师事务所","王文博","313700000973293896",0,"成功","-"]
-
     name: 律所三要素比对2
     testcase: testcases/checkInfo/lawFirm3_2.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","杭州天谷信息科技有限公司","王文博","313700000973293896",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName不正确","山东明朗律师事务所","王思聪","313700000973293896",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> codeUSC不正确","山东明朗律师事务所","王文博","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","王文博","313700000973293896",30500100,"不能为空"]
             - ["异常场景-> legalRepName为空","山东明朗律师事务所","","313700000973293896",30500100,"不能为空"]
             - ["异常场景-> codeUSC为空","山东明朗律师事务所","王文博","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","北","王文博","313700000973293896",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间含有空格","山东明 朗律师事务所","王文博","313700000973293896",30500101,"空格"]
             - ["异常场景-> 英文name中间含有空格,校验不通过","abc d","王文博","313700000973293896",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["正常场景-> legalRepName中间含有空格","山东明朗律师事务所","王 文博","313700000973293896",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","王文博","313700000973293896",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName长度小于2","山东明朗律师事务所","王","313700000973293896",30500101,"参数错误"]
             - ["异常场景-> codeUSC位数不正确","山东明朗律师事务所","王文博","31370000097329389",30500101,"参数错误"]
             - ["异常场景-> codeUSC中间有空格","山东明朗律师事务所","王文博","31370000097329389 6",30500116,"格式校验"]

-
     name: 组织机构三要素比对
     testcase: testcases/checkInfo/organization3_1.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","杭州天谷信息科技有限公司","金宏洲","913301087458306077",0,"成功","-"]
             - ["正常场景-> 34开头内部调用社会组织三要素接口","福建正中司法鉴定所","金尔启","34350000796081060U",0,"成功","-"]
             - ["正常场景-> 31开头内部调用律所三要素接口","山东明朗律师事务所","王文博","313700000973293896",0,"成功","-"]

-
     name: 组织机构三要素比对2
     testcase: testcases/checkInfo/organization3_2.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","北京理工大学教育基金会","何一兵","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName不正确","杭州天谷信息科技有限公司","杨宾","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode不正确","杭州天谷信息科技有限公司","何一兵","53100000500021676K",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","杨宾","53100000500021676K",30500100,"不能为空"]
             - ["异常场景-> legalRepName为空","北京理工大学教育基金会","","53100000500021676K",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","北京理工大学教育基金会","杨宾","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","北","杨宾","53100000500021676K",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间含有空格","北 京理工大学教育基金会","杨宾","53100000500021676K",30500101,"空格"]
             - ["异常场景-> 英文name中间含有空格，信息比对不通过","abc d","杨宾","53100000500021676K",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭c信-+~。息（有) 司 e 1","杨宾","53100000500021676K",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName长度小于2","北京理工大学教育基金会","郭","53100000500021676K",30500101,"参数错误"]
             - ["异常场景-> legalRepName中间含有空格","杭州天谷信息科技有限公司","何一 兵","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode位数不正确","北京理工大学教育基金会","杨宾","53100000500021676",30500101,"参数错误"]
             - ["异常场景-> codeUSC中间有空格","北京理工大学教育基金会","杨宾","5310000050 0021676K",30500116,"格式校验"]
-
     name: 非工商组织三要素比对
     testcase: testcases/checkInfo/orgSocial3_1.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","天津大学","柴立元","12100000401359321Q",0,"成功","-"]

-
     name: 非工商组织三要素比对2
     testcase: testcases/checkInfo/orgSocial3_2.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","北京理工大学教育基金会","金东寒","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName不正确","天津大学","王思聪","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode不正确","天津大学","金东寒","913301087458306077",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","金东寒","12100000401359321Q",30500100,"不能为空"]
             - ["异常场景-> legalRepName为空","天津大学","","12100000401359321Q",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","天津大学","金东寒","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","天","金东寒","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间有空格","天 津大学","金东寒","12100000401359321Q",30500101,"参数错误"]
             - ["异常场景-> 英文name中间有空格，信息比对不通过","abc d","金东寒","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭c谷-+~。息（有) 司 e1 1","金东寒","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName长度小于2","天津大学","金","12100000401359321Q",30500101,"参数错误"]
             - ["异常场景-> legalRepName中间有空格","天津大学","金 东寒","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> codeUSC长度位数不正确","天津大学","金东寒","1210000040135931Q",30500101,"参数错误"]
-
     name: 企业二要素比对_律所二要素
     testcase: testcases/checkInfo/enterprise2_1.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","浙江浙临律师事务所","31330000725275079F",0,"成功","-"]

-
     name: 企业二要素比对_律所二要素2
     testcase: testcases/checkInfo/enterprise2_2.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","北京市中银","31320000MD01904540",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode-填18位codeUSC不正确","北京市中银律师事务所","311100004005693354",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","311100004005693352",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","北京市中银律师事务所","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","北","31320000MD01904540",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间含有空格","北京 市中银律师事务所","311100004005693352",30500101,"空格"]
             - ["异常场景-> 英文name中间含有空格（支持），信息比对不通过","abc d","31320000MD01904540",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","31320000MD01904540",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode长度小于15位","北京市中银律师事务所","311100004",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于15位但小于18位","北京市中银律师事务所","31110000400569335",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于18位","北京市中银律师事务所","3111000040056933521",30500101,"参数错误"]
-
     name: 企业二要素比对_社会组织二要素
     testcase: testcases/checkInfo/enterprise2_1.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","天津大学","12100000401359321Q",0,"成功","-"]

-
     name: 企业二要素比对_社会组织二要素2
     testcase: testcases/checkInfo/enterprise2_2.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","天津大","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode-填18位codeUSC不正确","天津大学","121000004013593210",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","12100000401359321Q",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","天津大学","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","天","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间含有空格","天津 大学","12100000401359321Q",30500101,"空格"]
             - ["异常场景-> 英文name中间含有空格（支持），信息比对不通过","abc d","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","12100000401359321Q",30504001,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode长度小于15位","天津大学","121000004",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于15位但小于18位","天津大学","12100000401359321",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于18位","天津大学","12100000401359321Q1",30500101,"参数错误"]

-
     name: 企业精确查询接口1
     testcase: testcases/checkInfo/enterpriseDetail_1.yml
     parameters:
         - CaseName-keyword-VALIDATE1-VALIDATE2-VALIDATE3-VALIDATE4:
             - ["正常场景-> 有查询结果，根据名称查","深圳天谷信息科技有限公司",0,"成功","深圳天谷信息科技有限公司","91440300MA5ERJGK30","440300202595167"]
             - ["正常场景-> 有查询结果，根据codeUSC查","91440300MA5ERJGK30",0,"成功","深圳天谷信息科技有限公司","91440300MA5ERJGK30"]
             - ["正常场景-> 有查询结果，根据codeORG查","MA5ERJGK3",0,"成功","深圳天谷信息科技有限公司","91440300MA5ERJGK30"]

-
     name: 企业精确查询接口2
     testcase: testcases/checkInfo/enterpriseDetail_2.yml
     parameters:
         - CaseName-keyword-VALIDATE1-VALIDATE2:
             - ["正常场景-> 无查询结果","深圳天谷信息科技有限公",0,"成功"]

-
     name: 企业精确查询接口3
     testcase: testcases/checkInfo/enterpriseDetail_3.yml
     parameters:
         - CaseName-keyword-VALIDATE1-VALIDATE2:
             - ["异常场景-> 关键字为空","",30500100,"缺少参数：keyword"]
             - ["异常场景-> 关键字含有空格","测 试",30500116,"格式校验不通过，请检查填入信息是否有多余空格"]

-
     name: 企业精确查询接口4
     testcase: testcases/checkInfo/enterpriseDetail_4.yml
     parameters:
         - CaseName-keyword-VALIDATE1-VALIDATE2-VALIDATE3-VALIDATE4:
             - ["正常场景-> 有查询结果，根据codeREG查","440300202595167",0,"成功","深圳天谷信息科技有限公司","440300202595167"]
