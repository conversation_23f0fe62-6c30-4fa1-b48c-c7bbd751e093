config:
    name: 查询刷脸结果接口
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 校验刷脸结果-对外接口（废弃，新用户不再对接）
    api: api/psnAuth/queryFaceStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.status, $VALIDATE1]
      - contains: [content.data.message, $VALIDATE2]

-
    name: 查询认证信息--代替查询刷脸结果对外接口
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, $VALIDATE1]
      - eq: [content.data.failReason, $VALIDATE3]
