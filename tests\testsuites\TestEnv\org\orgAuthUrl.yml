config:
    name: 获取组织机构核身认证地址
#备注：1.核身没有人工审核 2.核身没有agentAccountId 3.运营支撑平台：默认组织认证类型为对公打款 4.获取组织机构核身地址，后续页面版做信息核验，集测未包含
testcases:
-
    name: 获取组织机构核身认证地址-查询应用配置信息-$CaseName
    testcase: testcases/org/orgAuthUrl_configAll.yml
    parameters:
        - CaseName-appId-authType-availableAuthTypes-agentAuthAdvancedEnabled-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-VALIDATE1-VALIDATE2:
            - ["不指定默认认证类型和页面显示的认证方式","${ENV(appId)}","",[],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",6,"ORGANIZATION_TRANSFER"]
            - ["指定默认认证类型为法人授权书认证","${ENV(appId)}","ORG_LEGAL_AUTHORIZE",[],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",6,"LEGAL_REP_SIGN_AUTHORIZE"]
            - ["指定默认认证类型为企业支付宝","${ENV(appId)}","ORG_ZM_AUTHORIZE",[],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",6,"ORGANIZATION_ZMXY"]
            - ["指定页面显示的认证方式,只有对公打款","${ENV(appId)}","",["ORG_BANK_TRANSFER"],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",3,"ORGANIZATION_TRANSFER"]
            - ["指定页面显示的认证方式,只有对公打款和法人授权书","${ENV(appId)}","",["ORG_BANK_TRANSFER","ORG_LEGAL_AUTHORIZE"],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",4,"ORGANIZATION_TRANSFER"]
            - ["指定默认认证类型为企业支付宝，但可选方式种没有","${ENV(appId)}","ORG_ZM_AUTHORIZE",["ORG_BANK_TRANSFER"],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",3,"ORGANIZATION_TRANSFER"]

-
    name: 获取组织机构核身认证地址-$CaseName
    testcase: testcases/org/orgAuthUrl.yml
    parameters:
        - CaseName-authType-availableAuthTypes-agentAuthAdvancedEnabled-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName:
            - ["企业信息都正确(常规企业)","",[],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋"]
            - ["企业信息都正确(15位工商注册码)","",[],[],"***************",1,"深圳天谷信息科技有限公司","","330702198409120432","张晋"]
            - ["企业信息都正确(外籍法人-护照)","",[],[],"91320214733305872J",1,"百达晶心精密机械（无锡）有限公司","INDIVIDUAL_PASSPORT","E6409934C","黄亮茳（WEE LIANG KIANG）"]
            - ["企业信息都正确(外籍法人-港澳通行证)","",[],[],"91440300060276033X",1,"深圳市锐玲商贸有限公司","INDIVIDUAL_CH_HONGKONG_MACAO","H60159767","高英娣"]
            - ["企业信息都正确(企业名称中支持特殊字符，法人证件类型传身份证)","",[],[],"91330108MA2G042101",1,"esigntest杭z天-+~。（技有) 限 e ee 11 11","INDIVIDUAL_CH_IDCARD","110108196710262291","何一兵"]
            - ["法人名称支持半角逗号","",[],[],"91330108MA2G042101",1,"esigntest杭z天-+~。（技有) 限 e ee 11 11","INDIVIDUAL_CH_IDCARD","110108196710262291","何一兵,"]


-
    name: 获取组织机构核身认证地址2-查询应用配置信息-$CaseName
    testcase: testcases/org/orgAuthUrl2_configAll.yml
    parameters:
        - CaseName-appId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-showAgreement-VALIDATE1:
            - ["appid展示协议页面,参数配置true","${ENV(appId2)}","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","true",True]
            - ["appid展示协议页面,参数配置false","${ENV(appId2)}","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","false",False]
#            - ["appid展示协议页面,参数配置没有传参","${ENV(appId2)}","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","",True]
            - ["appid不展示协议页面,参数配置true","${ENV(appId)}","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","true",True]
            - ["appid不展示协议页面,参数配置false","${ENV(appId)}","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","false",False]
#            - ["appid不展示协议页面,参数配置没有传参","${ENV(appId)}","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","",False]

-
    name: 获取组织机构核身认证地址3-查询应用配置信息-$CaseName
    testcase: testcases/org/orgAuthUrl3_configAll.yml
    parameters:
        - CaseName-appId-agentAvailableAuthTypes-agentAuthType-VALIDATE1-VALIDATE2:
            - ["appid配置都勾上,可选方式都选择，默认方式择一","${ENV(appId2)}",["PSN_TELECOM_AUTHCODE","PSN_BANK4_AUTHCODE","PSN_FACEAUTH_BYURL","PSN_FACEAUTH_BYURL_ZHIMA","PSN_FACEAUTH_BYURL_WEIZHONG","PSN_FACEAUTH_BYURL_ESIGN","PSN_FACEAUTH_BYURL_WE_CHAT"],"PSN_BANK4_AUTHCODE",7,"INDIVIDUAL_BANKCARD_4_FACTOR"]
            - ["appid配置都勾上,默认方式不在可选方式中，取默认实名方式","${ENV(appId2)}",["PSN_BANK4_AUTHCODE","PSN_FACEAUTH_BYURL","PSN_FACEAUTH_BYURL_ZHIMA","PSN_FACEAUTH_BYURL_WEIZHONG","PSN_FACEAUTH_BYURL_ESIGN","PSN_FACEAUTH_BYURL_WE_CHAT"],"PSN_TELECOM_AUTHCODE",6,"INDIVIDUAL_BANKCARD_4_FACTOR"]
            - ["appid配置无运营商三要素,可选方式中有，默认方式中指定，取默认实名方式","${ENV(appId3)}",["PSN_BANK4_AUTHCODE","PSN_FACEAUTH_BYURL","PSN_FACEAUTH_BYURL_ZHIMA","PSN_FACEAUTH_BYURL_WEIZHONG","PSN_FACEAUTH_BYURL_WE_CHAT"],"PSN_TELECOM_AUTHCODE",4,"INDIVIDUAL_BANKCARD_4_FACTOR"]
            - ["appid配置无运营商三要素,可选方式中只有运营商三要素，默认方式为支付宝刷脸，取支付宝刷脸","${ENV(appId3)}",["PSN_TELECOM_AUTHCODE"],"PSN_FACEAUTH_BYURL_ZHIMA",4,"FACEAUTH_ZMXY"]
            - ["appid配置无运营商三要素,可选方式中只有运营商三要素，默认方式为运营商三要素，取默认值","${ENV(appId3)}",["PSN_TELECOM_AUTHCODE"],"PSN_TELECOM_AUTHCODE",4,"INDIVIDUAL_BANKCARD_4_FACTOR"]

-
    name: 获取组织机构核身认证地址4-查询应用配置信息-$CaseName
    testcase: testcases/org/orgAuthUrl4_configAll.yml
    parameters:
        - CaseName-appId-availableAuthTypes-authType-VALIDATE1-VALIDATE2:
            - ["配置项中未勾选法人认证，可用认证方式和默认整整方式都不包含LEGAL_REP_AUTH","${ENV(appId2)}",[],"",6,"ORGANIZATION_TRANSFER"]
            - ["配置项中未勾选法人认证，可用认证方式包含LEGAL_REP_AUTH,发起不报错","${ENV(appId3)}",["LEGAL_REP_AUTH"],"",4,"ORGANIZATION_TRANSFER"]
#            - ["配置项中未勾选法人认证，默认认证方式LEGAL_REP_AUTH,发起不报错","${ENV(appId3)}",[],"LEGAL_REP_AUTH",4,"ORGANIZATION_TRANSFER"]
            - ["配置项中勾选法人认证，默认方式不在可选方式中，发起不报错","${ENV(appId)}",["ORG_BANK_TRANSFER"],"LEGAL_REP_AUTH",3,"ORGANIZATION_TRANSFER"]
            - ["配置项中勾选法人认证，默认方式不在可选方式中，发起不报错","${ENV(appId)}",["ORG_BANK_TRANSFER"],"LEGAL_REP_AUTH",3,"ORGANIZATION_TRANSFER"]

-
    name: 根据flowId获取实名链接-$CaseName
    testcase: testcases/org/getFlowUrl.yml
    parameters:
        - CaseName-authType-availableAuthTypes-agentAuthAdvancedEnabled-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName:
            - ["企业信息都正确(常规企业)","",[],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋"]

#-
#    name: 获取组织机构实名认证地址-企业错误信息-$CaseName
#    testcase: testcases/org/orgErrorMsg.yml
#    parameters:
#        - CaseName-accountId-appId-authType-availableAuthTypes-agentAuthAdvancedEnabled-agentAccountId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-repeatIdentity:
#            - ["企业异常场景","${ENV(accountId)}","**********","",[],[],"4bc90e5d670f403c8543c99ca1001e3f","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司哈哈","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",true]