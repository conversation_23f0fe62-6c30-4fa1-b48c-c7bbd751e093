config:
    name: 发起运营商3要素实名/核身认证并且校验短信验证码

testcases:
-
    name: 发起运营商3要素核身认证-$CaseName
    testcase: testcases/psn/telecom3Factors_verifyCodeSuccess.yml
    parameters:
        - CaseName-name-idNo-mobileNo-grade-authcode:
            - ["正常场景-简版三要素-正确验证码","武玉华","410621198702274071","***********","STANDARD","123456"]

-
    name: 发起运营商3要素实名认证-$CaseName
    testcase: testcases/psn/telecom3FactorsOid_verifyCodeSuccess.yml
    parameters:
        - CaseName-accountId-mobileNo-repetition-grade-authcode-name:
            - ["正常场景-简版三要素-正确验证码","${ENV(creatorOid)}","***********",true,"STANDARD","123456","武玉华"]
