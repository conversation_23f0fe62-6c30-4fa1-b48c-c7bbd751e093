config:
    name: 经办人认证方式变化后更新
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取企业实名认证地址1
    api: api/orgAuth/orgIdentityUrlOid5.yml
    variables:
      - authType: ""
      - availableAuthTypes: []
      - defaultAgentAuthType: $defaultAgentAuthType1
      - agentAvailableAuthTypes: $agentAvailableAuthTypes1
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 获取企业实名认证地址2-修改经办人认证方式
    api: api/orgAuth/orgIdentityUrlOid5.yml
    variables:
      - authType: ""
      - availableAuthTypes: []
      - defaultAgentAuthType: $defaultAgentAuthType2
      - agentAvailableAuthTypes: $agentAvailableAuthTypes2
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.authType.allPsnAuthType, $VALIDATE1]
      - eq: [content.data.authType.defaultPsnAuthType, $VALIDATE2]
