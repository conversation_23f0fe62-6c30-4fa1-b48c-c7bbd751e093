config:
     name: 信息比对详情版

testcases:
-
     name: 个人银行卡四要素详情版比对
     testcase: testcases/checkInfo/bank4detail_1.yml
     parameters:
         - CaseName-idNo-name-cardNo-mobileNo-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","342622198905262396","许华建","****************","***********",0,"成功","-"]
             - ["mock场景-> 测试前缀","******************","测试测试","****************","***********",0,"成功","-"]

-
     name: 个人运营商三要素详情版比对
     testcase: testcases/checkInfo/telecom3detail_1.yml
     parameters:
         - CaseName-name-idNo-mobileNo-VALIDATE1-VALIDATE2-VALIDATE3:
            - ["正常场景-> name正确","武玉华","******************","***********",0,"成功","-"]
            - ["mock场景-> 测试前缀","测试测试","******************","***********",0,"成功","-"]

-
     name: 个人银行卡三要素详情版比对
     testcase: testcases/checkInfo/bank3detail_1.yml
     parameters:
         - CaseName-idNo-name-cardNo-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","342622198905262396","许华建","****************",0,"成功","-"]
             - ["正常场景-> 身份证尾数带X","51070419800124311X","王继光","6230520480025878077",0,"成功","-"]
