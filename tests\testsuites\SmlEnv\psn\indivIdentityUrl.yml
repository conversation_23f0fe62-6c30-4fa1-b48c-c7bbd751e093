config:
    name: 获取个人实名认证地址

testcases:
-
    name: 获取个人实名认证地址-$CaseName
    testcase: testcases/psn/indivIdentityUrl.yml
    parameters:
        - CaseName-appId-accountId-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-repeatIdentity:
            - ["证件类型为身份证","${ENV(appId)}","04ae114eca0a4a97af9011c216fa7485","",[],[],"武玉华","******************","INDIVIDUAL_CH_IDCARD","***********","****************",[],true]
