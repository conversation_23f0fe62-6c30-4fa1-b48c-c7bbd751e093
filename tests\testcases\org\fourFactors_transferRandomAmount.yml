config:
    name: 发起企业核身认证4要素校验-随机金额打款认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起企业核身认证4要素校验
    api: api/orgAuth/fourFactors.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询打款银行信息
    api: api/orgAuth/subbranch.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.list.0.bankName, $subbranch]

-
    name: 发起随机金额打款认证
    api: api/orgAuth/transferRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询随机金额打款进度1
    api: api/orgAuth/transferProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.process, "PAID"]
      - eq: [content.data.cardNo, $cardNo]
      - eq: [content.data.message, "打款申请成功"]

-
    name: 随机金额校验
    api: api/orgAuth/verifyRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询随机金额打款进度2
    api: api/orgAuth/transferProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.process, "ORGANFINISHED"]
      - eq: [content.data.message, "打款申请成功"]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.subFlows.0.subFlowType, "ORGANIZATION_TRANSFER_RANDOM_AMOUNT"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

#如果银行相关信息查询不到，请确认测试appid是否开启了权限
-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "ORGANIZATION_TRANSFER_RANDOM_AMOUNT"]
      - eq: [content.data.organInfo.name, $name]
      - eq: [content.data.organInfo.legalRepCertType, $legalRepCertType]
      - eq: [content.data.organInfo.cardNo, $cardNo]
      - eq: [content.data.organInfo.bank, $bank]
      - eq: [content.data.organInfo.subbranch, $subbranch]

-
    name: 根据flowId查询其认证子service的详情 对内服务GET方式
    api: api/public/queryFlowServiceInnerLineGet.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "成功"]
      - eq: [content.data.objectType, "企业"]
      - eq: [content.data.subFlows.0.subFlowType, "随机金额打款校验"]

-
    name: 根据flowId查询其认证子service的详情 对内服务Post方式
    api: api/public/queryFlowServiceInnerLinePost.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "成功"]
      - eq: [content.data.objectType, "企业"]
      - eq: [content.data.subFlows.0.subFlowType, "随机金额打款校验"]
