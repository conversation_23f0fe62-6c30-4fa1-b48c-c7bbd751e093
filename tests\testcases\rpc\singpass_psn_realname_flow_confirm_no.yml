config:
    name: singpass个人实名流程：否认信息
    variables:
        accountId: ${ENV(singpass_psn_oid_notrealnamed)}
        name: "singpass test ${create_randomIdStr(3)}"
        certNo: "S8800003H"
        mobile: "***********"
        mobile2: "***********"

teststeps:
    -   name: 重置实名
        api: api/public/resetIdentity.yml
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]

    -   name: 发起个人实名
        api: api/rpc/singpassPsnApply.yml
        extract:
            -   url: content.data.url
            -   stateCode: state=(.*?)&
            -   code: code=(.*?)&
            -   serviceId: serviceId=(.*?)&
            -   flowId: content.data.flowId
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]
            -   len_gt: [content.data.flowId, 0]

    -   name: 查询个人实名流程：result=0
        api: api/rpc/singpassPsnResult.yml
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]
            -   eq: [content.data.flowId, $flowId]
            -   eq: [content.data.accountId, $accountId]
            -   eq: [content.data.result, 0]

    -   name: mock singpass登录认证
        api: api/realname/mockSingpassPsnLogin.yml
        validate:
            -   eq: [status_code,200]
            -   eq: [content.success, True]


#    -   name: 发起singpass回调
#        api: api/public/singpassCallback.yml
#        validate:
#            -   eq: [status_code,200]
#            -   eq: [headers.Content-Type, 'text/html']
#            -   contains: [url, "${ENV(singpass_psn_callback)}"]

    -   name: 查询个人实名流程：result=1
        api: api/rpc/singpassPsnResult.yml
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]
            -   eq: [content.data.flowId, $flowId]
            -   eq: [content.data.accountId, $accountId]
            -   eq: [content.data.result, 1]
            -   eq: [content.data.info.name, $name]
            -   eq: [content.data.info.certNo, $certNo]
            -   eq: [content.data.info.mobileNo, $mobile]


    -   name: 否认singpass认证信息
        api: api/rpc/singpassPsnConfirm.yml
        variables:
            confirm_state: 2
            operatorMobile: $mobile2
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]
            -   eq: [content.data.flowId, $flowId]
            -   eq: [content.data.accountId, $accountId]
            -   eq: [content.data.result, 3]
            -   eq: [content.data.info.name, $name]
            -   eq: [content.data.info.certNo, $certNo]
            -   eq: [content.data.info.mobileNo, $mobile2]

    -   name: 查询个人实名流程：result=3
        api: api/rpc/singpassPsnResult.yml
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]
            -   eq: [content.data.flowId, $flowId]
            -   eq: [content.data.accountId, $accountId]
            -   eq: [content.data.result, 3]
            -   eq: [content.data.info.name, $name]
            -   eq: [content.data.info.certNo, $certNo]
            -   eq: [content.data.info.mobileNo, $mobile2]