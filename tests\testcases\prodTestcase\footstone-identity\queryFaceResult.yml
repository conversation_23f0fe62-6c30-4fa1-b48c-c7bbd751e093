config:
    name: 查询刷脸结果接口
    base_url: ${ENV(footstone_identity_url)}

teststeps:
-
    name: 校验刷脸结果-对外接口（废弃，新用户不再对接）
    api: api/prodApi/footstone-identity/queryFaceStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.status, $VALIDATE1]
      - contains: [content.data.message, $VALIDATE2]

-
    name: 查询认证信息--代替查询刷脸结果对外接口
    api: api/prodApi/footstone-identity/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, $VALIDATE1]
      - eq: [content.data.failReason, $VALIDATE3]
