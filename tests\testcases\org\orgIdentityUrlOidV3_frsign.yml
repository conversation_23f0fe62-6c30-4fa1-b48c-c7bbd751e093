config:
    name: 获取组织机构实名认证地址V3-法人授权书认证
    base_url: ${ENV(base_url_rpc)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlV3.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.url, "tsignversion=eve"]

-
    name: 页面版企业经办人认证状态查询
    api: api/orgAuth/agentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.authorizedInV21, true]
      - eq: [content.data.authorisedInUserCenter, true]

-
    name: 页面版企业页面实名认证, 结果接口V2--前端打开页面之后查询实名的进程
    api: api/orgAuth/processonWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版信息比对
    api: api/orgAuth/infoVerify.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]
      - eq: [content.data.operatorType, $operatorType]

-
    name: 页面版法人签署认证进度结果
    api: api/orgAuth/frSignResult.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]
      - eq: [content.data.status, "NOTSTART"]
      - eq: [content.data.msg, "签署尚未启动"]

-
    name: 页面版发起实名法人授权书签署--企业重构后的接口
    api: api/orgAuth/beforeTriggerFrSign.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]
      - ne: [content.data.url, null]

-
    name: 页面版法人授权信息查询
    api: api/orgAuth/getFrSignInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]
      - eq: [content.data.certNo, $idNumber]
      - eq: [content.data.certType, $certType]
      - eq: [content.data.oid, $accountId]
      - eq: [content.data.legalRepName, $orgLegalName]
      - eq: [content.data.legalRepCertType, $legalRepCertType]
      - eq: [content.data.legalRepCertNo, $orgLegalIdNumber]

-
    name: 法人授权书的组织四要素信息比对
    api: api/orgAuth/info4AndSaveLegalCard.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]

#添加签署集测

-
    name: 页面版企业页面实名认证, 结果接口V2--前查询实名的进程
    api: api/orgAuth/processonWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
#进入签署之后type才会改变      - eq: [content.data.type, "LEGAL_REP_SIGN_AUTHORIZE"]
      - eq: [content.data.status, "ING"]
      - eq: [content.data.ifInfoAuthSuccessful, true]
