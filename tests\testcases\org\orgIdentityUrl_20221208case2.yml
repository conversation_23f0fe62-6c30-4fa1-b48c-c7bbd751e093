config:
    name: 企业实名支持新增传入
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建账号
    api: api/public/createByThirdPartyUserId_psnV1.yml
    extract:
      - agentAccountId: content.data.accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.accountId, null]
-
    name: 获取企业实名认证地址
    api: api/orgAuth/orgIdentityUrlOid5.yml
    variables:
      - authType: ""
      - availableAuthTypes: []
      - agentAccountId: $agentAccountId
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.authType.defaultPsnAuthType, $VALIDATE1]
      - eq: [content.data.authType.allPsnAuthType, $VALIDATE2]
