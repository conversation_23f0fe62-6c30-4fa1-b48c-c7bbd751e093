config:
     name: 个人信息比对

testcases:
-
     name: 个人二要素比对
     testcase: testcases/checkInfo/psn2_1.yml
     parameters:
         - CaseName-idNo-name-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","******************","武玉华",0,"成功"]
             - ["正常场景-> 身份证尾数带X","14273019911003072X","高美君",0,"成功"]
             - ["异常场景-> idNo不正确","362302198609175011","武玉华",********,"不通过"]
             - ["异常场景-> name不正确","******************","王思聪",********,"不通过"]

-
     name: 个人二要素比对2
     testcase: testcases/checkInfo/psn2_2.yml
     parameters:
         - CaseName-idNo-name-VALIDATE1-VALIDATE2:
             - ["异常场景-> idNo为空","","武玉华",********,"不能为空"]
             - ["异常场景-> name为空","******************","",********,"不能为空"]
             - ["异常场景-> 身份证号码小于18位","*****************","武玉华",********,"参数错误"]
             - ["异常场景-> 身份证号码大于18位","******************2","武玉华",********,"参数错误"]
#             - ["异常场景-> 姓名小于2位","******************","武",********,"参数错误"]
             - ["异常场景-> 姓名大于20位","******************","武去微软推哦怕时代法国红酒看来自行车不能你们",********,"参数错误"]
             - ["异常场景-> 姓名中含有空格","******************","武 玉尘",********,"空格"]

-
     name: 个人运营商三要素比对
     testcase: testcases/checkInfo/telecom3_1.yml
     parameters:
         - CaseName-name-idNo-mobileNo-VALIDATE1-VALIDATE2:
            - ["正常场景-> name正确","武玉华","******************","***********",0,"成功"]
            - ["异常场景-> idNo不正确","武玉华","14273019911003072X","***********",********,"不通过"]
            - ["异常场景-> mobileNo不正确","武玉华","******************","13699846000",********,"不通过"]
            - ["异常场景-> name不正确","王思聪","******************","***********",********,"不通过"]

-
     name: 个人运营商三要素比对2
     testcase: testcases/checkInfo/telecom3_2.yml
     parameters:
         - CaseName-name-idNo-mobileNo-VALIDATE1-VALIDATE2-VALIDATE3:
            - ["异常场景-> 必填项idNo为空","武玉华","","***********",********,"不能为空"]
            - ["异常场景-> 必填项mobileNo为空","武玉华","******************","",********,"不能为空"]
            - ["异常场景-> 必填项name为空","","******************","***********",********,"缺少参数：姓名不能为空"]
            - ["异常场景-> 身份证号码小于18位","武玉华","*****************","***********",********,"参数错误"]
            - ["异常场景-> 身份证号码大于18位","武玉华","******************2","***********",********,"参数错误"]
#            - ["异常场景-> 姓名小于2位","武","******************","***********",********,"参数错误"]
            - ["异常场景-> 姓名大于20位","武去微软推哦怕时代法国红酒看来自行车不能你们","******************","***********",********,"参数错误"]
            - ["异常场景-> 姓名中含有空格","武 玉华","******************","***********",********,"格式校验不通过"]
            - ["异常场景-> 手机号小于11位","武","******************","**********",********,"参数错误"]
            - ["异常场景-> 手机号大于11位","武","******************","***********1",********,"参数错误"]

-
     name: 个人银行卡三要素比对
     testcase: testcases/checkInfo/bank3_1.yml
     parameters:
         - CaseName-idNo-name-cardNo-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","******************","姜娇","****************",0,"成功"]
             - ["正常场景-> 身份证尾数带X","14273019911003072X","高美君","****************",0,"成功"]
             - ["异常场景-> name不正确","******************","王思聪","****************",********,"不通过"]
             - ["异常场景-> cardNo不正确","******************","武玉华","****************",********,"不通过"]
             - ["异常场景-> idNo不正确","******************","武玉华","****************",********,"不通过"]

-
     name: 个人银行卡三要素比对2
     testcase: testcases/checkInfo/bank3_2.yml
     parameters:
         - CaseName-idNo-name-cardNo-VALIDATE1-VALIDATE2:
             - ["异常场景-> 必填项name为空","14273019911003072X","","****************",********,"缺少参数：姓名不能为空"]
             - ["异常场景-> 必填项idNo为空","","武玉华","****************",********,"不能为空"]
             - ["异常场景-> 必填项cardNo为空","******************","武玉华","",********,"不能为空"]
             - ["异常场景-> 身份证号码小于18位","*****************","武玉华","****************",********,"参数错误"]
             - ["异常场景-> 身份证号码大于18位","******************2","武玉华","****************",********,"参数错误"]
#             - ["异常场景-> 姓名小于2位","******************","武","****************",********,"参数错误"]
             - ["异常场景-> 姓名大于20位","******************","武去微软推哦怕时代法国红酒看来自行车不能你们","****************",********,"参数错误"]
             - ["异常场景-> 姓名中间含有空格","******************","武 玉华","****************",********,"空格"]
             - ["异常场景-> 手机号小于11位","武","******************","**********",********,"参数错误"]

-
     name: 个人银行卡四要素比对
     testcase: testcases/checkInfo/bank4_1.yml
     parameters:
         - CaseName-idNo-name-cardNo-mobileNo-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","******************","姜娇","****************","***********",0,"成功"]
             - ["异常场景-> idNo不正确","14273019911003072X","武玉华","****************","***********",********,"不通过"]
             - ["异常场景-> name不正确","******************","王思聪","****************","***********",********,"不通过"]
             - ["异常场景-> cardNo不正确","******************","武玉华","****************","***********",********,"不通过"]
             - ["异常场景-> mobileNo不正确","******************","武玉华","****************","***********",********,"不通过"]

-
     name: 个人银行卡四要素比对2
     testcase: testcases/checkInfo/bank4_2.yml
     parameters:
         - CaseName-idNo-name-cardNo-mobileNo-VALIDATE1-VALIDATE2:
             - ["异常场景-> 必填项name为空","******************","","****************","***********",********,"缺少参数：姓名不能为空"]
             - ["异常场景-> 必填项idNo为空","","武玉华","****************","***********",********,"不能为空"]
             - ["异常场景-> 必填项cardNo为空","******************","武玉华","","***********",********,"不能为空"]
             - ["异常场景-> 必填项mobileNo为空","******************","武玉华","****************","",********,"不能为空"]
             - ["异常场景-> 身份证号码小于18位","*****************","武玉华","****************","***********",********,"参数错误"]
             - ["异常场景-> 身份证号码大于18位","******************2","武玉华","****************","***********",********,"参数错误"]
#             - ["异常场景-> 姓名小于2位","******************","武","****************","***********",********,"参数错误"]
             - ["异常场景-> 姓名大于20位","******************","武去微软推哦怕时代法国红酒看来自行车不能你们","****************","***********",********,"参数错误"]
             - ["异常场景-> 姓名中间含有空格","******************","武 玉华","****************","***********",********,"空格"]
             - ["异常场景-> 手机号小于11位","武","******************","**********","***********",********,"参数错误"]

-
     name: 个人运营商三要素详情版
     testcase: testcases/checkInfo/telecom3_3.yml
     parameters:
         - CaseName-name-idNo-mobileNo-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","武玉华","******************","***********",0,"成功"]  

-
     name: 个人银行三要素详情版
     testcase: testcases/checkInfo/bank3_3.yml
     parameters:
         - CaseName-idNo-name-cardNo-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","******************","姜娇","****************",0,"成功"]

-
     name: 个人银行四要素详情版
     testcase: testcases/checkInfo/bank4_3.yml
     parameters:
         - CaseName-idNo-name-cardNo-mobileNo-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","******************","姜娇","****************","***********",0,"成功"]

-
     name: 个人姓名、手机号2要素信息比对
     testcase: testcases/checkInfo/telecom2Factors.yml
     parameters:
         - CaseName-appId-mobileNo-name-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","${ENV(appId10)}","***********","刘志华",0,"成功"]


