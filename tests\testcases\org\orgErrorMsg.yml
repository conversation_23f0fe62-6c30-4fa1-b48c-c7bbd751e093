config:
    name: 企业错误信息
    base_url: ${ENV(base_url)}
teststeps:
-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]


#-
#    name: 企业二要素错误信息
#    api: api/orgAuth/zhimaErrorsMsg.yml
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 30504501]
#      - contains: [content.message, "企业名称不⼀致"]

-
    name: 企业三要素错误信息
    api: api/orgAuth/infoVerifyErrorsMsg.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 30504501]
      - contains: [content.message, "企业名称不⼀致"]