config:
    name: 发起银行卡4要素实名/核身认证并且校验短信验证码

testcases:
-
    name: 发起银行卡4要素核身认证-$CaseName
    testcase: testcases/psn/bankCard4Factors_verifyCodeSuccess.yml
    parameters:
        - CaseName-certType-name-idNo-mobileNo-bankCardNo-grade-authcode:
            - ["正常场景-简版四要素-正确验证码","","许华建","342622198905262396","***********","****************","STANDARD","123456"]

-
    name: 发起银行卡4要素核身认证2-$CaseName
    testcase: testcases/psn/bankCard4Factors_verifyCodeFail.yml
    parameters:
        - CaseName-certType-name-idNo-mobileNo-bankCardNo-grade-authcode:
            - ["正常场景-详情版四要素要素-错误验证码","INDIVIDUAL_CH_IDCARD","许华建","342622198905262396","***********","****************","ADVANCED","654321"]

-
    name: 发起银行卡4要素实名认证-$CaseName
    testcase: testcases/psn/bankCard4FactorsOid_verifyCodeSuccess.yml
    parameters:
        - CaseName-accountId-mobileNo-bankCardNo-repetition-grade-authcode-name-VALIDATE1:
            - ["正常场景-简版四要素-正确验证码","3a6d9b55e1d84b1881b560325e643adb","***********","****************",true,"STANDARD","123456","许华建","CRED_PSN_CH_IDCARD"]

-
    name: 发起银行卡4要素实名认证2-$CaseName
    testcase: testcases/psn/bankCard4FactorsOid_verifyCodeFail.yml
    parameters:
        - CaseName-accountId-mobileNo-bankCardNo-repetition-grade-authcode-name:
            - ["正常场景-详情版四要素-错误验证码","3a6d9b55e1d84b1881b560325e643adb","***********","****************",true,"ADVANCED","654321","许华建"]

-
    name: 发起银行卡4要素核身认证（境外）-$CaseName
    testcase: testcases/psn/bankCard4Factors_overseas.yml
    parameters:
        - CaseName-certType-idNo-name-bankCardNo-mobileNo-grade:
            - ["正常场景-> 证件类型为空","","110221198409128347","许莹莹","6227000016880001325","***********",""]
            - ["正常场景-> 证件类型为护照","INDIVIDUAL_PASSPORT","H10561683","韩艾珊","622908393301650316","***********",""]
            - ["正常场景-> 证件类型为澳门通行证","INDIVIDUAL_CH_HONGKONG_MACAO","M08596583","刘烱超","6222620780006496542","***********",""]
            - ["正常场景-> 英文名字中间支持空格","INDIVIDUAL_PASSPORT","*********","FAN HON SING","****************","***********",""]
