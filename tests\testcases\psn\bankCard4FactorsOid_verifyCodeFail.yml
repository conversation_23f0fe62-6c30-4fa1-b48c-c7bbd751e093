config:
    name: 发起银行卡4要素实名认证，校验短信验证码
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起银行卡4要素实名认证
    api: api/psnAuth/bankCard4FactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验短信验证码-错误验证码
    api: api/psnAuth/bank4VerifyCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, ********]
      - contains: [content.message, "验证码校验失败"]

-
    name: 银行卡四要素单发送验证码
    api: api/psnAuth/bankCard4FactorAuthCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.subFlows.0.subFlowType, "INDIVIDUAL_BANKCARD_4_FACTOR"]
      - eq: [content.data.subFlows.0.status, "ING"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.authType, "INDIVIDUAL_BANKCARD_4_FACTOR"]
      - eq: [content.data.indivInfo.name, $name]


-
    name: 根据flowId查询其认证子service的详情 对内服务GET方式
    api: api/public/queryFlowServiceInnerLineGet.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "进行中"]
      - eq: [content.data.objectType, "个人"]
      - eq: [content.data.subFlows.0.subFlowType, "个人银行卡四要素"]
      - eq: [content.data.subFlows.0.name, $name]

-
    name: 根据flowId查询其认证子service的详情 对内服务Post方式
    api: api/public/queryFlowServiceInnerLinePost.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "进行中"]
      - eq: [content.data.objectType, "个人"]
      - eq: [content.data.subFlows.0.subFlowType, "个人银行卡四要素"]
      - eq: [content.data.subFlows.0.name, $name]

-
    name: 获取verifyCode以及flow的状态
    api: api/psnAuth/getVerifyCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "ING"]