config:
    name: 获取组织机构实名认证地址V2-反向打款认证
    base_url: ${ENV(base_url_rpc)}

teststeps:
#创建企业oid，发起实名；经办人意愿-短信意愿；企业发起四要素，四要素完成后直接完成认证
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlV2.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.url, null]

-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版获取企业信息
    api: api/orgAuth/queryOrganizationInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.agentName, null]
      - eq: [content.data.certNo, $idNumber]
      - eq: [content.data.legalRepCertNo, $orgLegalIdNumber]
      - eq: [content.data.legalRepName, $orgLegalName]
      - eq: [content.data.name, $name]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.display, null]

-
    name: 查询企业实名流程进度1
    api: api/orgAuth/getOrgProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.ifInfoAuthSuccessful, false]
      - eq: [content.data.status, "INIT"]
      - eq: [content.data.type, "ORGANIZATION_INFO_AUTH"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 企业经办人认证状态查询
    api: api/orgAuth/getAgentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.authorizedInOrgFlow, False]
      - eq: [content.data.authorizedInV21, True]
      - eq: [content.data.authorisedInUserCenter, True]
      - eq: [content.data.status, "WILLINGNESS_REQUIRED"]

-
    name: 获取经办人意愿方式
    api: api/psnAuth/getWillingnessType.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.willAuthType, null]

-
    name: 查询意愿手机号
    api: api/psnAuth/queryWillingnessMobileNo.yml
    extract:
      - receiver: content.data.mobileNo
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]

-
    name: 发起意愿手机验证码认证
    api: api/psnAuth/sendWillingnessMobileCodeauth.yml
    variables:
      sendType: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验意愿手机验证码认证-正确验证码
    api: api/psnAuth/verifyWillingnessMobileCodeauth.yml
    variables:
      authCode: "123456"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询企业实名流程进度1
    api: api/orgAuth/getOrgProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.ifInfoAuthSuccessful, false]
      - eq: [content.data.status, "INIT"]
      - eq: [content.data.type, "ORGANIZATION_INFO_AUTH"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版企业信息比对接口
    api: api/orgAuth/infoVerify.yml
    variables:
      - version: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]
      - eq: [content.data.operatorType, 1]

-
    name: 查询企业实名流程进度2
    api: api/orgAuth/getOrgProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.type, "WILL_AUTHCODE"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.ifInfoAuthSuccessful, True]
