config:
    name: 添加修改删除地区
    base_url: ${ENV(base_url_realname)}

teststeps:
-
    name: 添加地区
    api: api/realname/addBankCityDistrict.yml
    validate:
      - eq: [status_code,200]

-
    name: 查询地区
    api: api/realname/queryBankCityDistrict.yml
    variables:
      district_query: $district
    extract:
      - id: content.0.id
    validate:
      - eq: [status_code,200]
      - eq: [content.0.district, $district]
      - eq: [content.0.districtCode, $districtCode]
      - eq: [content.0.parentCode, $parentCode]

-
    name: 修改地区
    api: api/realname/updateBankCityDistrict.yml
    validate:
      - eq: [status_code,200]


-
    name: 查询地区
    api: api/realname/queryBankCityDistrict.yml
    variables:
      district_query: $district_update
    validate:
      - eq: [status_code,200]
      - eq: [content.0.district, $district_update]
      - eq: [content.0.districtCode, $districtCode]
      - eq: [content.0.parentCode, $parentCode]

-
    name: merge地区
    api: api/realname/mergeBankCityDistrict.yml
    validate:
      - eq: [status_code,200]

-
    name: 查询地区
    api: api/realname/queryBankCityDistrict.yml
    variables:
      district_query: $district
    validate:
      - eq: [status_code,200]
      - eq: [content.0.district, $district]
      - eq: [content.0.districtCode, $districtCode]
      - eq: [content.0.parentCode, $parentCode]

-
    name: 删除地区
    api: api/realname/deleteBankCityDistrict.yml
    validate:
      - eq: [status_code,200]
      - eq: [content, true]
