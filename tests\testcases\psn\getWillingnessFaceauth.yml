config:
    name: 获取个人意愿刷脸结果
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取个人意愿刷脸结果
    api: api/psnAuth/getWillingnessFaceauth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.result.0.faceAuthMode, $VALIDATE1]
      - eq: [content.data.result.0.passed, $VALIDATE2]
      - eq: [content.data.result.0.status, $VALIDATE3]
      - eq: [content.data.result.0.message, $VALIDATE4]
