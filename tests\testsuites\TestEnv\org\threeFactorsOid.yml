config:
    name: 发起企业实名认证3要素校验

testcases:
-
    name: 发起企业实名认证3要素校验-法人授权书认证-$CaseName
    testcase: testcases/org/threeFactorsOid_legalRepSign.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-repetition-mobileNo-legalRepIdNo-VALIDATE1:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",true,"***********","330702198409120432","INDIVIDUAL_CH_IDCARD"]
            - ["企业信息都正确(企业名称中支持特殊字符)","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","110108196710262291","何一兵",true,"***********","110108196710262291","INDIVIDUAL_CH_IDCARD"]

-
    name: 发起企业实名认证3要素校验-随机金额打款认证-$CaseName
    testcase: testcases/org/threeFactorsOid_transferRandomAmount.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-repetition-code-message-cardNo-subbranch-amount-bank:
#            - [ "企业信息都正确(常规企业)-已开启配置项authShowAgreement","${ENV(appId10)}","${ENV(psnId_zhima)}","esigntest乌拉拉","CRED_ORG_USCC","9100000032906368N4","******************","王淼",true,0,"成功","**************","平安银行杭州高新支行","0.01","平安银行" ]
            - ["企业信息都正确(常规企业)-未开启配置项authShowAgreement","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",true,********,"打款实名未开启上传授权书配置项","**************","平安银行杭州高新支行","0.01","平安银行"]
            - ["企业信息都正确(企业名称中支持特殊字符)-未开启配置项authShowAgreement","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","110108196710262291","何一兵",true,********,"打款实名未开启上传授权书配置项","**************","平安银行杭州高新支行","0.01","平安银行"]

-
    name: 发起企业实名认证3要素校验-反向打款认证-$CaseName
    testcase: testcases/org/threeFactorsOid_reversePayment.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-repetition:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",true]
            - ["企业信息都正确(企业名称中支持特殊字符)","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","110108196710262291","何一兵",true]

-
    name: 发起企业实名认证3要素校验-企业支付宝一键实名认证-$CaseName
    testcase: testcases/org/threeFactorsOid_alipayOneclickConfirm.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-repetition-device-token-authCodeAlipay-orgCode:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","杭州易签宝网络科技有限公司","CRED_ORG_USCC","91330108MA2GKFYB1X","330821198202051817","程亮",true,"PC","xingchenAccessTokenAO001","xingchenAuthcodeZbf001","91330108MA2GKFYB1X"]
            - ["企业信息都正确(特殊字符都支持，全半角括号兼容)","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","330702198409120432","张晋",true,"PC","xingchenAccessTokenAO005","xingchenAuthcodeZbf005","91330108MA2G042101"]
