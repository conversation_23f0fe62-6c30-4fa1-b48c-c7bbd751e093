name: 获取企业实名认证地址
request:
    url: /v2/identity/auth/web/$accountId/orgIdentityUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
      authType: "ORG_BANK_TRANSFER"
      availableAuthTypes:
        - "ORG_BANK_TRANSFER"
        - "ORG_ZM_AUTHORIZE"
      agentAccountId: "ca991b71dcxxxx"
      contextInfo:
        contextId: "*****************"
        notifyUrl: "http://baidu.com"
        origin: "BROWSER"
        redirectUrl: "http://baidu.com"
        showResultPage: "true"
      orgEntity:
        certNo: "91440xxxxxxx"
        organizationType: "1"
        name: "天谷信息科技有限公司"
        legalRepCertNo: "37140xxxxx"
        legalRepName: "xxx"
      repeatIdentity: "true"