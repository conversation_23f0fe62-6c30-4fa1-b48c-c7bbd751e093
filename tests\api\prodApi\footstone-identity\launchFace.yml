name: 发起个人刷脸核身认证api
request:
    url: /v2/identity/auth/api/individual/face
    method: POST
#    headers:
#        Content-Type: application/json
#        X-Tsign-Open-App-Id: ${ENV(appId)}
#        X-Tsign-Open-Auth-Mode: simple
    headers: ${gen_headers2()}
    json:
        name: $name
        idNo: $idNo
        faceauthMode: $faceauthMode
        contextId: ""
        notifyUrl: ${ENV(notifyUrl)}
        callbackUrl: $callbackUrl
