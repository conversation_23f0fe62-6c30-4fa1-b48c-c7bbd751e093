# 查询余量接口新增认证余额查询功能-测试用例

## 功能测试

### 认证余额查询功能

#### TL-传入orderType为AUTH时正确返回认证余额

##### PD-前置条件：用户已登录；具有查询余量权限；认证余额服务正常；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为AUTH

##### 步骤三：发送请求

##### 步骤四：接收响应结果

##### ER-预期结果：1：接口调用成功，返回状态码200；2：响应中包含认证余额信息；3：余额数据格式正确；4：余额值为实际认证账户余额；

#### TL-AUTH枚举值大小写敏感性验证

##### PD-前置条件：用户已登录；具有查询余量权限；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为auth（小写）

##### 步骤三：发送请求

##### 步骤四：检查响应结果

##### ER-预期结果：1：接口返回参数错误提示；2：错误码为参数无效；3：错误信息明确指出orderType值无效；

#### TL-验证AUTH类型与其他orderType类型返回数据的区别

##### PD-前置条件：用户已登录；具有查询余量权限；系统中存在多种余额类型；

##### 步骤一：调用查询余量接口设置orderType为AUTH

##### 步骤二：记录返回的认证余额

##### 步骤三：调用查询余量接口设置orderType为其他有效类型

##### 步骤四：对比两次返回的余额数据

##### ER-预期结果：1：两次接口调用均成功；2：返回的余额类型不同；3：AUTH返回认证余额；4：数据结构一致但余额值不同；

### 参数验证功能

#### TL-orderType参数为空时的处理验证

##### PD-前置条件：用户已登录；具有查询余量权限；

##### 步骤一：调用查询余量接口

##### 步骤二：不传入orderType参数或设置为空

##### 步骤三：发送请求

##### 步骤四：检查响应结果

##### ER-预期结果：1：接口返回参数错误；2：错误码为必填参数缺失；3：错误信息提示orderType为必填参数；

#### TL-orderType参数为无效枚举值时的处理验证

##### PD-前置条件：用户已登录；具有查询余量权限；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为INVALID_TYPE

##### 步骤三：发送请求

##### 步骤四：检查响应结果

##### ER-预期结果：1：接口返回参数错误；2：错误码为参数值无效；3：错误信息列出有效的orderType枚举值；

## 边界测试

### 数据边界验证

#### TL-认证余额为0时的返回验证

##### PD-前置条件：用户已登录；认证账户余额为0；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为AUTH

##### 步骤三：发送请求

##### 步骤四：检查返回的余额值

##### ER-预期结果：1：接口调用成功；2：返回认证余额为0；3：数据格式正确；4：无异常错误信息；

#### TL-认证余额为最大值时的返回验证

##### PD-前置条件：用户已登录；认证账户余额为系统最大值；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为AUTH

##### 步骤三：发送请求

##### 步骤四：检查返回的余额值

##### ER-预期结果：1：接口调用成功；2：正确返回最大余额值；3：数值精度无丢失；4：格式显示正常；

## 异常测试

### 系统异常处理

#### TL-认证余额服务不可用时的异常处理

##### PD-前置条件：用户已登录；认证余额服务暂时不可用；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为AUTH

##### 步骤三：发送请求

##### 步骤四：检查异常处理结果

##### ER-预期结果：1：接口返回服务异常错误；2：错误码为服务不可用；3：错误信息友好提示；4：不返回错误的余额数据；

### 权限异常处理

#### TL-无查询权限用户访问认证余额的处理

##### PD-前置条件：用户已登录；用户无查询余量权限；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为AUTH

##### 步骤三：发送请求

##### 步骤四：检查权限验证结果

##### ER-预期结果：1：接口返回权限不足错误；2：错误码为无权限访问；3：不返回任何余额信息；4：错误信息明确说明权限不足；

## 性能测试

### 响应时间验证

#### TL-认证余额查询响应时间验证

##### PD-前置条件：系统正常运行；网络状况良好；

##### 步骤一：记录请求开始时间

##### 步骤二：调用查询余量接口设置orderType为AUTH

##### 步骤三：记录响应结束时间

##### 步骤四：计算响应时间

##### ER-预期结果：1：接口调用成功；2：响应时间小于2秒；3：返回正确的认证余额；4：响应时间稳定；

### 并发性能验证

#### TL-多用户并发查询认证余额的性能验证

##### PD-前置条件：系统正常运行；准备多个测试账户；

##### 步骤一：启动100个并发线程

##### 步骤二：每个线程调用查询余量接口设置orderType为AUTH

##### 步骤三：统计所有请求的响应时间

##### 步骤四：检查系统稳定性

##### ER-预期结果：1：所有请求均成功响应；2：平均响应时间小于3秒；3：系统无异常错误；4：返回数据准确；

## 安全测试

### 接口安全验证

#### TL-orderType参数SQL注入攻击防护验证

##### PD-前置条件：用户已登录；具有查询权限；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为"AUTH'; DROP TABLE users; --"

##### 步骤三：发送请求

##### 步骤四：检查系统安全性

##### ER-预期结果：1：接口返回参数错误；2：系统无异常行为；3：数据库表结构完整；4：安全日志记录攻击行为；

## 兼容性测试

### 向后兼容性验证

#### TL-原有orderType枚举值功能正常性验证

##### PD-前置条件：用户已登录；系统支持原有orderType类型；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为原有有效枚举值

##### 步骤三：发送请求

##### 步骤四：验证功能正常性

##### ER-预期结果：1：接口调用成功；2：返回对应类型的余额；3：功能与升级前一致；4：无兼容性问题；

#### TL-接口文档中AUTH枚举说明验证

##### PD-前置条件：接口文档已更新；开发人员可访问文档；

##### 步骤一：查看接口文档

##### 步骤二：找到orderType参数说明

##### 步骤三：确认AUTH枚举值的描述

##### 步骤四：验证示例代码正确性

##### ER-预期结果：1：文档中包含AUTH枚举说明；2：参数描述准确完整；3：示例代码可正常执行；4：返回值说明清晰；

### 多版本兼容性验证

#### TL-不同API版本对AUTH支持的兼容性验证

##### PD-前置条件：系统支持多个API版本；各版本环境可用；

##### 步骤一：使用最新版本API调用接口

##### 步骤二：设置orderType为AUTH

##### 步骤三：使用旧版本API调用相同接口

##### 步骤四：对比两个版本的处理结果

##### ER-预期结果：1：新版本支持AUTH枚举；2：旧版本返回不支持错误或忽略；3：版本兼容性处理正确；4：错误信息友好提示；

## 冒烟测试用例

### 核心功能验证

#### MYTL-传入orderType为AUTH时正确返回认证余额

##### PD-前置条件：用户已登录；具有查询余量权限；认证余额服务正常；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为AUTH

##### 步骤三：发送请求

##### 步骤四：接收响应结果

##### ER-预期结果：1：接口调用成功，返回状态码200；2：响应中包含认证余额信息；3：余额数据格式正确；4：余额值为实际认证账户余额；

#### MYTL-orderType参数为无效枚举值时的处理验证

##### PD-前置条件：用户已登录；具有查询余量权限；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为INVALID_TYPE

##### 步骤三：发送请求

##### 步骤四：检查响应结果

##### ER-预期结果：1：接口返回参数错误；2：错误码为参数值无效；3：错误信息列出有效的orderType枚举值；

#### MYTL-认证余额为0时的返回验证

##### PD-前置条件：用户已登录；认证账户余额为0；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为AUTH

##### 步骤三：发送请求

##### 步骤四：检查返回的余额值

##### ER-预期结果：1：接口调用成功；2：返回认证余额为0；3：数据格式正确；4：无异常错误信息；

#### MYTL-无查询权限用户访问认证余额的处理

##### PD-前置条件：用户已登录；用户无查询余量权限；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为AUTH

##### 步骤三：发送请求

##### 步骤四：检查权限验证结果

##### ER-预期结果：1：接口返回权限不足错误；2：错误码为无权限访问；3：不返回任何余额信息；4：错误信息明确说明权限不足；

#### MYTL-认证余额查询响应时间验证

##### PD-前置条件：系统正常运行；网络状况良好；

##### 步骤一：记录请求开始时间

##### 步骤二：调用查询余量接口设置orderType为AUTH

##### 步骤三：记录响应结束时间

##### 步骤四：计算响应时间

##### ER-预期结果：1：接口调用成功；2：响应时间小于2秒；3：返回正确的认证余额；4：响应时间稳定；

#### MYTL-原有orderType枚举值功能正常性验证

##### PD-前置条件：用户已登录；系统支持原有orderType类型；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为原有有效枚举值

##### 步骤三：发送请求

##### 步骤四：验证功能正常性

##### ER-预期结果：1：接口调用成功；2：返回对应类型的余额；3：功能与升级前一致；4：无兼容性问题；

## 线上验证用例

### 生产环境核心功能验证

#### PATL-生产环境AUTH认证余额查询功能验证

##### PD-前置条件：生产环境部署完成；用户已登录；具有查询余量权限；

##### 步骤一：在生产环境调用查询余量接口

##### 步骤二：设置orderType参数为AUTH

##### 步骤三：发送请求并记录响应

##### 步骤四：验证返回的认证余额数据

##### ER-预期结果：1：接口调用成功；2：返回真实的认证余额；3：响应时间符合性能要求；4：数据格式正确；

#### PATL-生产环境参数验证功能验证

##### PD-前置条件：生产环境部署完成；用户已登录；

##### 步骤一：调用查询余量接口

##### 步骤二：设置orderType参数为无效值

##### 步骤三：发送请求

##### 步骤四：验证错误处理机制

##### ER-预期结果：1：返回参数错误信息；2：错误码正确；3：不影响系统稳定性；4：错误日志正常记录；

#### PATL-生产环境向后兼容性验证

##### PD-前置条件：生产环境部署完成；原有功能正常运行；

##### 步骤一：使用原有orderType调用接口

##### 步骤二：验证原有功能正常

##### 步骤三：使用新增AUTH类型调用接口

##### 步骤四：验证新功能正常

##### ER-预期结果：1：原有功能不受影响；2：新功能正常工作；3：系统稳定运行；4：无兼容性问题；

#### PATL-生产环境性能表现验证

##### PD-前置条件：生产环境正常运行；有真实用户访问；

##### 步骤一：监控接口调用量

##### 步骤二：记录AUTH查询的响应时间

##### 步骤三：观察系统资源使用情况

##### 步骤四：验证并发处理能力

##### ER-预期结果：1：响应时间在可接受范围内；2：系统资源使用正常；3：并发处理无异常；4：用户体验良好；

#### PATL-生产环境安全性验证

##### PD-前置条件：生产环境部署完成；安全监控正常；

##### 步骤一：尝试无权限访问

##### 步骤二：验证权限控制机制

##### 步骤三：检查安全日志记录

##### 步骤四：确认数据安全性

##### ER-预期结果：1：权限控制有效；2：安全日志完整；3：敏感数据保护到位；4：无安全漏洞；

#### PATL-生产环境异常处理验证

##### PD-前置条件：生产环境运行；监控系统正常；

##### 步骤一：模拟服务异常情况

##### 步骤二：调用AUTH查询接口

##### 步骤三：观察系统异常处理

##### 步骤四：验证恢复机制

##### ER-预期结果：1：异常处理机制有效；2：错误信息友好；3：系统快速恢复；4：不影响其他功能；

### 数量校验
- 总用例数：20条
- 冒烟用例数：6条（30%）
- 线上验证用例数：6条（30%）
