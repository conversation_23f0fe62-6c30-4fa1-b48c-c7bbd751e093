config:
    name: 消费人工审核rpc接口
    base_url: ${ENV(base_url_rpc)}

teststeps:
-
    name: 消费人工审核rpc
    api: api/rpc/pushManualResult_result.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data, 0]
    json: {
        "transactionId": "3917638354812273796",
        "serviceId": "cf41379c-f262-4bf3-8eb2-ab00d8bed07d",
        "pass": false,
        "authDesc": "人工实名",
        "serviceType": 3,
        "failReason": "集测"
    }
