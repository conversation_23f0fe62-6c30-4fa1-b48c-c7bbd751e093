# 搭售商品组合下单功能-测试用例

## 功能测试

### 搭售商品展示功能

#### TL-搭售商品信息正确展示验证

##### PD-前置条件：用户已登录；主商品已配置搭售活动；搭售活动状态为启用；

##### 步骤一：进入主商品详情页面

##### 步骤二：查看搭售商品展示区域

##### 步骤三：检查搭售商品展示字段

##### 步骤四：验证专属特惠标识显示

##### ER-预期结果：1：搭售商品正确展示商品名称、商品价格、商品原价；2：优惠标识正确显示；3：专属特惠标识醒目展示；4：价格信息准确无误；

#### TL-不同优惠方式下搭售商品展示验证

##### PD-前置条件：用户已登录；主商品配置了打折和直减两种搭售优惠；

##### 步骤一：查看配置打折优惠的搭售商品

##### 步骤二：验证打折优惠展示效果

##### 步骤三：查看配置直减优惠的搭售商品

##### 步骤四：验证直减优惠展示效果

##### ER-预期结果：1：打折优惠正确显示折扣比例和优惠后价格；2：直减优惠正确显示减免金额和优惠后价格；3：优惠标识区分明确；4：价格计算准确；

#### TL-多端搭售商品展示一致性验证

##### PD-前置条件：用户已登录；同一搭售活动在多个端配置；

##### 步骤一：在微信小程序查看搭售商品展示

##### 步骤二：在支付宝小程序查看搭售商品展示

##### 步骤三：在PC端查看搭售商品展示

##### 步骤四：在APP端查看搭售商品展示

##### ER-预期结果：1：各端搭售商品信息展示一致；2：价格和优惠信息完全相同；3：界面布局适配各端特点；4：交互体验流畅；

### 用户权限验证功能

#### TL-个人账号已挂靠销售下单权限验证

##### PD-前置条件：个人账号已登录；账号已挂靠销售；配置需校验挂靠信息；

##### 步骤一：选择主商品和搭售商品

##### 步骤二：点击立即购买

##### 步骤三：系统进行权限校验

##### 步骤四：查看系统提示信息

##### ER-预期结果：1：系统提示不可下单搭售商品；2：仅允许购买主商品；3：提示信息明确说明原因；4：用户可正常购买主商品；

#### TL-个人账号未挂靠销售下单权限验证

##### PD-前置条件：个人账号已登录；账号未挂靠销售；配置需校验挂靠信息；首次使用搭售功能；

##### 步骤一：选择主商品和搭售商品

##### 步骤二：点击立即购买

##### 步骤三：系统进行权限校验

##### 步骤四：完成下单流程

##### ER-预期结果：1：系统允许下单搭售商品；2：下单流程正常进行；3：订单包含主商品和搭售商品；4：搭售优惠正确应用；

#### TL-企业账号权限验证

##### PD-前置条件：企业账号已登录；配置需校验挂靠信息；

##### 步骤一：使用已挂靠销售的企业账号下单

##### 步骤二：验证系统权限控制

##### 步骤三：使用未挂靠销售的企业账号下单

##### 步骤四：验证下单权限差异

##### ER-预期结果：1：已挂靠企业账号不可下单搭售商品；2：未挂靠企业账号可下单搭售商品；3：权限提示信息准确；4：主商品购买不受影响；

### 组合下单功能

#### TL-主商品和搭售商品组合下单流程验证

##### PD-前置条件：用户已登录；具有搭售商品下单权限；

##### 步骤一：选择主商品

##### 步骤二：选择搭售商品

##### 步骤三：确认商品组合

##### 步骤四：点击立即购买

##### 步骤五：进入订单确认页面

##### ER-预期结果：1：订单包含主商品和搭售商品；2：价格计算正确；3：优惠金额正确显示；4：订单信息完整准确；

#### TL-搭售商品可选择性验证

##### PD-前置条件：用户已登录；主商品配置多个搭售商品；

##### 步骤一：进入主商品页面

##### 步骤二：查看多个搭售商品选项

##### 步骤三：选择部分搭售商品

##### 步骤四：取消选择某些搭售商品

##### 步骤五：确认最终选择并下单

##### ER-预期结果：1：用户可自由选择搭售商品；2：可取消已选择的搭售商品；3：订单金额实时更新；4：最终订单包含用户选择的商品；

### 支付功能

#### TL-合并支付功能验证

##### PD-前置条件：用户已完成组合下单；选择合并支付方式；

##### 步骤一：在订单确认页面选择合并支付

##### 步骤二：选择支付方式

##### 步骤三：确认支付金额

##### 步骤四：完成支付操作

##### ER-预期结果：1：主商品和搭售商品合并为一笔支付；2：支付金额为两者总和；3：支付成功后生成统一支付记录；4：订单状态同步更新；

#### TL-分开支付功能验证

##### PD-前置条件：用户已完成组合下单；选择分开支付方式；

##### 步骤一：在订单确认页面选择分开支付

##### 步骤二：先支付主商品订单

##### 步骤三：再支付搭售商品订单

##### 步骤四：验证两笔支付记录

##### ER-预期结果：1：生成两个独立的支付订单；2：可分别选择不同支付方式；3：支付成功后生成两条支付记录；4：订单状态独立更新；

### 订单生效功能

#### TL-指定时间生效订单验证

##### PD-前置条件：用户已完成支付；选择指定时间生效；

##### 步骤一：设置订单生效时间

##### 步骤二：完成支付流程

##### 步骤三：等待指定时间到达

##### 步骤四：验证订单生效状态

##### ER-预期结果：1：主商品和搭售商品订单同时生效；2：生效时间与设置时间一致；3：订单状态正确更新为已生效；4：用户收到生效通知；

#### TL-续延生效订单验证

##### PD-前置条件：用户已完成支付；选择续延生效；用户有现有有效订单；

##### 步骤一：选择续延生效方式

##### 步骤二：系统计算续延时间

##### 步骤三：完成支付流程

##### 步骤四：验证订单生效时间

##### ER-预期结果：1：新订单在现有订单到期后生效；2：主商品和搭售商品生效时间一致；3：续延时间计算准确；4：订单状态正确显示；

### 功能闭环场景验证

#### TL-新用户完整购买流程闭环验证

##### PD-前置条件：新注册用户；未使用过搭售功能；具有下单权限；

##### 步骤一：用户登录系统

##### 步骤二：浏览主商品页面

##### 步骤三：查看搭售商品推荐

##### 步骤四：选择主商品和搭售商品

##### 步骤五：完成下单和支付

##### 步骤六：等待订单生效

##### 步骤七：验证服务开通

##### ER-预期结果：1：新用户可顺利完成完整购买流程；2：搭售商品优惠正确应用；3：订单生效时间一致；4：服务正常开通使用；

#### TL-老用户再次购买流程验证

##### PD-前置条件：已使用过搭售功能的用户；账号状态正常；

##### 步骤一：老用户登录系统

##### 步骤二：尝试再次使用搭售功能

##### 步骤三：验证系统限制提示

##### 步骤四：购买主商品

##### 步骤五：完成支付流程

##### ER-预期结果：1：系统正确识别用户已使用过搭售功能；2：不允许重复使用搭售优惠；3：主商品购买流程正常；4：用户体验友好；

#### TL-多功能模块交叉组合场景验证

##### PD-前置条件：用户已登录；主商品配置多种优惠；搭售商品配置优惠；

##### 步骤一：选择有折扣优惠的主商品

##### 步骤二：选择有直减优惠的搭售商品

##### 步骤三：应用用户优惠券

##### 步骤四：选择合并支付

##### 步骤五：完成支付流程

##### ER-预期结果：1：多种优惠可正确叠加使用；2：价格计算准确无误；3：优惠明细清晰展示；4：支付金额正确；

## 边界测试

### 优惠配置边界

#### TL-优惠展示方式边界验证

##### PD-前置条件：管理员已登录；具有优惠配置权限；

##### 步骤一：配置优惠展示方式为"下单直接优惠"

##### 步骤二：尝试配置搭售优惠

##### 步骤三：配置优惠展示方式为"输入优惠码后优惠"

##### 步骤四：尝试配置搭售优惠

##### ER-预期结果：1："下单直接优惠"方式支持配置搭售优惠；2："输入优惠码后优惠"方式不支持配置搭售优惠；3：系统给出明确的配置限制提示；4：配置规则严格执行；

#### TL-优惠方式边界验证

##### PD-前置条件：管理员已登录；优惠展示方式为"下单直接优惠"；

##### 步骤一：配置打折优惠方式的搭售活动

##### 步骤二：配置直减优惠方式的搭售活动

##### 步骤三：尝试配置其他优惠方式的搭售活动

##### 步骤四：验证配置结果

##### ER-预期结果：1：打折和直减优惠方式支持搭售配置；2：其他优惠方式不支持搭售配置；3：系统阻止不支持的配置操作；4：错误提示信息准确；

### 用户使用次数边界

#### TL-账号使用次数限制验证

##### PD-前置条件：用户已登录；具有搭售商品下单权限；

##### 步骤一：首次使用搭售功能下单

##### 步骤二：完成支付流程

##### 步骤三：再次尝试使用搭售功能

##### 步骤四：验证系统限制提示

##### ER-预期结果：1：首次使用搭售功能成功；2：第二次使用时系统提示已达使用限制；3：不允许重复使用搭售优惠；4：主商品购买不受影响；

#### TL-不同账号类型使用次数验证

##### PD-前置条件：准备个人账号和企业账号；均未使用过搭售功能；

##### 步骤一：个人账号使用搭售功能

##### 步骤二：企业账号使用搭售功能

##### 步骤三：验证使用次数独立计算

##### 步骤四：分别尝试第二次使用

##### ER-预期结果：1：不同账号类型使用次数独立计算；2：每种账号类型均只能使用一次；3：限制规则对所有账号类型生效；4：系统正确区分账号类型；

### 商品数量和金额边界

#### TL-搭售商品数量边界验证

##### PD-前置条件：用户已登录；主商品配置多个搭售商品；

##### 步骤一：选择最大数量的搭售商品

##### 步骤二：验证系统处理能力

##### 步骤三：尝试超出限制的搭售商品选择

##### 步骤四：验证边界控制

##### ER-预期结果：1：系统支持合理数量的搭售商品选择；2：超出限制时给出明确提示；3：订单金额计算准确；4：性能表现良好；

#### TL-订单金额边界验证

##### PD-前置条件：用户已登录；配置高价值搭售商品；

##### 步骤一：选择高价值主商品和搭售商品

##### 步骤二：验证订单总金额计算

##### 步骤三：测试支付系统处理能力

##### 步骤四：验证订单生成

##### ER-预期结果：1：高金额订单正确处理；2：价格计算精确无误；3：支付流程正常；4：订单记录完整；

## 异常测试

### 系统异常处理

#### TL-接口异常处理验证

##### PD-前置条件：用户已登录；系统运行正常；

##### 步骤一：调用搭售商品查询接口

##### 步骤二：模拟接口返回异常

##### 步骤三：验证前端异常处理

##### 步骤四：检查用户体验

##### ER-预期结果：1：接口异常时显示友好错误提示；2：不影响主商品正常购买；3：系统记录异常日志；4：用户可重试操作；

#### TL-数据库异常处理验证

##### PD-前置条件：用户进行下单操作；

##### 步骤一：模拟数据库连接异常

##### 步骤二：验证系统异常处理

##### 步骤三：检查数据一致性

##### 步骤四：验证恢复机制

##### ER-预期结果：1：数据库异常时系统优雅降级；2：不产生脏数据；3：异常恢复后功能正常；4：用户操作可重试；

#### TL-支付异常处理验证

##### PD-前置条件：用户已完成下单；进入支付环节；

##### 步骤一：选择支付方式

##### 步骤二：模拟支付失败

##### 步骤三：验证系统处理逻辑

##### 步骤四：检查订单状态

##### ER-预期结果：1：支付失败时订单状态为待支付；2：用户可重新发起支付；3：库存正确处理；4：异常信息准确记录；

### 网络异常处理

#### TL-网络中断异常处理验证

##### PD-前置条件：用户正在进行下单操作；

##### 步骤一：在下单过程中断网络连接

##### 步骤二：验证系统处理机制

##### 步骤三：恢复网络连接

##### 步骤四：检查操作状态

##### ER-预期结果：1：网络中断时显示连接异常提示；2：操作状态正确保存；3：网络恢复后可继续操作；4：不产生重复订单；

#### TL-网络超时异常处理验证

##### PD-前置条件：用户进行支付操作；

##### 步骤一：模拟网络超时情况

##### 步骤二：验证超时处理机制

##### 步骤三：检查支付状态

##### 步骤四：验证重试机制

##### ER-预期结果：1：超时时显示明确提示信息；2：支付状态正确处理；3：提供重试机制；4：避免重复扣款；

### 数据异常处理

#### TL-商品信息异常处理验证

##### PD-前置条件：用户浏览搭售商品；

##### 步骤一：模拟商品信息缺失

##### 步骤二：验证系统处理逻辑

##### 步骤三：检查用户界面展示

##### 步骤四：验证降级方案

##### ER-预期结果：1：商品信息异常时不影响主商品展示；2：搭售区域显示友好提示；3：用户可正常购买主商品；4：异常信息记录完整；

## 性能测试

### 响应时间测试

#### TL-搭售商品查询接口性能验证

##### PD-前置条件：系统运行正常；测试环境稳定；

##### 步骤一：发起搭售商品查询请求

##### 步骤二：记录接口响应时间

##### 步骤三：重复测试100次

##### 步骤四：统计平均响应时间

##### ER-预期结果：1：接口平均响应时间小于2秒；2：95%请求响应时间小于3秒；3：无超时请求；4：系统资源使用正常；

#### TL-下单接口性能验证

##### PD-前置条件：用户已选择商品；准备下单；

##### 步骤一：发起组合下单请求

##### 步骤二：记录下单处理时间

##### 步骤三：重复测试50次

##### 步骤四：分析性能数据

##### ER-预期结果：1：下单平均处理时间小于3秒；2：90%请求在5秒内完成；3：系统稳定无异常；4：数据库性能良好；

### 并发性能测试

#### TL-并发下单性能验证

##### PD-前置条件：准备1000个测试账号；系统资源充足；

##### 步骤一：1000用户同时发起搭售商品下单

##### 步骤二：监控系统响应情况

##### 步骤三：统计成功率和响应时间

##### 步骤四：检查系统稳定性

##### ER-预期结果：1：订单成功率大于99.5%；2：平均响应时间小于5秒；3：系统无崩溃或异常；4：数据一致性正确；

#### TL-并发支付性能验证

##### PD-前置条件：准备500个待支付订单；支付系统正常；

##### 步骤一：500用户同时发起支付请求

##### 步骤二：监控支付处理情况

##### 步骤三：统计支付成功率

##### 步骤四：检查支付数据一致性

##### ER-预期结果：1：支付成功率大于99%；2：平均支付时间小于8秒；3：无重复扣款或漏扣；4：支付记录准确完整；

### 压力测试

#### TL-系统压力极限测试

##### PD-前置条件：测试环境独立；监控工具就绪；

##### 步骤一：逐步增加并发用户数

##### 步骤二：监控系统各项指标

##### 步骤三：找到系统性能拐点

##### 步骤四：验证系统恢复能力

##### ER-预期结果：1：系统在设计负载下稳定运行；2：超负载时优雅降级；3：压力释放后快速恢复；4：关键数据不丢失；

## 安全测试

### 权限控制测试

#### TL-用户权限绕过测试

##### PD-前置条件：已挂靠销售的个人账号；

##### 步骤一：尝试直接调用下单接口

##### 步骤二：修改请求参数绕过前端限制

##### 步骤三：验证后端权限校验

##### 步骤四：检查安全日志

##### ER-预期结果：1：后端严格校验用户权限；2：非法请求被拒绝；3：安全日志记录异常操作；4：系统不存在权限绕过漏洞；

#### TL-接口权限控制验证

##### PD-前置条件：不同权限级别的用户账号；

##### 步骤一：使用无权限账号调用搭售接口

##### 步骤二：使用有权限账号调用接口

##### 步骤三：验证权限控制效果

##### 步骤四：检查权限日志记录

##### ER-预期结果：1：无权限账号请求被拒绝；2：有权限账号正常访问；3：权限验证逻辑正确；4：安全日志完整记录；

### 数据安全测试

#### TL-支付信息安全传输验证

##### PD-前置条件：用户进入支付环节；

##### 步骤一：监控支付数据传输

##### 步骤二：检查数据加密情况

##### 步骤三：验证敏感信息处理

##### 步骤四：检查日志记录

##### ER-预期结果：1：支付信息全程加密传输；2：敏感数据不明文存储；3：日志不包含敏感信息；4：符合数据安全规范；

#### TL-用户数据隐私保护验证

##### PD-前置条件：用户进行搭售商品购买；

##### 步骤一：检查用户数据收集范围

##### 步骤二：验证数据存储安全

##### 步骤三：检查数据传输加密

##### 步骤四：验证数据访问控制

##### ER-预期结果：1：仅收集必要用户数据；2：数据存储符合安全标准；3：数据传输全程加密；4：访问控制严格有效；

### 接口安全测试

#### TL-接口防刷验证

##### PD-前置条件：搭售商品查询接口正常；

##### 步骤一：短时间内大量调用接口

##### 步骤二：验证防刷机制

##### 步骤三：检查系统响应

##### 步骤四：验证恢复机制

##### ER-预期结果：1：系统检测到异常调用；2：触发防刷限制机制；3：正常用户不受影响；4：异常解除后功能恢复；

#### TL-SQL注入防护验证

##### PD-前置条件：搭售商品查询接口；

##### 步骤一：在请求参数中注入SQL语句

##### 步骤二：验证系统防护效果

##### 步骤三：检查数据库安全

##### 步骤四：验证日志记录

##### ER-预期结果：1：SQL注入攻击被成功阻止；2：数据库数据完整无损；3：攻击行为被记录；4：系统运行稳定；

## 兼容性测试

### 多端兼容性

#### TL-微信小程序兼容性验证

##### PD-前置条件：微信小程序环境；用户已登录；

##### 步骤一：在微信小程序完成搭售商品购买流程

##### 步骤二：验证界面展示效果

##### 步骤三：测试交互功能

##### 步骤四：检查支付流程

##### ER-预期结果：1：界面适配微信小程序规范；2：功能完整可用；3：支付流程顺畅；4：用户体验良好；

#### TL-支付宝小程序兼容性验证

##### PD-前置条件：支付宝小程序环境；用户已登录；

##### 步骤一：在支付宝小程序完成搭售商品购买流程

##### 步骤二：验证界面展示效果

##### 步骤三：测试交互功能

##### 步骤四：检查支付流程

##### ER-预期结果：1：界面适配支付宝小程序规范；2：功能完整可用；3：支付流程顺畅；4：用户体验良好；

#### TL-PC端兼容性验证

##### PD-前置条件：PC浏览器环境；用户已登录；

##### 步骤一：在PC端完成搭售商品购买流程

##### 步骤二：验证界面展示效果

##### 步骤三：测试交互功能

##### 步骤四：检查支付流程

##### ER-预期结果：1：界面适配PC端显示；2：功能完整可用；3：支付流程顺畅；4：用户体验良好；

#### TL-移动APP兼容性验证

##### PD-前置条件：安卓和苹果APP环境；用户已登录；

##### 步骤一：在移动APP完成搭售商品购买流程

##### 步骤二：验证界面展示效果

##### 步骤三：测试交互功能

##### 步骤四：检查支付流程

##### ER-预期结果：1：界面适配移动端显示；2：功能完整可用；3：支付流程顺畅；4：用户体验良好；

### 浏览器兼容性

#### TL-主流浏览器兼容性验证

##### PD-前置条件：准备Chrome、Firefox、Safari、Edge浏览器；

##### 步骤一：在Chrome浏览器测试搭售功能

##### 步骤二：在Firefox浏览器测试搭售功能

##### 步骤三：在Safari浏览器测试搭售功能

##### 步骤四：在Edge浏览器测试搭售功能

##### ER-预期结果：1：所有主流浏览器功能正常；2：界面展示一致；3：交互体验良好；4：支付流程顺畅；

### 设备兼容性

#### TL-不同设备型号兼容性验证

##### PD-前置条件：准备不同品牌和型号的移动设备；

##### 步骤一：在iPhone设备测试搭售功能

##### 步骤二：在华为设备测试搭售功能

##### 步骤三：在小米设备测试搭售功能

##### 步骤四：在OPPO设备测试搭售功能

##### ER-预期结果：1：不同设备功能表现一致；2：界面适配良好；3：性能表现稳定；4：用户体验优良；

## 冒烟测试用例

### MYTL-搭售商品基本展示功能验证

#### PD-前置条件：用户已登录；主商品已配置搭售活动；

#### 步骤一：进入主商品详情页面

#### 步骤二：查看搭售商品展示

#### 步骤三：验证基本信息显示

#### ER-预期结果：1：搭售商品正确展示；2：价格信息准确；3：优惠标识清晰；

### MYTL-用户权限基本验证

#### PD-前置条件：个人账号已登录；未挂靠销售；

#### 步骤一：选择主商品和搭售商品

#### 步骤二：点击立即购买

#### 步骤三：验证下单权限

#### ER-预期结果：1：系统允许下单；2：权限验证正确；3：流程进行顺畅；

### MYTL-组合下单基本流程验证

#### PD-前置条件：用户已登录；具有下单权限；

#### 步骤一：选择主商品和搭售商品

#### 步骤二：确认商品组合

#### 步骤三：完成下单操作

#### ER-预期结果：1：下单流程正常；2：订单信息正确；3：价格计算准确；

### MYTL-合并支付基本功能验证

#### PD-前置条件：用户已完成下单；选择合并支付；

#### 步骤一：选择支付方式

#### 步骤二：确认支付金额

#### 步骤三：完成支付操作

#### ER-预期结果：1：支付流程正常；2：支付金额正确；3：订单状态更新；

### MYTL-订单生效基本验证

#### PD-前置条件：用户已完成支付；选择指定时间生效；

#### 步骤一：设置生效时间

#### 步骤二：等待订单生效

#### 步骤三：验证生效状态

#### ER-预期结果：1：订单按时生效；2：主商品和搭售商品同时生效；3：状态更新正确；

### MYTL-微信小程序基本功能验证

#### PD-前置条件：微信小程序环境；用户已登录；

#### 步骤一：在微信小程序查看搭售商品

#### 步骤二：完成基本下单流程

#### 步骤三：验证支付功能

#### ER-预期结果：1：小程序功能正常；2：界面展示良好；3：支付流程顺畅；

## 线上验证用例

### PATL-搭售商品完整购买流程验证

#### PD-前置条件：线上环境；真实用户账号；

#### 步骤一：用户登录系统

#### 步骤二：浏览并选择搭售商品

#### 步骤三：完成下单和支付

#### 步骤四：验证订单生效

#### ER-预期结果：1：完整流程正常运行；2：用户体验良好；3：订单正确生效；4：服务正常开通；

### PATL-多端功能一致性验证

#### PD-前置条件：线上环境；多个客户端平台；

#### 步骤一：在微信小程序测试搭售功能

#### 步骤二：在支付宝小程序测试搭售功能

#### 步骤三：在PC端测试搭售功能

#### 步骤四：在APP端测试搭售功能

#### ER-预期结果：1：各端功能表现一致；2：数据同步正确；3：用户体验统一；4：支付流程正常；

### PATL-用户权限控制验证

#### PD-前置条件：线上环境；不同类型真实用户账号；

#### 步骤一：使用已挂靠销售账号测试

#### 步骤二：使用未挂靠销售账号测试

#### 步骤三：验证权限控制效果

#### 步骤四：检查系统提示信息

#### ER-预期结果：1：权限控制准确生效；2：提示信息用户友好；3：不影响主商品购买；4：系统运行稳定；

### PATL-支付系统集成验证

#### PD-前置条件：线上环境；真实支付渠道；

#### 步骤一：选择不同支付方式

#### 步骤二：完成真实支付操作

#### 步骤三：验证支付结果

#### 步骤四：检查订单状态

#### ER-预期结果：1：支付渠道正常工作；2：支付结果准确反馈；3：订单状态正确更新；4：资金流转正常；

### PATL-系统性能表现验证

#### PD-前置条件：线上环境；正常业务负载；

#### 步骤一：在业务高峰期测试搭售功能

#### 步骤二：监控系统响应时间

#### 步骤三：验证功能稳定性

#### 步骤四：检查用户体验

#### ER-预期结果：1：高负载下功能正常；2：响应时间在可接受范围；3：系统运行稳定；4：用户体验良好；

### PATL-数据一致性验证

#### PD-前置条件：线上环境；多个数据源；

#### 步骤一：完成搭售商品购买

#### 步骤二：检查订单数据一致性

#### 步骤三：验证支付数据同步

#### 步骤四：确认服务开通状态

#### ER-预期结果：1：各系统数据一致；2：订单信息准确同步；3：支付记录完整；4：服务状态正确；

## 补充测试用例

### 接口参数验证

#### TL-搭售商品查询接口参数验证

##### PD-前置条件：接口测试环境；有效的认证信息；

##### 步骤一：使用正确参数调用接口

##### 步骤二：使用无效gid参数调用接口

##### 步骤三：使用无效saleSchemaId参数调用接口

##### 步骤四：缺少必填参数调用接口

##### ER-预期结果：1：正确参数返回正常数据；2：无效参数返回错误提示；3：缺少参数返回参数错误；4：错误信息准确明确；

#### TL-请求头参数验证

##### PD-前置条件：接口测试环境；

##### 步骤一：使用正确的header参数调用接口

##### 步骤二：缺少X-Tsign-Open-Tenant-Id调用接口

##### 步骤三：缺少x-tsign-client-id调用接口

##### 步骤四：使用无效的client-id调用接口

##### ER-预期结果：1：正确header正常返回；2：缺少必填header返回401错误；3：无效client-id返回认证失败；4：错误码规范统一；

### 搭售活动时效性验证

#### TL-搭售活动有效期验证

##### PD-前置条件：管理员已登录；配置了有时效限制的搭售活动；

##### 步骤一：在活动有效期内查看搭售商品

##### 步骤二：在活动到期后查看搭售商品

##### 步骤三：验证活动状态变化

##### 步骤四：检查用户提示信息

##### ER-预期结果：1：有效期内搭售商品正常展示；2：到期后搭售商品不再展示；3：活动状态正确更新；4：用户收到明确提示；

#### TL-搭售活动库存限制验证

##### PD-前置条件：搭售商品配置了库存限制；

##### 步骤一：查看搭售商品库存数量

##### 步骤二：下单消耗部分库存

##### 步骤三：当库存不足时尝试下单

##### 步骤四：验证库存耗尽后的处理

##### ER-预期结果：1：库存数量实时更新；2：库存不足时限制下单；3：库存耗尽后停止展示；4：库存提示信息准确；

### 数据统计和监控

#### TL-搭售商品使用统计验证

##### PD-前置条件：系统运行正常；有搭售商品购买记录；

##### 步骤一：查看搭售商品使用统计

##### 步骤二：验证统计数据准确性

##### 步骤三：检查统计维度完整性

##### 步骤四：验证数据更新及时性

##### ER-预期结果：1：统计数据准确无误；2：统计维度全面完整；3：数据更新及时；4：报表展示清晰；

#### TL-搭售转化率监控验证

##### PD-前置条件：系统运行正常；有足够的样本数据；

##### 步骤一：查看搭售商品转化率

##### 步骤二：分析不同用户群体转化率

##### 步骤三：验证转化率计算逻辑

##### 步骤四：检查监控告警机制

##### ER-预期结果：1：转化率计算准确；2：用户群体分析合理；3：监控指标完整；4：告警机制有效；