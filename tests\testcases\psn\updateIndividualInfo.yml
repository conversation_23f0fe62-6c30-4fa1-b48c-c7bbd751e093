config:
    name: 保存/更新个人信息
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取个人核身认证地址
    api: api/psnAuth/indivAuthUrl.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 保存/更新个人信息
    api: api/psnAuth/updateIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
    teardown_hooks:
      - ${teardown_hook_sleep_n_secs(3)}

-
    name: 查询个人信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.name, $VALIDATE1]
      - eq: [content.data.certNo, $VALIDATE2]
      - eq: [content.data.mobileNo, $VALIDATE3]
      - eq: [content.data.bankCardNo, $VALIDATE4]
      - eq: [content.data.certType, $VALIDATE5]
