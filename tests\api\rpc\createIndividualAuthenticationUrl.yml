name: RPC获取个人实名认证URL
request:
    url: /createIndividualAuthenticationUrl/request
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        name: $name
        accountId: $accountId
        certType: $certType
        certNo: $certNo
        scope: "GENERAL"
        saleSchemaId: 14
        redirectUrl: "https://www.baidu.com"
        showResultPage: true
        dnsAppId: ${ENV(appId)}
