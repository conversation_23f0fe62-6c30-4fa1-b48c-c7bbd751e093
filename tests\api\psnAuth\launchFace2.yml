name: 发起个人刷脸核身认证api
request:
    url: /v2/identity/auth/api/individual/face
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: $appId
        X-Tsign-Open-Auth-Mode: simple
#        X-Tsign-Service-Group: non-standard-v3
#        X-Tsign-Service-Id: footstone-identity
    json:
        name: $name
        idNo: $idNo
        certType: $certType
        faceauthMode: "ESIGN"
        mobileNo: $mobileNo   #传了则可在刷脸完成后走意愿认证流程
#        certificationPurpose: $certificationPurpose  #用途：ORGANIZATION-企业,INDIVIDUAL-个人(默认)  #用途是个人时，无需传入企业信息
#        orgName: $orgName
#        orgCertNo: $orgCertNo
        contextId: ""
        notifyUrl: ${ENV(notifyUrl)}
