import os,json,yaml,platform,requests,argparse,time,datetime

class TIcheck():

    # description描述这个参数解析器是干什么的，当我们在命令行显示帮助信息的时候会看到description描述的信息
    parser = argparse.ArgumentParser(description="集成测试规范检查")
    # 通过对象的add_argument函数来增加参数。
    # '-n','--name'表示同一个参数,default参数表示在运行命令时若没有提供参数，程序会将此值当做参数值
    parser.add_argument('-n','--name',default="footstone-identity")
    # parser.add_argument('-p','--path',default="/usr/local/timevale/workspace/ITcheck/footstone/footstone/tests/testsuites/signer_check")
    # parser.add_argument('-tp','--testspath',default="/usr/local/timevale/workspace/ITcheck/footstone/footstone/tests")
    parser.add_argument('-p','--path',default=r"D:\jice\footstone-identity\tests\testsuites")
    parser.add_argument('-tp','--testspath',default=r"D:\jice\footstone-identity\tests")

    args = parser.parse_args()

    serviceName = args.name
    suit_path = args.path
    tests_path = args.testspath

    def get_file_path(self,root_path,file_list,dir_list):
        #获取该目录下所有的文件名称和目录名称
        dir_or_files = os.listdir(root_path)
        for dir_file in dir_or_files:
            #获取目录或者文件的路径
            dir_file_path = os.path.join(root_path,dir_file)
            #判断该路径为文件还是路径
            if os.path.isdir(dir_file_path):
                dir_list.append(dir_file_path)
                #递归获取所有文件和目录的路径
                self.get_file_path(dir_file_path,file_list,dir_list)
            else:
                file_list.append(dir_file_path)
        return file_list

    def find_case_path(self,case_path,one_case_list):
        with open(self.tests_path+"/"+case_path,encoding='utf-8') as stream:
             one_yaml_data_case = yaml.safe_load(stream)
        for i in one_yaml_data_case:
            if i.get('test') is not None:
                if i.get('test').get('testcase') is not None:
                    self.find_case_path(i.get('test').get('testcase'),one_case_list)
                else:
                   one_case_list.append(i.get('test'))
        return one_case_list

    def check_testcase(self):

        time_start=time.time()
        files= os.listdir(self.suit_path) #得到文件夹下的所有文件名称
        case_total = 0   #case总数
        assert_less_two = 0  #断言key值少于2个case数
        assert_less_two_case_list = []  #断言key值少于2个case路径集合
        api_list = []
        suite_name_rept = []
        suite_total = 0
        api_url_list = []  #api文件中的url集合
        abnormal_api_list = []  #case层api路径不对的api写法的集合
        file_list = self.get_file_path(self.suit_path,[],[])
        for y_file in file_list:
              if y_file.endswith('.yml'):   #判断是否是.yml结尾的文件
                  with open(y_file,encoding='utf-8') as stream:  #读取文件，读testsuite文件
                        yaml_data_suite = yaml.safe_load(stream)  #yaml转字典，反序列化YAML
                  yaml_data_case = []
                  case_list = []
                  if type(yaml_data_suite) == dict:
                      suite_testcases = yaml_data_suite.get('testcases')
                      suite_name = []
                      for i in suite_testcases:
                          suite_total = suite_total + 1
                          if type(i) == str:
                              one_suite_name = i
                          else:
                              one_suite_name = i.get('name')
                          if one_suite_name not in suite_name:
                              suite_name.append(one_suite_name)
                          else:
                              one_s_name = {}
                              one_s_name['testsuite_path'] = y_file
                              one_s_name['testsuite_name'] = one_suite_name
                              suite_name_rept.append(one_s_name)

                          run_count = 1
                          if type(suite_testcases) == list:
                              one_testcase_path = i['testcase']
                              parameters = i.get('parameters')
                              if parameters is not None:
                                  for u in parameters:
                                      for g in u:
                                          run_count = len(u[g])
                          else:
                              one_testcase_path = suite_testcases[i]['testcase']
                          begin_run_count = 0
                          with open(self.tests_path+"/"+one_testcase_path,encoding='utf-8') as stream:  #读取文件，读testcase文件
                                yaml_data_case = yaml.safe_load(stream)
                          while begin_run_count < run_count:
                              if type(yaml_data_case) == list:
                                  for j in yaml_data_case:
                                      one_test = j.get('test')
                                      if one_test is not None:
                                          if j.get('test').get('testcase') is not None:
                                                case_list = self.find_case_path(j.get('test').get('testcase'),case_list)
                                          else:
                                                case_list.append(one_test)
                                  begin_run_count = begin_run_count + 1
                              else:
                                  for r in yaml_data_case:
                                      if r == 'teststeps':
                                          for p in yaml_data_case[r]:
                                             if p.get('testcase') is not None:
                                                 case_list = self.find_case_path(p.get('testcase'),case_list)
                                             else:
                                                 case_list.append(p)
                                  begin_run_count = begin_run_count + 1
                  else:
                      suite_total = suite_total + 1
                      case_list =  yaml_data_suite
                  for x in case_list:
                      if x.get('suite') is None:
                          if x.get('api') is not None:
                              if 'api/' in x.get('api'):
                                  with open(self.tests_path+"/"+x.get('api'),encoding='utf-8') as stream:  #读取文件，读api文件
                                      yaml_data_api = yaml.safe_load(stream)
                                  #判断同一个api是否有重复的
                                  one_url = yaml_data_api.get('request').get('url').split('?')[0].split('}')
                                  if len(one_url) > 1:
                                      u_url = one_url[1]
                                  else:
                                      u_url = one_url[0]
                                  u_method = yaml_data_api.get('request').get('method')
                                  url_method = u_url + u_method
                                  if url_method not in api_list:
                                      api_list.append(url_method)
                                      one_url_dict = {}
                                      one_url_dict['api'] = u_url
                                      one_url_dict['method'] = u_method
                                      one_url_dict['count'] = 1
                                      one_url_dict['api_path'] = [x.get('api')]
                                      api_url_list.append(one_url_dict)
                                  else:
                                      for y in api_url_list:
                                          if y['api'] == u_url and y['method'] == u_method and x.get('api') not in y['api_path']:
                                              y['count'] = y['count'] + 1
                                              y['api_path'].append(x.get('api'))
                              else:
                                  #判断case层的api写的是否规范
                                  one_abnormal_api = {}
                                  one_abnormal_api['api'] = x.get('api')
                                  one_abnormal_api['name'] = x.get('name')
                                  abnormal_api_list.append(one_abnormal_api)
                          #判断case断言的个数
                          one_dict = {}
                          if x.get('validate') is None:
                              assert_less_two = assert_less_two + 1
                              one_dict['testsuite_path'] = y_file
                              one_dict['onecase_name'] = x.get('name')
                              assert_less_two_case_list.append(one_dict)
                          else:
                              if len(x.get('validate')) < 2:
                                  assert_less_two = assert_less_two + 1
                                  one_dict['testsuite_path'] = y_file
                                  one_dict['onecase_name'] = x.get('name')
                                  assert_less_two_case_list.append(one_dict)
                              else:
                                  one_assertList = []
                                  #判断断言的key值是否存在重复的
                                  for t in x.get('validate'):
                                      for u in t:
                                          if t[u][0] not in one_assertList:
                                              one_assertList.append(t[u][0])
                                  if len(one_assertList) < 2:
                                      assert_less_two = assert_less_two + 1
                                      one_dict['testsuite_path'] = y_file
                                      one_dict['onecase_name'] = x.get('name')
                                      assert_less_two_case_list.append(one_dict)

                          case_total = case_total + 1
        time_end=time.time()
        repeat_api_total = 0
        for g in api_url_list:
            if g['count'] > 1:
                repeat_api_total = repeat_api_total + 1
        statistics = {
            "case_total" : case_total,   #case总数
            "assert_less_two_list": assert_less_two_case_list,  #断言少于两个key值的case集合
            "assert_less_two_total" : assert_less_two,   #断言少于两个key值的case数
            "apis": api_url_list,
            "repeat_api_total": repeat_api_total,
            "case_api_no" : abnormal_api_list,  #所有的异常api集合，从case这一层判断，api层写的不规范集合
            "case_api_no_total": len(abnormal_api_list),
            "suite_name_rept":suite_name_rept,
            "suite_name_rept_total":len(suite_name_rept),
            "suite_total":suite_total
        }
        useTime = (datetime.datetime.fromtimestamp(time_end)-datetime.datetime.fromtimestamp(time_start)).seconds
        result_data = {
            "serviceName": self.serviceName,
            "statistics": statistics,
            "time": str(useTime)+'s'
        }
        print(result_data)
        url = "http://172.20.62.158:5003/data/ITCheckResult"
        header = {"Content-Type": "application/json"}
        requests.post(url=url,headers=header,json=result_data,verify=False)

if __name__=='__main__':
    TIcheck().check_testcase()