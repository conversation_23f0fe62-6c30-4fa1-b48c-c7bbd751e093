config:
    name: 经办人手机和银行卡号修改，更新流程
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取企业实名认证地址1
    api: api/orgAuth/orgIdentityUrlOid2.yml
    variables:
      - agentAccountId: $agentAccountId
      - certNo: ""
      - organizationType: 1
      - name: ""
      - legalRepCertType: ""
      - legalRepCertNo: ""
      - legalRepName: ""
      - repeatIdentity: true
      - showAgreement: true
      - agentBankCardNo: $agentBankCardNo1
      - agentMobile: $agentMobile1
      - orgUneditableInfo: $orgUneditableInfo1
      - orgEditableInfo: $orgEditableInfo1
    extract:
      - flowId1: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 获取企业实名认证地址2-修改主体信息
    api: api/orgAuth/orgIdentityUrlOid2.yml
    variables:
      - agentAccountId: $agentAccountId
      - certNo: ""
      - organizationType: 1
      - name: ""
      - legalRepCertType: ""
      - legalRepCertNo: ""
      - legalRepName: ""
      - repeatIdentity: true
      - showAgreement: true
      - agentBankCardNo: $agentBankCardNo2
      - agentMobile: $agentMobile2
      - orgUneditableInfo: $orgUneditableInfo2
      - orgEditableInfo: $orgEditableInfo2
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, $flowId1]

-
    name: 查询经办人信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.mobileNo, $agentMobile2]
      - eq: [content.data.bankCardNo, $agentBankCardNo2]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - contains: [content.data.infoEditable.orgUneditableInfo, $VALIDATE1]
