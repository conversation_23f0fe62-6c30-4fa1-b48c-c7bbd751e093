import base64
import datetime
import random
import time
import math

import os

app_id = os.environ['app_id']
app_secret = os.environ['app_secret']
open_url = os.environ['base_url']
app_id2 = os.environ['app_id2']
app_secret2 = os.environ['app_secret2']
token = ''
sep = os.path.sep  # 路径分隔符
cur_dir = os.path.abspath('.') + sep

def gen_headers(**kwargs):
    header = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id,
        "X-Tsign-Open-Auth-Mode": "Secret",
        "X-Tsign-Open-App-Secret": app_secret
    }
    headers = {**header, **kwargs}
    return headers


def gen_headers2(**kwargs):
    header = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": app_id2,
        "X-Tsign-Open-Auth-Mode": "Secret",
        "X-Tsign-Open-App-Secret": app_secret2
    }
    headers = {**header, **kwargs}
    return headers

def get_picture_base64():
    picture = open('tests/data/bigPic_ocr.jpg', 'rb')
    pd = picture.read()
    pd64 = base64.b64encode(pd)
    return pd64.decode()


#
# if __name__ == '__main__':
#     get_picture_base64()

def create_randomId():
    nums = math.floor(1e20 * random.random())
    return nums


def create_randomIdStr(length=20):
    nums = math.floor(1e20 * random.random())
    str1 = str(nums)[:length]
    return str1

def create_randomId_num(length=17):
    nums = math.floor(1e20 * random.random())
    nums = str(nums)[:length]
    return nums

def teardown_hook_sleep_n_secs(n_secs):
    """ sleep n seconds after request
    """
    # if response.status_code == 200:
    #     time.sleep(0.1)
    # else:
    #     time.sleep(n_secs)
    time.sleep(n_secs)


def str_to_list(str):
    value = list(str)
    return value


def list_to_str(list):
    result = ''.join(list)
    return result


def parse_url_query_param_value(url, query_key):
    from urllib import parse
    queries = parse.parse_qs(url)
    v = queries.get(query_key, '')
    return v

def getfileKey():
    str = '$d5aba471-b689-4ec1-828d-7e189187b766$177337449'
    return str

def getfileKey_idcard1():
    str = '$723f5984-36a4-47c1-9650-a1cb861e3b97$3553900136'
    return str

def getfileKey_idcard2():
    str = '$4ea69b51-2701-4fd7-b51b-67847c5dc7f5$1189801194'
    return str

# 获取当前时间戳
def get_now_stamp():
    now_stamp = int(time.time()) * 1000
    return now_stamp

# 获取时间戳，参数是0时，是当前时间戳
def get_day_time(n):
    the_date = datetime.datetime.now()  # 当前时间;
    modify_date = (the_date + datetime.timedelta(days=n)).strftime('%Y-%m-%d %H:%M:%S')
    modify_time = time.strptime(modify_date, "%Y-%m-%d %H:%M:%S")  # 将时间转化为数组形式
    print(modify_date)
    modify_stamp = int(round(time.mktime(modify_time)) * 1000)  # 将时间转化为时间戳形式
    print(modify_stamp)
    return modify_stamp