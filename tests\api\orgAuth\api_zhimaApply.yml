name: 发起企业芝麻认证申请
variables:
  flowId:
  certNo: ""  #企业证件号
  device: ""  #客户端类型
  dnsAppId: ""  #动态域名 APPID
  lang: ""  #国际化语言类型
  name: ""  #企业名称
  redirectUrl: ""  #认证完成后页面跳转地址
  timestamp: ""  #请求时间戳，单位精确到毫秒
  version: ""  #请求版本
request:
  headers:
    Content-Type: application/json
    X-Tsign-Open-App-Id: ${ENV(appId_zhima)}
    X-Tsign-Open-Auth-Mode: simple
    X-Tsign-Open-Operator-Id : ${ENV(psnId_zhima)}
  url: /v2/identity/auth/web/organization/$flowId/zhima/apply
  method: post
  json:
    certNo:  $certNo   #企业证件号
    device:  $device   #客户端类型
    dnsAppId:  $dnsAppId   #动态域名 APPID
    lang:  $lang   #国际化语言类型
    name:  $name   #企业名称
    redirectUrl:  $redirectUrl   #认证完成后页面跳转地址
    timestamp:  $timestamp   #请求时间戳，单位精确到毫秒
    version:  $version   #请求版本
