name: 获取组织机构核身认证地址api
request:
    url: /v2/identity/auth/web/orgAuthUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
      authType: $authType
      availableAuthTypes: $availableAuthTypes
      agentAuthAdvancedEnabled: $agentAuthAdvancedEnabled
      contextInfo:
        contextId: "测试"
        notifyUrl: "http://baidu.com"
        origin: "BROWSER"
        redirectUrl: "http://baidu.com"
        showResultPage: "true"
      orgEntity:
        certNo: $certNo
        organizationType: $organizationType
        name: $name
        legalRepCertType: $legalRepCertType
        legalRepCertNo: $legalRepCertNo
        legalRepName: $legalRepName
