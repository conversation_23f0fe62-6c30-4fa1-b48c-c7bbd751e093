config:
    name: 发起企业实名认证3要素校验

testcases:
-
    name: 发起企业实名认证3要素校验-法人授权书认证-$CaseName
    testcase: testcases/org/threeFactorsOid_legalRepSign.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-repetition-mobileNo-legalRepIdNo-VALIDATE1:
            - ["企业信息都正确(常规企业)","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",true,"***********","330702198409120432","INDIVIDUAL_CH_IDCARD"]

-
    name: 发起企业实名认证3要素校验-随机金额打款认证-$CaseName
    testcase: testcases/org/threeFactorsOid_transferRandomAmount.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-repetition-cardNo-subbranch-amount-bank:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",true,"**************","平安银行杭州高新支行","0.01","平安银行"]

-
    name: 发起企业实名认证3要素校验-反向打款认证-$CaseName
    testcase: testcases/org/threeFactorsOid_reversePayment.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-repetition:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",true]

-
    name: 发起企业实名认证3要素校验-企业支付宝一键实名认证-$CaseName
    testcase: testcases/org/threeFactorsOid_alipayOneclickConfirm.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-repetition-device-token-authCodeAlipay-orgCode:
            - ["企业信息都正确(常规企业)","${ENV(creatorOid)}","杭州易签宝网络科技有限公司","CRED_ORG_USCC","91330108MA2GKFYB1X","330821198202051817","程亮",true,"PC","xingchenAccessTokenAO001","xingchenAuthcodeZbf001","91330108MA2GKFYB1X"]
