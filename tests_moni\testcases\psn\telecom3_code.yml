config:
    name: 个人运营商三要素比对(核身)
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起个人运营商三要素
    api: api/psnAuth/telecom3Factors.yml
    extract: 
        - flowId: content.data.flowId
    validate:
        - eq: [content.code, $VALIDATE1]
        - contains: [content.message, $VALIDATE2]

-
    name: 个人运营商三要素验证码回填
    api: api/psnAuth/telecom3VerifyCode.yml
    validate:
        - eq: [content.message, $VALIDATE3]



