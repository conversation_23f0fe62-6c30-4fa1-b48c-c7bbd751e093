config:
    name: 在不同appid下获取组织机构实名认证地址-获得的flowid不同
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取组织机构实名认证地址2-1
    api: api/orgAuth/orgIdentityUrlOid2.yml
    variables:
      - appId: ${ENV(appId2)}
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 获取组织机构实名认证地址2-2
    api: api/orgAuth/orgIdentityUrlOid2.yml
    variables:
      - appId: ${ENV(appId)}
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, $flowId]
