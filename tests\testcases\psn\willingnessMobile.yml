config:
    name: 手机意愿认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询意愿手机号
    api: api/psnAuth/queryWillingnessMobileNo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.mobileNo, null]

-
    name: 发起意愿手机验证码认证
    api: api/psnAuth/sendWillingnessMobileCodeauth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验意愿手机验证码认证
    api: api/psnAuth/verifyWillingnessMobileCodeauth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, $VALIDATE1]
      - eq: [content.message, $VALIDATE2]
