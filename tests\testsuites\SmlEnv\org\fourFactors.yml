config:
    name: 发起企业核身认证4要素校验
#备注：1.核身没有人工审核 2.四要素发起，没有法定代表人认证 3.除发起企业核身认证4要素校验单接口，其他异常场景和实名一致，不重复写
#116
testcases:
-
    name: 发起企业核身认证4要素校验-法人授权书认证-$CaseName
    testcase: testcases/org/fourFactors_legalRepSign.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-agentName-agentIdNo-mobileNo:
            - ["企业信息都正确(常规企业)","杭州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD","武玉华","******************","***********"]

-
    name: 发起企业核身认证4要素校验-随机金额打款认证-$CaseName
    testcase: testcases/org/fourFactors_transferRandomAmount.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-cardNo-subbranch-amount-bank:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD","**************","平安银行杭州高新支行","0.01","平安银行"]

-
    name: 发起企业核身认证4要素校验-反向打款认证-$CaseName
    testcase: testcases/org/fourFactors_reversePayment.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD"]

-
    name: 发起企业核身认证4要素校验-企业支付宝一键实名认证-$CaseName
    testcase: testcases/org/fourFactors_alipayOneclickConfirm.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-device-token-authCodeAlipay:
            - ["企业信息都正确(常规企业)","杭州易签宝网络科技有限公司","91330108MA2GKFYB1X","程亮","330821198202051817","INDIVIDUAL_CH_IDCARD","PC","xingchenAccessTokenAO001","xingchenAuthcodeZbf001"]
