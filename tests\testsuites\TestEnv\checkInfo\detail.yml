config:
     name: 信息比对

testcases:

-
     name: 个人运营商三要素详情版比对
     testcase: testcases/checkInfo/telecom3detail_1.yml
     parameters:
         - CaseName-name-idNo-mobileNo-VALIDATE1-VALIDATE2-VALIDATE3:
            - ["正常场景-> 信息都正确","许华建","342622198905262396","19057265772",0,"成功","-"]
            - ["异常场景-> idNo不正确","许华建","14273019911003072X","19057265772",30501006,"姓名与⾝份证号不⼀致","-"]
            - ["异常场景-> mobileNo不正确","许华建","342622198905262396","13699846000",30501010,"手机号与身份证号不⼀致","-"]
            - ["异常场景-> name不正确","王王","342622198905262396","19057265772",30501006,"姓名与⾝份证号不⼀致","-"]
            - ["mock场景-> 测试前缀","测试测试","410621198702274071","13301053554",0,"成功","-"]
            - ["mock场景-> 测试前缀+点号","测试测·试","410621198702274071","13301053554",0,"成功","-"]
#            - ["mock场景-> 测试前缀+非汉字","测试1","410621198702274071","13301053554",30501006,"姓名与⾝份证号不⼀致","-"]
