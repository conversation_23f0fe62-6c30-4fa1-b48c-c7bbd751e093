config:
     name: 企业信息比对

testcases:
-
     name: 企业二要素比对
     testcase: testcases/checkInfo/enterprise2_1.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州天谷信息科技有限公司","913301087458306077",0,"成功"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","330108000003512",0,"成功"]

-
     name: 企业二要素比对2
     testcase: testcases/checkInfo/enterprise2_2.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","上海寻梦信息技术有限公司","913301087458306077",30504003,"不匹配"]
             - ["异常场景-> orgCode-填18位codeUSC不正确","杭州天谷信息科技有限公司","91310115MA1K3J0A7C",30504003,"不匹配"]
             - ["异常场景-> orgCode-填15位codeREG不正确","杭州天谷信息科技有限公司","440301103097413",30504003,"不匹配"]
             - ["异常场景-> name为空","","913301087458306077",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","杭","913301087458306077",30504003,"不匹配"]
             - ["异常场景-> name中间含有空格","杭 州天谷信息科技有限公司","913301087458306077",30500101,"有空格"]
             - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","44030110309741",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","91330108745830607",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","9133010874583060778",30500101,"参数错误"]

-
     name: 企业三要素比对
     testcase: testcases/checkInfo/enterprise3_1.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确，填18位codeUSC","杭州天谷信息科技有限公司","何一兵","913301087458306077",0,"成功"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","何一兵","330108000003512",0,"成功"]

-
     name: 企业三要素比对2
     testcase: testcases/checkInfo/enterprise3_2.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
            - ["异常场景-> name不正确","上海寻梦信息技术有限公司","何一兵","913301087458306077",30504003,"不匹配"]
            - ["异常场景-> orgCode-填18位codeUSC不正确","杭州天谷信息科技有限公司","何一兵","91310115MA1K3J0A7C",30504001,"不通过"]
            - ["异常场景-> orgCode-填15位codeREG不正确","杭州天谷信息科技有限公司","何一兵","440301103097413",30504003,"不匹配"]
            - ["异常场景-> name为空","","何一兵","913301087458306077",30500100,"缺少参数"]
            - ["异常场景-> legalRepName为空","杭州天谷信息科技有限公司","","913301087458306077",30500100,"不能为空"]
            - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","何一兵","",30500100,"不能为空"]
            - ["异常场景-> name长度小于2","杭","何一兵","913301087458306077",30504003,"不匹配"]
            - ["异常场景-> name中间含有空格","杭 州天谷信息科技有限公司","何一兵","913301087458306077",30500101,"空格"]
            - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","何一兵","44030110309741",30500101,"参数错误"]
            - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","何一兵","91330108745830607",30500101,"参数错误"]
            - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","何一兵","9133010874583060778",30500101,"参数错误"]
            - ["异常场景-> legalRepName长度小于2","杭州天谷信息科技有限公司","何","913301087458306077",30500101,"参数错误"]

-
     name: 企业四要素比对
     testcase: testcases/checkInfo/enterprise4_1.yml
     parameters:
         - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州天谷信息科技有限公司","何一兵","110108196710262291","913301087458306077",0,"成功"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","何一兵","110108196710262291","330108000003512",0,"成功"]

-
     name: 企业四要素比对2
     testcase: testcases/checkInfo/enterprise4_2.yml
     parameters:
         - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","上海寻梦信息技术有限公司","何一兵","410621198702274071","913301087458306077",30504001,"不通过"]
             - ["异常场景-> legalRepName不正确","杭州天谷信息科技有限公司","王思聪","410621198702274071","913301087458306077",30504001,"不通过"]
             - ["异常场景-> legalRepCertNo不正确","杭州天谷信息科技有限公司","何一兵","330108000003512","913301087458306077",30504001,"不通过"]
             - ["异常场景-> 填18位codeUSC不正确","杭州天谷信息科技有限公司","何一兵","410621198702274071","91310115MA1K3J0A7C",0,"成功"]
             - ["异常场景-> 填15位codeREG不正确","杭州天谷信息科技有限公司","何一兵","410621198702274071","440301103097413",30504001,"不通过"]
             - ["异常场景-> name为空","","何一兵","110108196710262291","913301087458306077",30500100,"缺少参数"]
             - ["异常场景-> legalRepName为空","杭州天谷信息科技有限公司","","110108196710262291","913301087458306077",30500100,"不能为空"]
             - ["异常场景-> legalRepCertNo为空","杭州天谷信息科技有限公司","何一兵","","913301087458306077",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","何一兵","110108196710262291","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","杭","何一兵","110108196710262291","913301087458306077",30504001,"不通过"]
             - ["异常场景-> name中间含有空格","杭 州天谷信息科技有限公司","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
             - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","何一兵","110108196710262291","44030110309741",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","何一兵","110108196710262291","91330108745830607",30500101,"参数错误"]
             - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","何一兵","110108196710262291","9133010874583060778",30500101,"参数错误"]
             - ["异常场景-> orgCode中间有空格","杭州天谷信息科技有限公司","何一兵","110108196710262291","913301087 4583060778",30500116,"空格"]
             - ["异常场景-> legalRepName长度小于2位","杭州天谷信息科技有限公司","何","110108196710262291","913301087458306077",30500101,"参数错误"]
             - ["异常场景-> legalRepCertNo长度小于18位","杭州天谷信息科技有限公司","何一兵","11010819671026229","913301087458306077",30504001,"不通过"]
             - ["异常场景-> legalRepCertNo长度大于18位","杭州天谷信息科技有限公司","何一兵","1101081967102622912","913301087458306077",30504001,"不通过"]
             - ["异常场景-> legalRepCertNo中间有空格","杭州天谷信息科技有限公司","何一兵","110108196710262291","91330108745830 6077",30500116,"空格"]

-
     name: 律所三要素比对
     testcase: testcases/checkInfo/lawFirm3_1.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","山东海乐普律师事务所","李乐国","31370000693763173G",0,"成功"]

-
     name: 律所三要素比对2
     testcase: testcases/checkInfo/lawFirm3_2.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","杭州浙临律师事务所","章炯","31330000725275079F",30504003,"不匹配"]
#             - ["异常场景-> legalRepName不正确","北京市中银律师事务所","王思聪","311100004005693352",30504001,"不通过"]
             - ["异常场景-> codeUSC不正确","北京市中银律师事务所","郭大成","913301087458306077",30504002,"失败"]
             - ["异常场景-> name为空","","郭大成","311100004005693352",30500100,"不能为空"]
             - ["异常场景-> legalRepName为空","北京市中银律师事务所","","311100004005693352",30500100,"不能为空"]
             - ["异常场景-> codeUSC为空","北京市中银律师事务所","郭大成","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","北","郭大成","311100004005693352",30504002,"失败"]
             - ["异常场景-> name中间含有空格","北 京市中银律师事务所","郭大成","311100004005693352",30500101,"空格"]
             - ["异常场景-> legalRepName长度小于2","北京市中银律师事务所","郭","311100004005693352",30500101,"参数错误"]
#             - ["异常场景-> legalRepName中间含有空格","北京市中银律师事务所","郭 大成","311100004005693352",30500116,"格式校验不通过"]
             - ["异常场景-> codeUSC位数不正确","北京市中银律师事务所","郭大成","31110000400569335",30500101,"参数错误"]
             - ["异常场景-> codeUSC中间有空格","北京市中银律师事务所","郭大成","31110000400569335 2",30500116,"空格"]

-
     name: 组织机构三要素比对
     testcase: testcases/checkInfo/organization3_1.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","杭州天谷信息科技有限公司","何一兵","913301087458306077",0,"成功"]

-
     name: 组织机构三要素比对2
     testcase: testcases/checkInfo/organization3_2.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","北京理工大学教育基金会","何一兵","913301087458306077",30504003,"不匹配"]
             - ["异常场景-> legalRepName不正确","杭州天谷信息科技有限公司","杨宾","913301087458306077",30504005,"不匹配"]
             - ["异常场景-> orgCode不正确","杭州天谷信息科技有限公司","何一兵","53100000500021676K",30504003,"不匹配"]
             - ["异常场景-> name为空","","杨宾","53100000500021676K",30500100,"不能为空"]
             - ["异常场景-> legalRepName为空","北京理工大学教育基金会","","53100000500021676K",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","北京理工大学教育基金会","杨宾","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","北","杨宾","53100000500021676K",30504003,"不匹配"]
             - ["异常场景-> name中间含有空格","北 京理工大学教育基金会","杨宾","53100000500021676K",30500101,"空格"]
             - ["异常场景-> legalRepName长度小于2","北京理工大学教育基金会","郭","53100000500021676K",30500101,"参数错误"]
             - ["异常场景-> legalRepName中间含有空格","杭州天谷信息科技有限公司","何一 兵","913301087458306077",30504005,"不匹配"]
             - ["异常场景-> orgCode位数不正确","北京理工大学教育基金会","杨宾","53100000500021676",30500101,"参数错误"]
             - ["异常场景-> codeUSC中间有空格","北京理工大学教育基金会","杨宾","5310000050 0021676K",30500116,"空格"]

-
     name: 非工商组织三要素比对
     testcase: testcases/checkInfo/orgSocial3_1.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","天津大学","金东寒","12100000401359321Q",0,"成功"]

-
     name: 非工商组织三要素比对2
     testcase: testcases/checkInfo/orgSocial3_2.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","北京理工大学教育基金会","金东寒","12100000401359321Q",30504003,"不匹配"]
             - ["异常场景-> legalRepName不正确","天津大学","王思聪","12100000401359321Q",30504005,"不匹配"]
             - ["异常场景-> orgCode不正确","天津大学","金东寒","913301087458306077",30504002,"失败"]
             - ["异常场景-> name为空","","金东寒","12100000401359321Q",30500100,"不能为空"]
             - ["异常场景-> legalRepName为空","天津大学","","12100000401359321Q",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","天津大学","金东寒","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","天","金东寒","12100000401359321Q",30504003,"不匹配"]
             - ["异常场景-> name中间有空格","天 津大学","金东寒","12100000401359321Q",30500101,"空格"]
             - ["异常场景-> legalRepName长度小于2","天津大学","金","12100000401359321Q",30500101,"参数错误"]
             - ["异常场景-> legalRepName中间有空格","天津大学","金 东寒","12100000401359321Q",30504005,"不匹配"]
             - ["异常场景-> codeUSC长度位数不正确","天津大学","金东寒","1210000040135931Q",30500101,"18位"]


-
     name: 企业二要素(中文名字中间不支持空格)
     testcase: testcases/checkInfo/orgSocial3_2.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["异常场景-> name不正确","北京理工大学教育基金会","金东寒","12100000401359321Q",30504003,"不匹配"]
             - ["异常场景-> legalRepName不正确","天津大学","王思聪","12100000401359321Q",30504005,"不匹配"]
             - ["异常场景-> orgCode不正确","天津大学","金东寒","913301087458306077",30504002,"失败"]
             - ["异常场景-> name为空","","金东寒","12100000401359321Q",30500100,"不能为空"]
             - ["异常场景-> legalRepName为空","天津大学","","12100000401359321Q",30500100,"不能为空"]
             - ["异常场景-> orgCode为空","天津大学","金东寒","",30500100,"不能为空"]
             - ["异常场景-> name长度小于2","天","金东寒","12100000401359321Q",30504003,"不匹配"]
             - ["异常场景-> name中间有空格","天 津大学","金东寒","12100000401359321Q",30500101,"参数错误"]
             - ["异常场景-> legalRepName长度小于2","天津大学","金","12100000401359321Q",30500101,"参数错误"]
             - ["异常场景-> legalRepName中间有空格","天津大学","金 东寒","12100000401359321Q",30504005,"不匹配"]
             - ["异常场景-> codeUSC长度位数不正确","天津大学","金东寒","1210000040135931Q",30500101,"参数错误"]

- 
        name: 企业二要素(中文名字中间不支持空格)->$CaseName
        testcase: testcases/checkInfo/enterprise2_1.yml
        parameters:
            - CaseName-name-orgCode-VALIDATE1-VALIDATE2:     
                - ["异常场景->中文名字之间有空格","杭州天谷 信息科技有限公司","913301087458306077",30500101,'空格']
                - ["异常场景->英文名字之间有空格","Hang zhou","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","杭州 天谷kjasjka","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kasklas杭州 天谷","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 abc","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","abc 杭州 天谷","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","a bc 杭州 天谷","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 a bc","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kas klas杭州 天谷","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷kas klas ","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州天谷 labc","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","ty 杭州天谷","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","杭州天谷ab c","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","ab c杭州天谷","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","杭州天谷 ab c","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","ab c 杭州天谷","913301087458306077",30504003,"不匹配"]

- 
        name: 企业三要素(中文名字中间不支持空格)->$CaseName
        testcase: testcases/checkInfo/enterprise3_1.yml
        parameters:
            - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
                    - ["异常场景->中文名字之间有空格","杭州天谷 信息科技有限公司","何一兵","913301087458306077",30500101,"空格"]
                    - ["异常场景->英文名字之间有空格","Hang zhou","何一兵","913301087458306077",30504003,"不匹配"]
                    - ["异常场景->中英文组合名字","杭州 天谷kjasjka","何一兵","913301087458306077",30500101,"空格"]
                    - ["异常场景->中英文组合名字","kasklas杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                    - ["异常场景->中英文组合名字","杭州 天谷 abc","何一兵","913301087458306077",30500101,"空格"]
                    - ["异常场景->中英文组合名字","abc 杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                    - ["异常场景->中英文组合名字","a bc 杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                    - ["异常场景->中英文组合名字","杭州 天谷 a bc","何一兵","913301087458306077",30500101,"空格"]
                    - ["异常场景->中英文组合名字","kas klas杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                    - ["异常场景->中英文组合名字","杭州 天谷kas klas ","何一兵","913301087458306077",30500101,"空格"]
                    - ["异常场景->中英文组合名字","杭州天谷 labc","何一兵","913301087458306077",30504003,"不匹配"]
                    - ["异常场景->中英文组合名字","ty 杭州天谷","何一兵","913301087458306077",30504003,"不匹配"]
                    - ["异常场景->中英文组合名字","杭州天谷ab c","何一兵","913301087458306077",30504003,"不匹配"]
                    - ["异常场景->中英文组合名字","ab c杭州天谷","何一兵","913301087458306077",30504003,"不匹配"]
                    - ["异常场景->中英文组合名字","杭州天谷 ab c","何一兵","913301087458306077",30504003,"不匹配"]
                    - ["异常场景->中英文组合名字","ab c 杭州天谷","何一兵","913301087458306077",30504003,"不匹配"]

- 
        name: 企业四要素(中文名字中间不支持空格)->$CaseName
        testcase: testcases/checkInfo/enterprise4_1.yml
        parameters:
            - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2:
                - ["异常场景->中文名字之间有空格","杭州天谷 信息科技有限公司","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
                - ["异常场景->英文名字之间有空格","Hang zhou","何一兵","110108196710262291","913301087458306077",30504001,"不通过"]
                - ["异常场景->中英文组合名字","杭州 天谷kjasjka","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kasklas杭州 天谷","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 abc","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","abc 杭州 天谷","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","a bc 杭州 天谷","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 a bc","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kas klas杭州 天谷","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷kas klas ","何一兵","110108196710262291","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州天谷 labc","何一兵","110108196710262291","913301087458306077",30504001,"不通过"]
                - ["异常场景->中英文组合名字","ty 杭州天谷","何一兵","110108196710262291","913301087458306077",30504001,"不通过"]
                - ["异常场景->中英文组合名字","杭州天谷ab c","何一兵","110108196710262291","913301087458306077",30504001,"不通过"]
                - ["异常场景->中英文组合名字","ab c杭州天谷","何一兵","110108196710262291","913301087458306077",30504001,"不通过"]
                - ["异常场景->中英文组合名字","杭州天谷 ab c","何一兵","110108196710262291","913301087458306077",30504001,"不通过"]
                - ["异常场景->中英文组合名字","ab c 杭州天谷","何一兵","110108196710262291","913301087458306077",30504001,"不通过"]

- 
        name: 律所信息校验(中文名字中间不支持空格)->$CaseName
        testcase: testcases/checkInfo/lawFirm3_1.yml
        parameters:
            - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
                - ["异常场景->中文名字之间有空格","杭州天谷 信息科技有限公司","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->英文名字之间有空格","Hang zhou","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","杭州 天谷kjasjka","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kasklas杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 abc","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","abc 杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","a bc 杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 a bc","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kas klas杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷kas klas ","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州天谷 labc","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","ty 杭州天谷","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","杭州天谷ab c","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","ab c杭州天谷","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","杭州天谷 ab c","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","ab c 杭州天谷","何一兵","913301087458306077",30504002,"失败"]

- 
        name: 组织信息比对(中文名字中间不支持空格)->$CaseName
        testcase: testcases/checkInfo/organization3_1.yml  
        parameters:
            - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
                - ["异常场景->中文名字之间有空格","杭州天谷 信息科技有限公司","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->英文名字之间有空格","Hang zhou","何一兵","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","杭州 天谷kjasjka","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kasklas杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 abc","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","abc 杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","a bc 杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 a bc","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kas klas杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷kas klas ","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州天谷 labc","何一兵","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","ty 杭州天谷","何一兵","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","杭州天谷ab c","何一兵","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","ab c杭州天谷","何一兵","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","杭州天谷 ab c","何一兵","913301087458306077",30504003,"不匹配"]
                - ["异常场景->中英文组合名字","ab c 杭州天谷","何一兵","913301087458306077",30504003,"不匹配"]

- 
        name: 非工商社会组织信息比对(中文名字中间不支持空格)->$CaseName
        testcase: testcases/checkInfo/orgSocial3_1.yml
        parameters:
            - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
                - ["异常场景->中文名字之间有空格","杭州天谷 信息科技有限公司","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->英文名字之间有空格","Hang zhou","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","杭州 天谷kjasjka","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kasklas杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 abc","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","abc 杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","a bc 杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷 a bc","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","kas klas杭州 天谷","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州 天谷kas klas ","何一兵","913301087458306077",30500101,"空格"]
                - ["异常场景->中英文组合名字","杭州天谷 labc","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","ty 杭州天谷","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","杭州天谷ab c","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","ab c杭州天谷","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","杭州天谷 ab c","何一兵","913301087458306077",30504002,"失败"]
                - ["异常场景->中英文组合名字","ab c 杭州天谷","何一兵","913301087458306077",30504002,"失败"]
    

