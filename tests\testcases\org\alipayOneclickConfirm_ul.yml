config:
    name: 企业支付宝一键实名异常场景
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 申请企业支付宝一键实名
    api: api/orgAuth/alipayOneclickApply.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 企业支付宝一键实名
    api: api/orgAuth/alipayOneclickConfirm.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, $VALIDATE1]
      - contains: [content.message, $VALIDATE2]
