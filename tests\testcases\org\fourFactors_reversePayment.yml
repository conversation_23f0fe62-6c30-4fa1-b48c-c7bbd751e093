config:
    name: 发起企业核身认证4要素校验-反向打款认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起企业核身认证4要素校验
    api: api/orgAuth/fourFactors.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 发起企业反向打款认证
    api: api/orgAuth/reversePayment.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]
      - eq: [content.data.paymentAmount, "0.01"]

-
    name: 查询反向打款进度
    api: api/orgAuth/reversePaymentProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.process, "UN_RECEIVED"]
      - eq: [content.data.message, "无对应打款信息"]
    teardown_hooks:
      - ${teardown_hook_sleep_n_secs(3)}

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.subFlows.0.subFlowType, "ORGANIZATION_REVERSE_PAYMENT"]
      - eq: [content.data.subFlows.0.status, "ING"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "ORGANIZATION_REVERSE_PAYMENT"]
      - eq: [content.data.organInfo.name, $name]
      - eq: [content.data.organInfo.legalRepCertType, $legalRepCertType]


-
    name: 根据flowId查询其认证子service的详情 对内服务GET方式
    api: api/public/queryFlowServiceInnerLineGet.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "进行中"]
      - eq: [content.data.objectType, "企业"]
      - eq: [content.data.subFlows.0.subFlowType, "企业反向打款"]

-
    name: 根据flowId查询其认证子service的详情 对内服务Post方式
    api: api/public/queryFlowServiceInnerLinePost.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "进行中"]
      - eq: [content.data.objectType, "企业"]
      - eq: [content.data.subFlows.0.subFlowType, "企业反向打款"]