config:
    name: 发起企业核身认证3要素校验-随机金额打款认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起企业核身认证3要素校验
    api: api/orgAuth/threeFactors.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询打款银行信息-根据联行号
    api: api/orgAuth/subbranch2.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.list.0.bankName, "平安银行杭州高新支行"]

-
    name: 发起随机金额打款认证
    api: api/orgAuth/transferRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询随机金额打款进度1
    api: api/orgAuth/transferProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.process, "PAID"]
      - eq: [content.data.cardNo, $cardNo]
      - eq: [content.data.message, "打款申请成功"]

-
    name: 随机金额校验
    api: api/orgAuth/verifyRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询随机金额打款进度2
    api: api/orgAuth/transferProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.process, "ORGANFINISHED"]
      - eq: [content.data.message, "打款申请成功"]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.subFlows.0.subFlowType, "ORGANIZATION_TRANSFER_RANDOM_AMOUNT"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "ORGANIZATION_TRANSFER_RANDOM_AMOUNT"]
      - eq: [content.data.organInfo.name, $name]
      - eq: [content.data.organInfo.cardNo, $cardNo]
      - eq: [content.data.organInfo.bank, $bank]
      - eq: [content.data.organInfo.subbranch, $subbranch]