#config:
#    name: 查询刷脸资源
#
#oss存储项目未上线，影响上测试，所以先屏蔽
#testcases:
#-
#    name: 查询刷脸资源-$CaseName
#    testcase: testcases/psn/flowResources.yml
#    parameters:
#        - CaseName-flowType-value:
#            - ["查询刷脸资源-个人-实名-腾讯云人脸识别","auth","3870092993009030614"]
#            - ["查询刷脸资源-个人-实名-快捷刷脸","auth","3870983036447757725"]
#            - ["查询刷脸资源-个人-实名-支付宝人脸识别","auth","3870090073018471829"]
#            - ["查询刷脸资源-企业-实名-对公打款","auth","3872784528485912984"]
#            - ["查询刷脸资源-企业-实名-法定代表人认证","auth","3875348930296811990"]
#            - ["查询刷脸资源-个人-意愿-腾讯云人脸识别","willingness","b2b2ca92151c4ac49a1a8d6b880e2ca7"]
##            - ["查询刷脸资源-个人-意愿-快捷刷脸","willingness",""]
##            - ["查询刷脸资源-个人-意愿-支付宝人脸识别","willingness",""]
#            - ["查询刷脸资源-签署中意愿-腾讯云人脸识别","willingness","b92c47bb522747ae9c7c7e2efcb5b982"]