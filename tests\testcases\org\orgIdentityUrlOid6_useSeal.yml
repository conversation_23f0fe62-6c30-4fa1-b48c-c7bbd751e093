config:
    name: 获取企业实名地址-申请用印场景
    variables:
      - accountId: '66bded89ff08405b90480c90c8efedbd'  #企业oid:esigntest闭环测试02
      - appId: '**********'
      - agentAccountId: '934908b5103542ae81ffa80afe3fbcf8'    #经办人oid: ***********登录账号

teststeps:
-
    name: 获取组织机构实名认证地址-单次用印
    api: api/orgAuth/orgIdentityUrlOid6.yml
    variables:
      - bizScene: 'AGENT_APPLY_USE_SEAL'
      - bizSceneContext:
          agentUseSealType: 'ONCE'
          signFlowId: '266bbea246a947e180935bf2be8bf109'
          authorizedAppId: '**********'
          signFlowName: '集测发起经办人单次用印实名流程001'
          signFlowCreateTimestamp: ${get_now_stamp()}
      - dataCollect:
          clientType: 'WEB'
          bizSource: 'Sign'
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - startswith: ["content.data.url", "http"]

-
    name: 获取组织机构实名认证地址-平台用印
    api: api/orgAuth/orgIdentityUrlOid6.yml
    variables:
      - bizScene: 'AGENT_APPLY_USE_SEAL'
      - bizSceneContext:
          agentUseSealType: 'APPID'
          authorizedAppId: '**********'
          expiryTimestamp: ${get_day_time(30)}
      - dataCollect:
          clientType: 'WEB'
          bizSource: 'Sign'
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - startswith: ["content.data.url", "http"]

-
    name: 失效实名流程
    api: api/orgAuth/api_invalidTheFlowByFlowId.yml
    variables:
      flowId: '3098416529382316167'
    validate:
      - eq: [status_code,200]

