# 赠送管理系统规范化改造-测试用例

## 功能测试

### 权限控制功能

#### TL-销售角色用户名下客户赠送权限验证

##### PD-前置条件：用户为销售角色；已配置"名下客户赠送"权限；系统中存在该销售名下的客户；

##### 步骤一：销售用户登录系统

##### 步骤二：进入运营支撑-赠送管理-特殊审批赠送

##### 步骤三：点击客户选择下拉框

##### 步骤四：查看可选择的客户列表

##### ER-预期结果：1：成功进入赠送管理页面；2：客户下拉框仅显示该销售名下的客户；3：无法选择其他销售名下的客户；4：客户列表数据准确；

#### TL-非销售角色用户客户选择权限验证

##### PD-前置条件：用户为非销售角色；具有运营支撑权限；系统中存在多个客户；

##### 步骤一：非销售用户登录系统

##### 步骤二：进入运营支撑-赠送管理-特殊审批赠送

##### 步骤三：点击客户选择下拉框

##### 步骤四：查看可选择的客户列表

##### ER-预期结果：1：成功进入赠送管理页面；2：客户选择范围根据运营支撑权限判断；3：不受"名下客户赠送"权限限制；4：客户列表符合运营权限范围；

#### TL-无权限用户访问赠送管理功能验证

##### PD-前置条件：用户无运营支撑权限；用户无"名下客户赠送"权限；

##### 步骤一：无权限用户登录系统

##### 步骤二：尝试访问运营支撑-赠送管理-特殊审批赠送

##### 步骤三：检查系统响应

##### ER-预期结果：1：无法访问赠送管理页面；2：显示权限不足提示；3：页面跳转到无权限提示页；4：操作日志记录访问失败；

### 产品试用场景功能

#### TL-产品试用场景商品选择范围验证

##### PD-前置条件：用户已登录；具有赠送权限；选择产品试用场景；

##### 步骤一：进入赠送管理页面

##### 步骤二：选择赠送场景为"产品试用"

##### 步骤三：点击商品选择下拉框

##### 步骤四：查看可选择的商品列表

##### ER-预期结果：1：商品列表仅显示配置的试用商品；2：包含e签宝四个版本（基础版690、专业版475、高级版191、旗舰版719）；3：包含其他指定商品（AI算力套餐包630等）；4：不显示非试用商品；

#### TL-e签宝版本商品每年一次试用限制验证

##### PD-前置条件：用户已登录；选择产品试用场景；客户本年度已试用过e签宝基础版；

##### 步骤一：选择该客户

##### 步骤二：选择产品试用场景

##### 步骤三：尝试选择e签宝基础版商品

##### 步骤四：设置赠送参数并提交

##### ER-预期结果：1：系统检测到该客户本年度已试用；2：显示"该商品本年度已试用，无法重复赠送"提示；3：无法提交赠送申请；4：其他版本商品不受影响；

#### TL-产品试用场景有效期15日限制验证

##### PD-前置条件：用户已登录；选择产品试用场景；选择有效商品；

##### 步骤一：选择客户和商品

##### 步骤二：设置有效期

##### 步骤三：尝试设置有效期为30日

##### 步骤四：提交赠送申请

##### ER-预期结果：1：有效期选择框最大值为15日；2：无法选择超过15日的时间；3：设置30日时显示错误提示；4：只能在15日范围内选择；

#### TL-产品试用场景延续设置移除验证

##### PD-前置条件：用户已登录；选择产品试用场景；

##### 步骤一：进入赠送设置页面

##### 步骤二：查看页面配置选项

##### 步骤三：寻找延续设置相关选项

##### ER-预期结果：1：页面中不存在延续设置选项；2：无法配置延续相关参数；3：页面布局正常；4：其他设置项正常显示；

#### TL-非版本商品成本100元限制验证

##### PD-前置条件：用户已登录；选择产品试用场景；选择非版本商品（如AI算力套餐包）；

##### 步骤一：选择客户和非版本商品

##### 步骤二：设置赠送数量使成本超过100元

##### 步骤三：提交赠送申请

##### 步骤四：检查系统验证结果

##### ER-预期结果：1：系统计算总成本；2：超过100元时显示成本限制提示；3：无法提交超出限制的申请；4：在限制范围内可正常提交；

### 营销赠送场景功能

#### TL-营销赠送场景有效期一年限制验证

##### PD-前置条件：用户已登录；选择营销赠送场景；

##### 步骤一：选择客户和商品

##### 步骤二：设置有效期

##### 步骤三：尝试设置有效期为18个月

##### 步骤四：检查系统限制

##### ER-预期结果：1：有效期选择框最大值为1年；2：无法选择超过1年的时间；3：设置超出范围时显示错误提示；4：在1年范围内可正常选择；

#### TL-营销赠送场景商品选择范围验证

##### PD-前置条件：用户已登录；选择营销赠送场景；

##### 步骤一：进入赠送管理页面

##### 步骤二：选择赠送场景为"营销赠送"

##### 步骤三：点击商品选择下拉框

##### 步骤四：查看可选择的商品列表

##### ER-预期结果：1：商品列表显示所有可赠送商品；2：不受产品试用的商品限制；3：商品数据完整准确；4：商品分类正确；

### 提前交付使用场景功能

#### TL-提前交付使用赠送类型功能验证

##### PD-前置条件：用户已登录；具有赠送权限；

##### 步骤一：进入赠送管理页面

##### 步骤二：查看赠送场景选项

##### 步骤三：选择"提前交付使用"场景

##### 步骤四：验证该场景的功能可用性

##### ER-预期结果：1：赠送场景中包含"提前交付使用"选项；2：可正常选择该场景；3：场景切换功能正常；4：相关配置项正确显示；

## 边界测试

### 时间边界验证

#### TL-年度试用限制跨年重置验证

##### PD-前置条件：客户在上一年度已试用e签宝基础版；当前为新年度；

##### 步骤一：选择该客户

##### 步骤二：选择产品试用场景

##### 步骤三：选择e签宝基础版商品

##### 步骤四：提交赠送申请

##### ER-预期结果：1：系统识别为新年度；2：允许重新试用该商品；3：试用限制正确重置；4：赠送申请成功提交；

#### TL-成本边界100元临界值验证

##### PD-前置条件：用户已登录；选择产品试用场景；选择非版本商品；

##### 步骤一：选择客户和商品

##### 步骤二：设置赠送数量使成本恰好为100元

##### 步骤三：提交赠送申请

##### 步骤四：验证系统处理结果

##### ER-预期结果：1：成本计算准确；2：100元临界值可正常提交；3：系统处理成功；4：赠送记录正确保存；

## 异常测试

### 权限异常处理

#### TL-权限被撤销后的访问控制验证

##### PD-前置条件：销售用户原有"名下客户赠送"权限；权限被管理员撤销；

##### 步骤一：权限撤销后用户重新登录

##### 步骤二：尝试访问赠送管理功能

##### 步骤三：检查系统权限验证

##### ER-预期结果：1：系统检测到权限变更；2：拒绝访问赠送管理功能；3：显示权限不足提示；4：用户会话状态正确更新；

### 业务规则异常处理

#### TL-商品配置异常时的系统处理验证

##### PD-前置条件：配置中心中试用商品配置异常或缺失；

##### 步骤一：用户选择产品试用场景

##### 步骤二：尝试加载商品列表

##### 步骤三：检查系统异常处理

##### ER-预期结果：1：系统检测到配置异常；2：显示友好的错误提示；3：不影响其他场景功能；4：异常日志正确记录；

#### TL-客户数据异常时的处理验证

##### PD-前置条件：销售名下客户数据存在异常；

##### 步骤一：销售用户登录系统

##### 步骤二：尝试加载客户列表

##### 步骤三：检查异常数据处理

##### ER-预期结果：1：过滤异常客户数据；2：显示有效客户列表；3：异常情况友好提示；4：不影响正常客户选择；

## 性能测试

### 权限验证性能

#### TL-大量客户数据下的权限验证性能

##### PD-前置条件：销售用户名下有1000+客户；系统正常运行；

##### 步骤一：销售用户登录系统

##### 步骤二：进入赠送管理页面

##### 步骤三：点击客户选择下拉框

##### 步骤四：记录加载时间

##### ER-预期结果：1：客户列表加载时间小于3秒；2：权限验证响应及时；3：页面交互流畅；4：内存使用合理；

### 商品查询性能

#### TL-商品配置查询性能验证

##### PD-前置条件：配置中心存储大量商品配置；系统正常运行；

##### 步骤一：用户选择产品试用场景

##### 步骤二：点击商品选择下拉框

##### 步骤三：记录商品加载时间

##### 步骤四：验证查询性能

##### ER-预期结果：1：商品列表加载时间小于2秒；2：配置查询响应快速；3：数据过滤准确；4：系统资源占用正常；

## 安全测试

### 权限绕过防护

#### TL-通过接口直接访问绕过权限验证的防护

##### PD-前置条件：用户无"名下客户赠送"权限；了解接口地址；

##### 步骤一：用户直接调用赠送接口

##### 步骤二：传入非本人名下客户ID

##### 步骤三：尝试执行赠送操作

##### 步骤四：检查安全防护机制

##### ER-预期结果：1：接口验证用户权限；2：拒绝非法访问请求；3：返回权限不足错误；4：安全日志记录攻击行为；

### 数据篡改防护

#### TL-客户ID参数篡改防护验证

##### PD-前置条件：销售用户仅有部分客户权限；

##### 步骤一：正常进入赠送页面

##### 步骤二：通过浏览器开发工具修改客户ID参数

##### 步骤三：选择非权限范围内的客户

##### 步骤四：尝试提交赠送申请

##### ER-预期结果：1：系统验证客户权限；2：检测到参数篡改；3：拒绝非法操作；4：记录安全异常日志；

## 兼容性测试

### 原有功能兼容性

#### TL-原有赠送功能向后兼容性验证

##### PD-前置条件：系统升级前存在历史赠送记录；升级完成；

##### 步骤一：查看历史赠送记录

##### 步骤二：验证历史数据完整性

##### 步骤三：测试原有功能是否正常

##### ER-预期结果：1：历史数据完整保留；2：原有功能正常工作；3：数据格式兼容；4：无功能回归问题；

#### TL-不同角色用户功能兼容性验证

##### PD-前置条件：系统中存在多种角色用户；权限配置完成；

##### 步骤一：不同角色用户分别登录

##### 步骤二：访问赠送管理功能

##### 步骤三：验证各角色功能正常性

##### ER-预期结果：1：各角色功能按权限正常工作；2：角色间无功能冲突；3：权限隔离有效；4：用户体验一致；

## 配置管理测试

### 商品配置管理

#### TL-配置中心商品配置修改生效验证

##### PD-前置条件：管理员具有配置中心权限；系统正常运行；

##### 步骤一：管理员登录配置中心

##### 步骤二：修改试用商品配置

##### 步骤三：保存配置更改

##### 步骤四：用户端验证配置生效

##### ER-预期结果：1：配置修改成功保存；2：用户端实时或准实时生效；3：商品列表更新正确；4：无缓存问题；

#### TL-商品配置异常恢复验证

##### PD-前置条件：商品配置存在错误；系统检测到异常；

##### 步骤一：触发配置异常情况

##### 步骤二：系统自动检测异常

##### 步骤三：执行配置恢复机制

##### 步骤四：验证恢复效果

##### ER-预期结果：1：系统及时检测配置异常；2：自动或手动恢复机制有效；3：服务快速恢复正常；4：异常处理日志完整；

## 补充测试场景

### 批量操作测试

#### TL-批量客户赠送操作验证

##### PD-前置条件：销售用户具有多个名下客户；选择产品试用场景；

##### 步骤一：选择多个客户

##### 步骤二：选择相同商品进行批量赠送

##### 步骤三：设置赠送参数

##### 步骤四：提交批量赠送申请

##### ER-预期结果：1：支持批量客户选择；2：批量操作执行成功；3：每个客户的限制规则独立验证；4：操作结果准确记录；

### 审批流程测试

#### TL-特殊审批赠送流程验证

##### PD-前置条件：用户提交赠送申请；配置了审批流程；

##### 步骤一：提交赠送申请

##### 步骤二：申请进入审批流程

##### 步骤三：审批人员处理申请

##### 步骤四：查看审批结果

##### ER-预期结果：1：申请正确进入审批流程；2：审批人员可正常处理；3：审批结果及时通知；4：审批记录完整保存；

## 冒烟测试用例

### 核心功能验证

#### MYTL-销售角色用户名下客户赠送权限验证

##### PD-前置条件：用户为销售角色；已配置"名下客户赠送"权限；系统中存在该销售名下的客户；

##### 步骤一：销售用户登录系统

##### 步骤二：进入运营支撑-赠送管理-特殊审批赠送

##### 步骤三：点击客户选择下拉框

##### 步骤四：查看可选择的客户列表

##### ER-预期结果：1：成功进入赠送管理页面；2：客户下拉框仅显示该销售名下的客户；3：无法选择其他销售名下的客户；4：客户列表数据准确；

#### MYTL-产品试用场景商品选择范围验证

##### PD-前置条件：用户已登录；具有赠送权限；选择产品试用场景；

##### 步骤一：进入赠送管理页面

##### 步骤二：选择赠送场景为"产品试用"

##### 步骤三：点击商品选择下拉框

##### 步骤四：查看可选择的商品列表

##### ER-预期结果：1：商品列表仅显示配置的试用商品；2：包含e签宝四个版本（基础版690、专业版475、高级版191、旗舰版719）；3：包含其他指定商品（AI算力套餐包630等）；4：不显示非试用商品；

#### MYTL-e签宝版本商品每年一次试用限制验证

##### PD-前置条件：用户已登录；选择产品试用场景；客户本年度已试用过e签宝基础版；

##### 步骤一：选择该客户

##### 步骤二：选择产品试用场景

##### 步骤三：尝试选择e签宝基础版商品

##### 步骤四：设置赠送参数并提交

##### ER-预期结果：1：系统检测到该客户本年度已试用；2：显示"该商品本年度已试用，无法重复赠送"提示；3：无法提交赠送申请；4：其他版本商品不受影响；

#### MYTL-产品试用场景有效期15日限制验证

##### PD-前置条件：用户已登录；选择产品试用场景；选择有效商品；

##### 步骤一：选择客户和商品

##### 步骤二：设置有效期

##### 步骤三：尝试设置有效期为30日

##### 步骤四：提交赠送申请

##### ER-预期结果：1：有效期选择框最大值为15日；2：无法选择超过15日的时间；3：设置30日时显示错误提示；4：只能在15日范围内选择；

#### MYTL-营销赠送场景有效期一年限制验证

##### PD-前置条件：用户已登录；选择营销赠送场景；

##### 步骤一：选择客户和商品

##### 步骤二：设置有效期

##### 步骤三：尝试设置有效期为18个月

##### 步骤四：检查系统限制

##### ER-预期结果：1：有效期选择框最大值为1年；2：无法选择超过1年的时间；3：设置超出范围时显示错误提示；4：在1年范围内可正常选择；

#### MYTL-提前交付使用赠送类型功能验证

##### PD-前置条件：用户已登录；具有赠送权限；

##### 步骤一：进入赠送管理页面

##### 步骤二：查看赠送场景选项

##### 步骤三：选择"提前交付使用"场景

##### 步骤四：验证该场景的功能可用性

##### ER-预期结果：1：赠送场景中包含"提前交付使用"选项；2：可正常选择该场景；3：场景切换功能正常；4：相关配置项正确显示；

#### MYTL-非版本商品成本100元限制验证

##### PD-前置条件：用户已登录；选择产品试用场景；选择非版本商品（如AI算力套餐包）；

##### 步骤一：选择客户和非版本商品

##### 步骤二：设置赠送数量使成本超过100元

##### 步骤三：提交赠送申请

##### 步骤四：检查系统验证结果

##### ER-预期结果：1：系统计算总成本；2：超过100元时显示成本限制提示；3：无法提交超出限制的申请；4：在限制范围内可正常提交；

#### MYTL-原有赠送功能向后兼容性验证

##### PD-前置条件：系统升级前存在历史赠送记录；升级完成；

##### 步骤一：查看历史赠送记录

##### 步骤二：验证历史数据完整性

##### 步骤三：测试原有功能是否正常

##### ER-预期结果：1：历史数据完整保留；2：原有功能正常工作；3：数据格式兼容；4：无功能回归问题；

## 线上验证用例

### 生产环境核心功能验证

#### PATL-生产环境销售权限控制功能验证

##### PD-前置条件：生产环境部署完成；销售用户已配置权限；真实客户数据存在；

##### 步骤一：销售用户在生产环境登录

##### 步骤二：访问赠送管理功能

##### 步骤三：验证客户选择范围

##### 步骤四：确认权限控制有效性

##### ER-预期结果：1：权限控制在生产环境正常工作；2：客户数据准确显示；3：性能表现良好；4：无安全风险；

#### PATL-生产环境产品试用规则验证

##### PD-前置条件：生产环境部署完成；配置中心商品配置正确；

##### 步骤一：选择产品试用场景

##### 步骤二：验证商品选择范围

##### 步骤三：测试年度限制规则

##### 步骤四：验证有效期限制

##### ER-预期结果：1：商品配置正确生效；2：业务规则准确执行；3：限制机制有效；4：用户体验良好；

#### PATL-生产环境营销赠送功能验证

##### PD-前置条件：生产环境正常运行；营销场景配置完成；

##### 步骤一：选择营销赠送场景

##### 步骤二：设置赠送参数

##### 步骤三：验证有效期限制

##### 步骤四：提交赠送申请

##### ER-预期结果：1：营销赠送功能正常；2：有效期限制生效；3：申请流程顺畅；4：数据记录准确；

#### PATL-生产环境系统性能验证

##### PD-前置条件：生产环境正常运行；有真实用户访问；

##### 步骤一：监控系统响应时间

##### 步骤二：观察并发处理能力

##### 步骤三：检查资源使用情况

##### 步骤四：验证系统稳定性

##### ER-预期结果：1：响应时间在可接受范围；2：并发处理正常；3：资源使用合理；4：系统运行稳定；

#### PATL-生产环境数据一致性验证

##### PD-前置条件：生产环境部署完成；历史数据完整；

##### 步骤一：检查历史赠送记录

##### 步骤二：验证数据迁移完整性

##### 步骤三：测试新旧功能兼容性

##### 步骤四：确认数据一致性

##### ER-预期结果：1：历史数据完整保留；2：数据迁移无丢失；3：新旧功能兼容；4：数据一致性良好；

#### PATL-生产环境异常处理验证

##### PD-前置条件：生产环境运行；监控系统正常；

##### 步骤一：模拟异常场景

##### 步骤二：观察系统异常处理

##### 步骤三：验证恢复机制

##### 步骤四：检查日志记录

##### ER-预期结果：1：异常处理机制有效；2：系统快速恢复；3：用户体验不受影响；4：日志记录完整；

### 数量校验
- 总用例数：30条
- 冒烟用例数：9条（30%）
- 线上验证用例数：6条（20%）
