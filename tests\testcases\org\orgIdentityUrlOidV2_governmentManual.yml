config:
    name: 获取组织机构实名认证地址V2-反向打款认证
    base_url: ${ENV(base_url_rpc)}

teststeps:
#创建企业oid，发起实名；经办人意愿-短信意愿；企业发起反向打款-打款成功
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlV2.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.url, null]

-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版获取企业信息
    api: api/orgAuth/queryOrganizationInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.agentName, null]
      - eq: [content.data.certNo, $idNumber]
      - eq: [content.data.legalRepName, $orgLegalName]
      - eq: [content.data.name, $name]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.display, null]

-
    name: 查询企业实名流程进度1
    api: api/orgAuth/getOrgProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.ifInfoAuthSuccessful, false]
      - eq: [content.data.status, "INIT"]
      - eq: [content.data.type, "ORGANIZATION_INFO_AUTH"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 企业经办人认证状态查询
    api: api/orgAuth/getAgentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.authorizedInOrgFlow, False]
      - eq: [content.data.authorizedInV21, True]
      - eq: [content.data.authorisedInUserCenter, True]
      - eq: [content.data.status, "WILLINGNESS_REQUIRED"]

-
    name: 获取经办人意愿方式
    api: api/psnAuth/getWillingnessType.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.willAuthType, null]

-
    name: 查询意愿手机号
    api: api/psnAuth/queryWillingnessMobileNo.yml
    extract:
      - receiver: content.data.mobileNo
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]

-
    name: 发起意愿手机验证码认证
    api: api/psnAuth/sendWillingnessMobileCodeauth.yml
    variables:
      sendType: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验意愿手机验证码认证-正确验证码
    api: api/psnAuth/verifyWillingnessMobileCodeauth.yml
    variables:
      authCode: "123456"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 上传营业执照
    api: api/public/fileUploadUrl.yml
    variables:
      - contentMd5: "v/91uFJ2BvFr/VPzYybiKQ=="
      - contentType: "application/octet-stream"
      - fileName: "银行卡.jpeg"
      - fileSize: 47970
    extract:
      - fileKey1: content.data.fileKey
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.uploadUrl, null]

-
    name: 上委托授权书
    api: api/public/fileUploadUrl.yml
    variables:
      - contentMd5: "v/91uFJ2BvFr/VPzYybiKQ=="
      - contentType: "application/octet-stream"
      - fileName: "银行卡.jpeg"
      - fileSize: 47970
    extract:
      - fileKey2: content.data.fileKey
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.uploadUrl, null]

-
    name: 页面版填写邮箱地址后发送验证码
    api: api/psnAuth/manualAuthCode.yml
    variables:
      - email: "<EMAIL>"
      - serviceTypeName: "ORG_ARTIFICIAL"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - contains: [content.data, "-"]

-
    name: 党政机关或事业单位提交人工审核
    api: api/orgAuth/manualSubmit.yml
    variables:
      - mobile: ""
      - authCode: "123456"
      - orgName: $name
      - orgCertNo: $idNumber
      - email: "<EMAIL>"
      - orgCertType: "ORGANIZATION_USC_CODE"
      - authType: "LEGAL_REP_SIGN_AUTHORIZE"
      - photoInfo: $fileKey1
      - legalRepCertNo: "432427189501249863"
      - legalRepName: $orgLegalName
      - proxyPhoto: $fileKey2
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]
      - contains: [content.data.seqNo, $VALIDATE]

-
    name: 获取人工实名审核结果1
    api: api/orgAuth/getOrgManualRst.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.serviceStatus, 1]
      - contains: [content.data.seqNo, $VALIDATE]

-
    name: realname接口查询运营支撑平台要审核的realnameid
    api: api/realname/queryOrgManualList2.yml
    variables:
      - certType: 11
      - certNo: $idNumber
      - serviceStatusForQuery: 1
      - authType: ""
      - bizSource: ""
    extract:
      - realnameId1: content.data.list.0.realnameId
    validate:
      - eq: [status_code,200]
      - contains: [content.message, "成功"]
      - eq: [content.data.list.0.authType, 2]
      - eq: [content.data.list.0.name, $name]

-
    name: realname接口运营支撑平台拒绝审批
    api: api/realname/updatePsnManual.yml
    variables:
      serviceStatus: 1
      realnameId: $realnameId1
      auditorResult: 2
      auditingRemark: "1、填写的认证组织信息与证件照不一致；2、证件照不清晰"
    validate:
      - eq: [status_code,200]
      - eq: [content.serviceId, $realnameId1]

-
    name: 获取人工实名审核结果2
    api: api/orgAuth/getOrgManualRst.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.serviceStatus, 4]
      - contains: [content.data.seqNo, $VALIDATE]

#-
#    name: 上传营业执照--重新上传
#    api: api/public/fileUploadUrl.yml
#    variables:
#      - contentMd5: "v/91uFJ2BvFr/VPzYybiKQ=="
#      - contentType: "application/octet-stream"
#      - fileName: "银行卡.jpeg"
#      - fileSize: 47970
#    extract:
#      - fileKey3: content.data.fileKey
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 0]
#      - eq: [content.message, "成功"]
#      - ne: [content.data.uploadUrl, null]
#
#-
#    name: 上委托授权书--重新上传
#    api: api/public/fileUploadUrl.yml
#    variables:
#      - contentMd5: "v/91uFJ2BvFr/VPzYybiKQ=="
#      - contentType: "application/octet-stream"
#      - fileName: "银行卡.jpeg"
#      - fileSize: 47970
#    extract:
#      - fileKey4: content.data.fileKey
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 0]
#      - eq: [content.message, "成功"]
#      - ne: [content.data.uploadUrl, null]
#
-
    name: 页面版填写邮箱地址后发送验证码--重新发送
    api: api/psnAuth/manualAuthCode.yml
    variables:
      - email: "<EMAIL>"
      - serviceTypeName: "ORG_ARTIFICIAL"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - contains: [content.data, "-"]

#-
#    name: 页面版填写邮箱地址后验证码--重新回填
#    api: api/psnAuth/manualAuthCode.yml
#    variables:
#      - email: "<EMAIL>"
#      - serviceTypeName: "ORG_ARTIFICIAL"
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 0]
#      - eq: [content.message, "成功"]
#      - contains: [content.data, "-"]

-
    name: 党政机关或事业单位提交人工审核--重新提交
    api: api/orgAuth/manualSubmit.yml
    variables:
      - mobile: ""
      - authCode: "123456"
      - orgName: $name
      - orgCertNo: $idNumber
      - email: "<EMAIL>"
      - orgCertType: "ORGANIZATION_USC_CODE"
      - authType: "LEGAL_REP_SIGN_AUTHORIZE"
      - photoInfo: $fileKey1
      - legalRepCertNo: "432427189501249863"
      - legalRepName: $orgLegalName
      - proxyPhoto: $fileKey2
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]
      - contains: [content.data.seqNo, $VALIDATE]

-
    name: 获取人工实名审核结果3
    api: api/orgAuth/getOrgManualRst.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.serviceStatus, 1]
      - contains: [content.data.seqNo, $VALIDATE]

-
    name: realname接口查询运营支撑平台要审核的realnameid--重新查询审批单
    api: api/realname/queryOrgManualList2.yml
    variables:
      - certType: 11
      - certNo: $idNumber
      - serviceStatusForQuery: 1
      - authType: ""
      - bizSource: ""
    extract:
      - realnameId2: content.data.list.0.realnameId
    validate:
      - eq: [status_code,200]
      - contains: [content.message, "成功"]
      - eq: [content.data.list.0.authType, 2]
      - eq: [content.data.list.0.name, $name]

-
    name: realname接口运营支撑平台同意审批
    api: api/realname/updatePsnManual.yml
    variables:
      realnameId: $realnameId2
      auditorResult: 1
      auditingRemark: null
      serviceStatus: 3
    validate:
      - eq: [status_code,200]
      - eq: [content.serviceId, $realnameId2]

-
    name: 获取人工实名审核结果4
    api: api/orgAuth/getOrgManualRst.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.serviceStatus, 3]
      - contains: [content.data.seqNo, $VALIDATE]