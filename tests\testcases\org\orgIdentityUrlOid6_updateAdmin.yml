config:
    name: 获取企业实名地址-更换管理员场景（默认）
    variables:
      - accountId: '66bded89ff08405b90480c90c8efedbd'  #企业oid:esigntest闭环测试02
      - appId: '**********'
      - agentAccountId: '934908b5103542ae81ffa80afe3fbcf8'    #经办人oid: ***********登录账号

teststeps:
-
    name: 获取组织机构实名认证地址-默认
    api: api/orgAuth/orgIdentityUrlOid6.yml
    variables:
      - bizScene: 'DEFAULT'
      - bizSceneContext:
          invalidateSealAuth: false
      - dataCollect:
          clientType: 'WEB'
          bizSource: 'saas_proactive_create'
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - startswith: ["content.data.url", "http"]

