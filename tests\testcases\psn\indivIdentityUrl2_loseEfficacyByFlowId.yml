config:
    name: 获取个人实名认证地址2-失效流程
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取个人实名认证地址2
    api: api/psnAuth/indivIdentityUrl2.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 失效flowid
    api: api/public/loseEfficacyByFlowId.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
