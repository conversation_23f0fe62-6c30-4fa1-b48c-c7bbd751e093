config:
    name: 获取个人实名认证地址+页面版通过运营商三要素完成实名
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建账号
    api: api/public/createByThirdPartyUserId_psnV1.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.accountId, null]

-
    name: 获取个人实名认证地址
    api: api/psnAuth/indivIdentityUrl4.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]


-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版查询个人认证信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.oid, $accountId]

-
    name: 查询个人认证流程状态1
    api: api/psnAuth/queryIndivFlowidStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "INIT"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版发起运营商三要素
    api: api/psnAuth/telecom3FactorInfoAuth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版发起运营商三要素验证码
    api: api/psnAuth/telecom3FactorAuthCode_web.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版运营商三要素验证码校验-错误验证码
    api: api/psnAuth/telecom3Factor.yml
    variables:
      - authcode: 654321
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 30503002]
      - contains: [content.message, "验证码校验失败"]
-
    name: 查询个人认证流程状态2
    api: api/psnAuth/queryIndivFlowidStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "ING"]

-
    name: 页面版运营商三要素验证码校验-正确验证码
    api: api/psnAuth/telecom3Factor.yml
    variables:
      - authcode: 123456
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询个人认证流程状态3
    api: api/psnAuth/queryIndivFlowidStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]