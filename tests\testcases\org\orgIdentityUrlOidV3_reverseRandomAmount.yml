#********迭代调整，该集测作废，但是代码还在，保留
config:
    name: 获取组织机构实名认证地址V3-反向打款认证
    base_url: ${ENV(base_url_rpc)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlV3.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.url, "tsignversion=eve"]

-
    name: 页面版企业经办人认证状态查询
    api: api/orgAuth/agentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.authorizedInV21, true]
      - eq: [content.data.authorisedInUserCenter, true]

-
    name: 页面版企业页面实名认证, 结果接口V2--前端打开页面之后查询实名的进程
    api: api/orgAuth/processonWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版信息比对
    api: api/orgAuth/infoVerify.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]
      - eq: [content.data.operatorType, $operatorType]

-
    name: 页面版查询随机金额业务进度
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]

-
    name: 页面版查询反向打款业务进度1
    api: api/orgAuth/reversePaymentProcessWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]
      - eq: [content.data.message, "子账户未创建"]

-
    name: 页面版发起反向打款V2
    api: api/orgAuth/paymentReverseWeb.yml
    variables:
      finishCash: false
      finishFileKey: false
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - contains: [content.data, "-"]

-
    name: 页面版查询反向打款业务进度2
    api: api/orgAuth/reversePaymentProcessWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "UN_RECEIVED"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]
      - eq: [content.data.message, "无对应打款信息"]
      - count_eq: [content.data.taskGroupDoing, 2]
      - contains: [content.data.taskGroupDoing, "PAY"]
      - contains: [content.data.taskGroupDoing, "AUTHORIZATION"]

-
    name: 页面版重新发起打款
    api: api/orgAuth/reversePaymentTaskFinishWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版查询反向打款业务进度3
    api: api/orgAuth/reversePaymentProcessWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]
      - eq: [content.data.message, "子账户未创建"]

-
    name: 页面版发起反向打款V2-2
    api: api/orgAuth/paymentReverseWeb.yml
    variables:
      finishCash: false
      finishFileKey: false
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - contains: [content.data, "-"]

-
    name: 页面版查询反向打款业务进度4
    api: api/orgAuth/reversePaymentProcessWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "UN_RECEIVED"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]
      - eq: [content.data.message, "无对应打款信息"]
      - count_eq: [content.data.taskGroupDoing, 2]
      - contains: [content.data.taskGroupDoing, "PAY"]
      - contains: [content.data.taskGroupDoing, "AUTHORIZATION"]

-
    name: realname接口查询运营支撑平台要审核的realnameid
    api: api/realname/queryOrgManualList2.yml
    extract:
      - realnameId1: content.data.list.0.realnameId
    validate:
      - eq: [status_code,200]
      - contains: [content.message, "成功"]
      - eq: [content.data.list.0.authType, 4]
      - eq: [content.data.list.0.name, $name]
      - eq: [content.data.list.0.bizSource, $bizSource]

-
    name: realname接口运营支撑平台拒绝审批
    api: api/realname/updatePsnManual.yml
    variables:
      serviceStatus: 9
      realnameId: $realnameId1
      auditorResult: 2
      auditingRemark: "1、未上传授权书；2、授权书被授权人信息与经办人信息不一致"
    validate:
      - eq: [status_code,200]
      - eq: [content.serviceId, $realnameId1]

-
    name: 页面版查询反向打款业务进度5
    api: api/orgAuth/reversePaymentProcessWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "UN_RECEIVED"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]
      - eq: [content.data.message, "无对应打款信息"]
      - count_eq: [content.data.taskGroupDoing, 1]
      - contains: [content.data.taskGroupDoing, "PAY"]
      - contains: [content.data.taskGroupFail, "AUTHORIZATION"]
      - contains: [content.data.auditing, "1、未上传授权书；2、授权书被授权人信息与经办人信息不一致"]

-
    name: 页面版重新发起打款
    api: api/orgAuth/reversePaymentTaskFinishWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版查询反向打款业务进度6
    api: api/orgAuth/reversePaymentProcessWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]
      - eq: [content.data.message, "子账户未创建"]

-
    name: 页面版发起反向打款V2-3
    api: api/orgAuth/paymentReverseWeb.yml
    variables:
      finishCash: false
      finishFileKey: false
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - contains: [content.data, "-"]

-
    name: 页面版查询反向打款业务进度7
    api: api/orgAuth/reversePaymentProcessWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "UN_RECEIVED"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]
      - eq: [content.data.message, "无对应打款信息"]
      - count_eq: [content.data.taskGroupDoing, 2]
      - contains: [content.data.taskGroupDoing, "PAY"]
      - contains: [content.data.taskGroupDoing, "AUTHORIZATION"]

-
    name: realname接口查询运营支撑平台要审核的realnameid2
    api: api/realname/queryOrgManualList2.yml
    extract:
      - realnameId2: content.data.list.0.realnameId
    validate:
      - eq: [status_code,200]
      - contains: [content.message, "成功"]
      - eq: [content.data.list.0.authType, 4]
      - eq: [content.data.list.0.name, $name]
      - eq: [content.data.list.0.bizSource, $bizSource]

-
    name: realname接口运营支撑平台同意审批
    api: api/realname/updatePsnManual.yml
    variables:
      serviceStatus: 3
      realnameId: $realnameId2
      auditorResult: 1
      auditingRemark: null
    validate:
      - eq: [status_code,200]
      - eq: [content.serviceId, $realnameId2]

#缓存问题，15分钟刷新后流程才会成功，待折袖修复后补充查询结果的case