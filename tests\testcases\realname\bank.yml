config:
    name: 添加修改删除银行
    base_url: ${ENV(base_url_realname)}

teststeps:
-
    name: 添加银行
    api: api/realname/addBank.yml
    validate:
      - eq: [status_code,200]

-
    name: 查询银行
    api: api/realname/queryBank.yml
    extract:
      - id: content.bankModelList.0.id
    validate:
      - eq: [status_code,200]
      - eq: [content.bankModelList.0.bankName, $bankName]
      - eq: [content.bankModelList.0.cityName, $cityName]
      - str_eq: [content.bankModelList.0.cityCode, $cityCode]

-
    name: rollback银行
    api: api/realname/rollBackBankCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content, false]

-
    name: 删除银行
    api: api/realname/deleteBank.yml
    validate:
      - eq: [status_code,200]
      - eq: [content, true]
