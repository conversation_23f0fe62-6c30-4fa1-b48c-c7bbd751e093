- config:
    name: 开启高质量供应商二次比对-企业四要素比对-第一次调用供应商为中数智汇
    base_url: ${ENV(base_url)}
    variables:
        operator: "夙瑶"
        Cookie: "access_token=****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; TSIGN.SESSION.SANDBOX=9145533d-f7c8-498e-abdf-287105c93605; TSIGN.SESSION.COMMON=a9f448ee-0a6c-43f5-b6db-1f1edccfe392; redirect_referer=aHR0cDovL3N1cHBvcnQtbWljcm9mZS1tYWluZnJvbnQtdmVyaWZ5LWZ1bi11cGdyYWRlLnByb2plY3RrOHMudHNpZ24uY24vbWljcm9mZS9yZWFsbmFtZS9wcm92aWRlcg==; test_access_token=***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"



- test:
    name: 配置高质量供应商为蚂蚁
    api: api/public/providerManagerSave.yml
    variables:
        name: "MAYI"
        url: "https://prodapigw.cloud.alipay.com"
        account: "LTAI9kGTlVr1a7Wx"
        signKey: null
        weight: "1"
        qualityTag: "HIGH"
        desc: "蚂蚁"
        id: "110"
        type: 3
        errCode: "{\"PROVIDER_SERVICE_CONNECTION_CLOSED\":\"placeholder\",\"PROVIDER_SERVICE_TIMEOUT\":\"placeholder\"}"
        extendJson: "{\"manager\":{},\"client\":{},\"select\":{},\"retry\":{}}"
    validate:
        - eq: [content.code, 0]
        - contains: [content.message, 成功]
    json: {
     "id": $id,
     "name": $name,
     "type": $type,
     "desc": $desc,
     "url": $url,
     "account": $account,
     "signKey": $signKey,
     "weight": $weight,
     "operator": $operator,
     "qualityTag": $qualityTag,
     "errCode": $errCode,
     "extendJson": $extendJson
    }

- test:
    name: 配置供应商中数智汇的权重为999
    api: api/public/providerManagerSave.yml
    variables:
        name: "ZSZH"
        url: "https://api.qichacha.com/FuzzySearch/GetList"
        account: "97cc1a29efe04e8eaef59f789673ab20"
        signKey: "6AA7B3D4D01C8BD3197F81C684FB10C0"
        weight: "999"
        qualityTag: "NORMAL"
        desc: "中数智汇"
        id: "663"
        type: 3
        errCode: null,
        extendJson: "{\"manager\":{},\"client\":{},\"select\":{},\"retry\":{}}"
    validate:
        - eq: [content.code, 0]
        - contains: [content.message, 成功]
    json: {
     "id": $id,
     "name": $name,
     "type": $type,
     "desc": $desc,
     "url": $url,
     "account": $account,
     "signKey": $signKey,
     "weight": $weight,
     "operator": $operator,
     "qualityTag": $qualityTag,
     "errCode": $errCode,
     "extendJson": $extendJson
    }



- test:
    name: 企业名称不一致，统一社会信用代码一致，法人姓名一致，法人证件号一致
    api: api/checkInfo/enterprise4.yml
    variables:
       name: "厦门亿永丰农资有限公司${create_randomId()}"
       orgCode: "91350212M0000M4E0V"
       legalRepName: "郭国森"
       legalRepCertNo: "350212199011233019"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
     "legalRepCertNo": $legalRepCertNo
    }

- test:
    name: 企业名称一致，统一社会信用代码一致，法人姓名不一致，法人证件号不一致
    api: api/checkInfo/enterprise4.yml
    variables:
       name: "厦门亿永丰农资有限公司"
       orgCode: "91350212M0000M4E0V"
       legalRepName: "郭国森create_randomIdStr"
       legalRepCertNo: "350212199011233020"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
     "legalRepCertNo": $legalRepCertNo
    }

- test:
    name: 企业名称一致，统一社会信用代码一致，法人姓名不一致，法人证件号不一致
    api: api/checkInfo/enterprise4.yml
    variables:
       name: "厦门亿永丰农资有限公司${create_randomId()}"
       orgCode: "91350212M0000M4E0V"
       legalRepName: "郭国森create_randomIdStr"
       legalRepCertNo: "350212199011233020"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
     "legalRepCertNo": $legalRepCertNo
    }

- test:
    name: 企业名称不一致，统一社会信用代码不一致，法人姓名不一致，法人证件号不一致
    api: api/checkInfo/enterprise4.yml
    variables:
       name: "厦门亿永丰农资有限公司${create_randomId()}"
       orgCode: "91350212M0000M4E0T"
       legalRepName: "郭国森${create_randomIdStr()}"
       legalRepCertNo: "350212199011233020"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
     "legalRepCertNo": $legalRepCertNo
    }

- test:
    name: 企业名称一致，统一社会信用代码不一致，法人姓名一致，法人证件号一致
    api: api/checkInfo/enterprise4.yml
    variables:
       name: "厦门亿永丰农资有限公司"
       orgCode: "91${create_randomId_num(16)}"
       legalRepName: "郭国森"
       legalRepCertNo: "350212199011233019"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
     "legalRepCertNo": $legalRepCertNo
    }

- test:
    name: 企业名称一致，统一社会信用代码一致，法人姓名不一致，法人证件号一致
    api: api/checkInfo/enterprise4.yml
    variables:
       name: "厦门亿永丰农资有限公司"
       orgCode: "91350212M0000M4E0V"
       legalRepName: "郭国森${create_randomIdStr()}"
       legalRepCertNo: "350212199011233019"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
     "legalRepCertNo": $legalRepCertNo
    }

- test:
    name: 企业名称一致，统一社会信用代码一致，法人姓名一致，法人证件号不一致
    api: api/checkInfo/enterprise4.yml
    variables:
       name: "厦门亿永丰农资有限公司"
       orgCode: "91350212M0000M4E0V"
       legalRepName: "郭国森"
       legalRepCertNo: "${create_randomId()}"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
     "legalRepCertNo": $legalRepCertNo
    }

- test:
    name: 企业名称一致，统一社会信用代码不一致，法人姓名不一致，法人证件号不一致
    api: api/checkInfo/enterprise4.yml
    variables:
       name: "厦门亿永丰农资有限公司"
       orgCode: "91350212M0000M4E0T"
       legalRepName: "郭国森啦"
       legalRepCertNo: "${create_randomId()}"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
     "legalRepCertNo": $legalRepCertNo
    }

- test:
    name: 企业名称不一致，统一社会信用代码一致，法人姓名一致，法人证件号不一致
    api: api/checkInfo/enterprise4.yml
    variables:
       name: "厦门亿永丰农资有限公司${create_randomId()}"
       orgCode: "91350212M0000M4E0V"
       legalRepName: "郭国森"
       legalRepCertNo: "${create_randomId()}"
    validate:
        - eq: [content.code,********]
        - eq: [content.message, "信息比对不通过,请检查信息的正确性"]
    json: {
     "name": $name,
     "orgCode": $orgCode,
     "legalRepName": $legalRepName,
     "legalRepCertNo": $legalRepCertNo
    }


- test:
    name: 重置高质量供应商为空
    api: api/public/providerManagerSave.yml
    variables:
        name: "MAYI"
        url: "https://prodapigw.cloud.alipay.com"
        account: "LTAI9kGTlVr1a7Wx"
        signKey: null
        weight: "1"
        qualityTag: "NORMAL"
        desc: "蚂蚁"
        id: "110"
        type: 3
        errCode: "{\"PROVIDER_SERVICE_CONNECTION_CLOSED\":\"placeholder\",\"PROVIDER_SERVICE_TIMEOUT\":\"placeholder\"}"
        extendJson: "{\"manager\":{},\"client\":{},\"select\":{},\"retry\":{}}"
    validate:
        - eq: [content.code, 0]
        - contains: [content.message, 成功]
    json: {
     "id": $id,
     "name": $name,
     "type": $type,
     "desc": $desc,
     "url": $url,
     "account": $account,
     "signKey": $signKey,
     "weight": $weight,
     "operator": $operator,
     "qualityTag": $qualityTag,
     "errCode": $errCode,
     "extendJson": $extendJson
    }

- test:
    name: 重置供应商中数智汇的权重为1
    api: api/public/providerManagerSave.yml
    variables:
        name: "ZSZH"
        url: "https://api.qichacha.com/FuzzySearch/GetList"
        account: "97cc1a29efe04e8eaef59f789673ab20"
        signKey: "6AA7B3D4D01C8BD3197F81C684FB10C0"
        weight: "1"
        qualityTag: "NORMAL"
        desc: "中数智汇"
        id: "663"
        type: 3
        errCode: null,
        extendJson: "{\"manager\":{},\"client\":{},\"select\":{},\"retry\":{}}"
    validate:
        - eq: [content.code, 0]
        - contains: [content.message, 成功]
    json: {
     "id": $id,
     "name": $name,
     "type": $type,
     "desc": $desc,
     "url": $url,
     "account": $account,
     "signKey": $signKey,
     "weight": $weight,
     "operator": $operator,
     "qualityTag": $qualityTag,
     "errCode": $errCode,
     "extendJson": $extendJson
    }




