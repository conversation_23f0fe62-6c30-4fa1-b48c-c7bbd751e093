config:
    name: 获取个人实名认证地址+页面版选择刷脸实名(刷脸等级为三要素，刷脸流程未终结)
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建账号
    api: api/public/createByThirdPartyUserId_psnV1.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.accountId, null]

-
    name: 获取个人实名认证地址
    api: api/psnAuth/indivIdentityUrl4.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版查询个人认证信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.bankCardNo, $bankCardNo]
      - eq: [content.data.oid, $accountId]

-
    name: 查询个人认证流程状态
    api: api/psnAuth/queryIndivFlowidStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "INIT"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版刷脸前发起运营商三要素
    api: api/psnAuth/faceauthTelecom3FactorInfoAuth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版刷脸前发起运营商三要素后发送验证码
    api: api/psnAuth/faceauthTelecom3FactorSendCode.yml
    variables:
      - sendType: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版刷脸前发起运营商三要素后回填验证码
    api: api/psnAuth/faceauthTelecom3FactorCheckCode.yml
    variables:
      - authcode: 123456
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.data.pass, True]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版发起刷脸
    api: api/psnAuth/faceauth_web.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.authUrl, null]

-
    name: 以轮询的方式查询个人实名刷脸认证结果
    api: api/psnAuth/queryFaceauthPolling.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.livingScore, null]

-
    name: 查询个人认证流程状态2
    api: api/psnAuth/queryIndivFlowidStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "ING"]