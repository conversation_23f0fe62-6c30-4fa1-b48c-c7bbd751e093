config:
    name: 发起银行卡4要素实名认证，校验短信验证码
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起银行卡4要素实名认证
    api: api/psnAuth/bankCard4FactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 根据用户OID查询正在进行中的一次认证记录
    api: api/public/processing.yml
    variables:
      - agentOid: ""
      - oid: $accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]
      - eq: [content.data.status, 1]

-
    name: 校验短信验证码-正确验证码
    api: api/psnAuth/bank4VerifyCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.subFlows.0.subFlowType, "INDIVIDUAL_BANKCARD_4_FACTOR"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.authType, "INDIVIDUAL_BANKCARD_4_FACTOR"]
      - eq: [content.data.indivInfo.name, $name]

-
    name: 根据flowId查询其认证子service的详情 对内服务GET方式
    api: api/public/queryFlowServiceInnerLineGet.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "成功"]
      - eq: [content.data.objectType, "个人"]
      - eq: [content.data.subFlows.0.subFlowType, "个人银行卡四要素"]
      - eq: [content.data.subFlows.0.name, $name]

-
    name: 根据flowId查询其认证子service的详情 对内服务Post方式
    api: api/public/queryFlowServiceInnerLinePost.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "成功"]
      - eq: [content.data.objectType, "个人"]
      - eq: [content.data.subFlows.0.subFlowType, "个人银行卡四要素"]
      - eq: [content.data.subFlows.0.name, $name]

-
    name: 获取verifyCode以及flow的状态
    api: api/psnAuth/getVerifyCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - ne: [content.data.verifyCode, null]

-
    name: 查询存证数据
    api: api/public/getEvidenceData.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.0.extDataMap.certType, $VALIDATE1]
      - eq: [content.data.0.extDataMap.phone, $mobileNo]
      - ne: [content.data.0.extDataMap.idCardNo, null]
      - eq: [content.data.0.extDataMap.name, $name]
      - eq: [content.data.0.extDataMap.bankcardNo, $bankCardNo]
      - eq: [content.data.0.realNameStep, "FOUR_FACTOR_CHECK"]
      - eq: [content.data.0.realNameWay, "FOUR_FACTOR_AUTH"]
      - eq: [content.data.1.extDataMap.smsSend, "123456"]
      - eq: [content.data.1.extDataMap.phoneNo, $mobileNo]
      - eq: [content.data.2.extDataMap.smsFilled, "123456"]
      - eq: [content.data.2.realNameStep, "FILL_VERIFY_CODE"]
      - eq: [content.data.2.realNameWay, "FOUR_FACTOR_AUTH"]

-
    name: 查询用户最近一次认证信息
    api: api/public/lastDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.data.flowId, $flowId]
      - eq: [content.data.status, SUCCESS]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.authType, "INDIVIDUAL_BANKCARD_4_FACTOR"]
      - eq: [content.data.accountId, $accountId]
