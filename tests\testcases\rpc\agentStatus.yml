config:
    name: 企业经办人（包括法人、经办人）实名状态查询
    base_url: ${ENV(base_url_rpc)}

teststeps:
-
    name: 企业经办人（包括法人、经办人）实名状态查询
    api: api/rpc/agentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.operatorType, $operatorType]
      - eq: [content.data.authorized, $authorized]
      - eq: [content.data.authorisedInUserCenter, $authorisedInUserCenter]
