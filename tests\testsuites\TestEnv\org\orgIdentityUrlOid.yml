config:
    name: 获取组织机构实名认证地址
#备注：1.运营支撑平台：默认组织认证类型为对公打款 2.获取组织机构实名地址，后续页面版做信息核验，集测未包含
testcases:
-
    name: 获取组织机构实名认证地址-查询应用配置信息1-$CaseName
    testcase: testcases/org/orgIdentityUrlOid_configAll.yml
    parameters:
        - CaseName-appId-accountId-authType-availableAuthTypes-agentAuthAdvancedEnabled-agentAccountId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-repeatIdentity-VALIDATE1-VALIDATE2:
            - ["不指定默认认证类型和页面显示的认证方式","${ENV(appId)}","${ENV(accountId)}","",[],[],"92cede5d8e924ca48c67c93137a04836","","","","","","",true,6,"ORGANIZATION_TRANSFER"]
            - ["指定默认认证类型为法人授权书认证","${ENV(appId)}","${ENV(accountId)}","ORG_LEGAL_AUTHORIZE",[],[],"92cede5d8e924ca48c67c93137a04836","","","","","","",true,6,"LEGAL_REP_SIGN_AUTHORIZE"]
            - ["指定默认认证类型为企业支付宝","${ENV(appId)}","${ENV(accountId)}","ORG_ZM_AUTHORIZE",[],[],"92cede5d8e924ca48c67c93137a04836","","","","","","",true,6,"ORGANIZATION_ZMXY"]
            - ["指定页面显示的认证方式,只有对公打款","${ENV(appId)}","${ENV(accountId)}","",["ORG_BANK_TRANSFER"],[],"92cede5d8e924ca48c67c93137a04836","","","","","","",true,3,"ORGANIZATION_TRANSFER"]
            - ["指定页面显示的认证方式,只有对公打款和法人授权书","${ENV(appId)}","${ENV(accountId)}","",["ORG_BANK_TRANSFER","ORG_LEGAL_AUTHORIZE"],[],"92cede5d8e924ca48c67c93137a04836","","","","","","",true,4,"ORGANIZATION_TRANSFER"]
            - ["指定默认认证类型为企业支付宝，但可选方式种没有","${ENV(appId)}","${ENV(accountId)}","ORG_ZM_AUTHORIZE",["ORG_BANK_TRANSFER"],[],"92cede5d8e924ca48c67c93137a04836","","","","","","",true,3,"ORGANIZATION_TRANSFER"]
#该功能在党政机关、事业单位项目中已经去掉了            - ["党政机关默认开启人工实名","6fe242dd15fe453ab8a1642ba3257d37","",[],[],"92cede5d8e924ca48c67c93137a04836","","","","","","",true,7,"ORGANIZATION_TRANSFER"]
            - ["1209迭代，指定默认认证增加企业法人","${ENV(appId)}","${ENV(accountId)}","LEGAL_REP_AUTH",[],[],"92cede5d8e924ca48c67c93137a04836","","","","","","",true,6,"LEGAL_REP_AUTH"]

-
    name: 获取组织机构实名认证地址-查询应用配置信息2-$CaseName
    testcase: testcases/org/orgIdentityUrlOid2_configAll.yml
    parameters:
        - CaseName-appId-accountId-agentAccountId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-repeatIdentity-showAgreement-orgUneditableInfo-orgEditableInfo-VALIDATE1-agentBankCardNo-agentMobile:
            - ["appid展示协议页面,参数配置没有传参","${ENV(appId2)}","${ENV(accountId)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,"",[],[],True,"",""]
            - ["appid展示协议页面,参数配置true","${ENV(appId2)}","${ENV(accountId)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,true,[],[],True,"",""]
            - ["appid展示协议页面,参数配置false","${ENV(appId2)}","${ENV(accountId)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,false,[],[],False,"",""]
            - ["appid不展示协议页面,参数配置true","${ENV(appId)}","${ENV(accountId2)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,true,[],[],True,"",""]
            - ["appid不展示协议页面,参数配置false","${ENV(appId)}","${ENV(accountId2)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,false,[],[],False,"",""]
            - ["appid不展示协议页面,参数配置没有传参","${ENV(appId)}","${ENV(accountId2)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,"",[],[],False,"",""]


-
    name: 获取组织机构实名认证地址-查询应用配置信息3-$CaseName
    testcase: testcases/org/orgIdentityUrlOid2_configAll_orgEditableInfo.yml
    parameters:
        - CaseName-appId-accountId-agentAccountId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-repeatIdentity-showAgreement-orgUneditableInfo-orgEditableInfo-VALIDATE1-VALIDATE2-agentBankCardNo-agentMobile:
            - ["已实名oid,设置姓名、证件号可编辑","${ENV(appId2)}","${ENV(accountId2)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,true,[""],["name","certNo"],4,["certNo","legalRepName","agentIdNo","agentName"],"",""]
            - ["已实名oid,不设置任何可编辑参数","${ENV(appId2)}","${ENV(accountId2)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,true,[],[],5,["certNo","legalRepName","agentIdNo","name","agentName"],"",""]
            - ["已实名oid,设置所有参数不可编辑","${ENV(appId2)}","${ENV(accountId2)}","${ENV(psnOid_noRealname)}","","","","","","",true,true,["name","certNo","legalRepName","agentIdNo","agentName","legalRepCertNo","organizationType","agentMobileNo","agentBankCardNo"],[],7,['certNo', 'legalRepName', 'agentBankCardNo', 'agentMobileNo', 'agentIdNo', 'name', 'agentName'],"****************","***********"]
            - ["已实名oid,设置所有参数可编辑","${ENV(appId)}","${ENV(accountId2)}","${ENV(psnOid_noRealname)}","","","","","","",true,true,[],["name","certNo","legalRepName","agentIdNo","agentName","legalRepCertNo","organizationType","agentMobileNo","agentBankCardNo"],1,["certNo"],"****************","***********"]
            - ["未实名oid,不设置任何可编辑参数","${ENV(appId)}","${ENV(accountId)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,true,[],[],2,["agentIdNo","agentName"],"",""]
            - ["未实名oid,设置所有参数不可编辑","${ENV(appId)}","${ENV(accountId)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,true,["name","certNo","legalRepName","agentIdNo","agentName","legalRepCertNo","organizationType","agentMobile","agentBankCardNo"],[],2,['agentIdNo', 'agentName'],"",""]
            - ["未实名oid,设置所有参数可编辑","${ENV(appId)}","${ENV(accountId)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,true,[],["name","certNo","legalRepName","agentIdNo","agentName","legalRepCertNo","organizationType","agentMobile","agentBankCardNo"],1,["agentIdNo"],"",""]

-
    name: 获取企业实名认证地址-********迭代1-$CaseName
    testcase: testcases/org/orgIdentityUrl_1021case1.yml
    parameters:
        - CaseName-creator-name-idType-idNumber-orgLegalIdNumber-orgLegalName-agentAccountId-certNo2-organizationType2-name2-legalRepCertType2-legalRepCertNo2-legalRepName2-agentName2-agentIdNo2-operatorType-certType-agentBankCardNo-agentMobile:
            - ["无GID，oid中无任何信息，实名传参所有信息","${ENV(creatorOid)}","","","","","","${ENV(creatorOid)}","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","武玉华","******************","","ORGANIZATION_USC_CODE"]
            - ["无GID，oid中无任何信息，实名传参所有信息,实名传值的证件类型是用户中心的","${ENV(creatorOid)}","","","","","","${ENV(creatorOid)}","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","武玉华","******************","","CRED_ORG_USCC"]
            - ["无GID，oid中有证件类型、名字、法人名字和法人证件号，实名传参更改证件类型、名字、法人名字和法人证件号，并且增加证件号","${ENV(creatorOid)}","深圳天谷信息科技有限公","CRED_ORG_USCC","","330702198409120432","测试","${ENV(creatorOid)}","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","武玉华","******************",2,"ORGANIZATION_REG_CODE"]
            - ["有GID，oid中有证件类型和证件号，实名传参修改名字、法人名字和法人证件号","${ENV(creatorOid)}","深圳天谷信息科技有限公","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120431","测试","${ENV(creatorOid)}","91440300MA5ERJGK30",null,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_TWCARD","330702198409120432","张晋","武玉华","******************",2,""]
            - ["经办人无GID，非法人，实名传参经办人名字和证件号","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","","330702198409120432","张晋","28f44639272741e2b94154ab568d15de","91440300MA5ERJGK31",null,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120431","张晋","武玉华","******************",2,""]
            - ["经办人有GID，非法人，实名修改经办人名字","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","","330702198409120432","张晋","${ENV(creatorOid)}","91440300MA5ERJGK30",null,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","修改","******************",2,"ORGANIZATION_USC_CODE"]
            - ["经办人无GID，是法人，实名传参经办人名字和证件号","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","","330702198409120432","张晋","28f44639272741e2b94154ab568d15de","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","张晋","330702198409120432",1,"ORGANIZATION_USC_CODE"]
            - ["经办人有GID，是法人，实名修改经办人名字","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","","330702198409120432","张晋","325434ce18cf4966aeb0f4c298317a58","91440300MA5ERJGK30",null,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","","修改","330702198409120432",1,"ORGANIZATION_USC_CODE"]

-
    name: 获取企业实名认证地址-********迭代2-$CaseName
    testcase: testcases/org/orgIdentityUrl_1021case2.yml
    parameters:
        - CaseName-creator-name-idType-idNumber-orgLegalIdNumber-orgLegalName-agentAccountId-certNo2-organizationType2-name2-legalRepCertType2-legalRepCertNo2-legalRepName2-agentName2-agentIdNo2-operatorType-VALIDATE1-VALIDATE2-certType-agentBankCardNo-agentMobile:
            - ["有GID，oid中有证件号，实名修改证件号","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋","${ENV(creatorOid)}","91440300MA5ERJGK31",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","武玉华","******************",2,********,"参数错误：输入证件号与账号不匹配","ORGANIZATION_USC_CODE"]
            - ["经办人非法人，有GID，实名修改证件号","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","","330702198409120432","张晋","${ENV(creatorOid)}","91440300MA5ERJGK31",2,"深圳天谷信息科技有限公","INDIVIDUAL_CH_IDCARD","330702198409120431","测试","武玉华","******************",2,********,"参数错误：输入经办人证件号与经办人账号不匹配","ORGANIZATION_REG_CODE"]
            - ["经办人有GID，是法人，实名修改经办人身份证号","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","","330702198409120432","张晋","325434ce18cf4966aeb0f4c298317a58","91440300MA5ERJGK31",2,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋","张晋","330702198409120431",1,********,"参数错误：当前经办人身份为法定代表人，输入法定代表人证件号与经办人证件号不匹配","ORGANIZATION_USC_CODE"]

-
    name: 获取企业实名认证地址-********迭代-$CaseName
    testcase: testcases/org/orgIdentityUrlOid_configAll_agentAvailableAuthTypes.yml
    parameters:
        - CaseName-appId-accountId-agentAccountId-agentAvailableAuthTypes-VALIDATE1:
            - ["经办人实名指定了运营商三要素","${ENV(appId)}","${ENV(accountId)}","${ENV(creatorOid)}",["PSN_TELECOM_AUTHCODE"], ["INDIVIDUAL_TELECOM_3_FACTOR"]]
            - ["经办人实名指定了卡四","${ENV(appId)}","${ENV(accountId)}","${ENV(creatorOid)}",["PSN_BANK4_AUTHCODE"],["INDIVIDUAL_BANKCARD_4_FACTOR"]]
            - ["经办人实名指定了刷脸","${ENV(appId)}","${ENV(accountId)}","${ENV(creatorOid)}",["PSN_FACEAUTH_BYURL"],['INDIVIDUAL_FACEAUTH', 'FACEAUTH_ZMXY', 'FACEAUTH_TECENT_CLOUD', 'FACEAUTH_ESIGN', 'FACEAUTH_WE_CHAT_FACE']]
            - ["经办人实名指定了支付宝刷脸","${ENV(appId)}","${ENV(accountId)}","${ENV(creatorOid)}",["PSN_FACEAUTH_BYURL_ZHIMA"],['FACEAUTH_ZMXY', 'INDIVIDUAL_FACEAUTH']]
            - ["经办人实名指定了微众刷脸","${ENV(appId)}","${ENV(accountId)}","${ENV(creatorOid)}",["PSN_FACEAUTH_BYURL_WEIZHONG"],['FACEAUTH_TECENT_CLOUD', 'INDIVIDUAL_FACEAUTH']]
            - ["经办人实名指定了自研刷脸","${ENV(appId)}","${ENV(accountId)}","${ENV(creatorOid)}",["PSN_FACEAUTH_BYURL_ESIGN"],['FACEAUTH_ESIGN', 'INDIVIDUAL_FACEAUTH']]
            - ["经办人实名指定了微信小程序刷脸","${ENV(appId)}","${ENV(accountId)}","${ENV(creatorOid)}",["PSN_FACEAUTH_BYURL_WE_CHAT"],['FACEAUTH_WE_CHAT_FACE', 'INDIVIDUAL_FACEAUTH']]
            - ["经办人实名指定了刷脸组合","${ENV(appId)}","${ENV(accountId)}","${ENV(creatorOid)}",["PSN_FACEAUTH_BYURL","PSN_FACEAUTH_BYURL_ZHIMA"],['INDIVIDUAL_FACEAUTH', 'FACEAUTH_ZMXY']]
            - ["经办人实名指定了所有方式组合","${ENV(appId)}","${ENV(accountId)}","${ENV(creatorOid)}",["PSN_TELECOM_AUTHCODE","PSN_BANK4_AUTHCODE","PSN_FACEAUTH_BYURL_WEIZHONG","PSN_FACEAUTH_BYURL_ZHIMA","PSN_FACEAUTH_BYURL_ESIGN","PSN_FACEAUTH_BYURL_WE_CHAT"],['INDIVIDUAL_BANKCARD_4_FACTOR', 'FACEAUTH_TECENT_CLOUD', 'FACEAUTH_ZMXY', 'FACEAUTH_ESIGN', 'FACEAUTH_WE_CHAT_FACE', 'INDIVIDUAL_FACEAUTH','INDIVIDUAL_TELECOM_3_FACTOR']]

-
    name: 不同appid获取组织机构实名认证地址生成的flowid不同-$CaseName
    testcase: testcases/org/appidIsolation.yml
    parameters:
        - CaseName-accountId-agentAccountId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-repeatIdentity-showAgreement-orgUneditableInfo-orgEditableInfo-agentBankCardNo-agentMobile:
            - ["不同appid","${ENV(accountId2)}","92cede5d8e924ca48c67c93137a04836","","","","","","",true,true,[""],["name","certNo"],"",""]

-
    name: 智能排序-$CaseName
    testcase: testcases/org/orgIdentityUrlOidV3_configAll_smartOrgAuthType.yml
    parameters:
        - CaseName-appId-accountId-agentAccountId-authType-availableAuthTypes-VALIDATE-name-idNumber-legalRepCertType-orgLegalName-orgLegalIdNumber-version-agentAvailableAuthTypes:
#开启智能排序，有交集场景-默认全开
            - ["91企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_91)}","${ENV(creatorOid)}","",[],["LEGAL_REP_SIGN_AUTHORIZE", "ORGANIZATION_TRANSFER", "ORGANIZATION_TRANSFER_RANDOM_AMOUNT", "ORGANIZATION_REVERSE_PAYMENT", "ORGANIZATION_ZMXY"],"深圳天谷信息科技有限公司","91440300MA5ERJGK30","","张晋","","v2",[]]
            - ["91企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_91)}","${ENV(agentAccountId_91fr)}","",[],['ORGANIZATION_LEGAL_WILL_AUTH', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"深圳天谷信息科技有限公司","91440300MA5ERJGK30","","张晋","","v2",[]]
            - ["92企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_92)}","${ENV(creatorOid)}","",[],["LEGAL_REP_SIGN_AUTHORIZE", "ORGANIZATION_TRANSFER", "ORGANIZATION_TRANSFER_RANDOM_AMOUNT", "ORGANIZATION_REVERSE_PAYMENT", "ORGANIZATION_ZMXY"],"孔铁矿","92130183MA7DAFQQ5A","","孔铁矿","","v2",[]]
            - ["92企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_92)}","${ENV(agentAccountId_92fr)}","",[],['ORGANIZATION_LEGAL_WILL_AUTH', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"孔铁矿","92130183MA7DAFQQ5A","","孔铁矿","","v2",[]]
#            - ["有限合伙企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_hehuo1)}","${ENV(creatorOid)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
#            - ["有限合伙企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_hehuo1)}","${ENV(agentAccountId_hehuo1)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_LEGAL_WILL_AUTH', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
#            - ["有限合伙企业（法人是企业），开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_hehuo2)}","${ENV(creatorOid)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"深圳市腾讯信达有限合伙企业（有限合伙）","91440300MA5ENQ5K0C","","深圳市腾讯博远科技有限公司","","v2",[]]
            - ["12企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_12)}","${ENV(creatorOid)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["12企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_12)}","${ENV(agentAccountId_12fr)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["11企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_11)}","${ENV(creatorOid)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"广西科学院","12450000498505056J","","元昌安","","v2",[]]
            - ["31律所，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_31)}","${ENV(creatorOid)}","",[],['ORGANIZATION_ZMXY', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["31律所，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_31)}","${ENV(agentAccountId_31fr)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["93农信社，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_93)}","${ENV(creatorOid)}","",[],['LEGAL_REP_SIGN_AUTHORIZE', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"沽源县富东黑猪养殖专业合作社","93130724074892304T","","邓永海","","v2",[]]
            - ["93农信社，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_93)}","${ENV(agentAccountId_93fr)}","",[],['ORGANIZATION_LEGAL_WILL_AUTH', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"沽源县富东黑猪养殖专业合作社","93130724074892304T","","邓永海","","v2",[]]
            - ["其他组织，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_qita)}","${ENV(creatorOid)}","",[],['ORGANIZATION_ZMXY', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
            - ["其他组织，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId4)}","${ENV(accountId_qita)}","${ENV(agentAccountId_qita)}","",[],['ORGANIZATION_ZMXY', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
#开启智能排序，无交集场景，91、92、93没有无交集场景
#            - ["有限合伙企业，开启智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_hehuo1)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
#            - ["有限合伙企业，开启智能排序-经办人是法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_hehuo1)}","${ENV(agentAccountId_hehuo1)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_LEGAL_WILL_AUTH', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
#            - ["有限合伙企业（法人是企业），开启智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_hehuo2)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"深圳市腾讯信达有限合伙企业（有限合伙）","91440300MA5ENQ5K0C","","深圳市腾讯博远科技有限公司","","v2",[]]
            - ["12企业，开启智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_12)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["12企业，开启智能排序-经办人是法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_12)}","${ENV(agentAccountId_12fr)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["11企业，开启智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_11)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"广西科学院","12450000498505056J","","元昌安","","v2",[]]
            - ["31律所，开启智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_31)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["31律所，开启智能排序-经办人是法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_31)}","${ENV(agentAccountId_31fr)}","ORG_LEGAL_AUTHORIZE",["ORG_LEGAL_AUTHORIZE"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["其他组织，开启智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_qita)}","${ENV(creatorOid)}","ORG_LEGAL_AUTHORIZE",["ORG_LEGAL_AUTHORIZE"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
            - ["其他组织，开启智能排序-经办人是法人，可用认证方式和智能推荐无交集","${ENV(appId4)}","${ENV(accountId_qita)}","${ENV(agentAccountId_qita)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT', 'ORGANIZATION_ZMXY'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
#开启智能排序，有交集场景-交集小部分
            - ["91企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集","${ENV(appId4)}","${ENV(accountId_91)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['LEGAL_REP_SIGN_AUTHORIZE'],"深圳天谷信息科技有限公司","91440300MA5ERJGK30","","张晋","","v2",[]]
            - ["91企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集","${ENV(appId4)}","${ENV(accountId_91)}","${ENV(agentAccountId_91fr)}","ORG_LEGAL_AUTHORIZE",["ORG_LEGAL_AUTHORIZE"],['ORGANIZATION_LEGAL_WILL_AUTH'],"深圳天谷信息科技有限公司","91440300MA5ERJGK30","","张晋","","v2",[]]
            - ["92企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集","${ENV(appId4)}","${ENV(accountId_92)}","${ENV(creatorOid)}","ORG_LEGAL_AUTHORIZE",["ORG_LEGAL_AUTHORIZE"],["LEGAL_REP_SIGN_AUTHORIZE"],"孔铁矿","92130183MA7DAFQQ5A","","孔铁矿","","v2",[]]
            - ["92企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集","${ENV(appId4)}","${ENV(accountId_92)}","${ENV(agentAccountId_92fr)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['ORGANIZATION_LEGAL_WILL_AUTH'],"孔铁矿","92130183MA7DAFQQ5A","","孔铁矿","","v2",[]]
            - ["93农信社，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集","${ENV(appId4)}","${ENV(accountId_93)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["ORG_LEGAL_AUTHORIZE"],['LEGAL_REP_SIGN_AUTHORIZE'],"沽源县富东黑猪养殖专业合作社","93130724074892304T","","邓永海","","v2",[]]
            - ["93农信社，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集","${ENV(appId4)}","${ENV(accountId_93)}","${ENV(agentAccountId_93fr)}","ORG_LEGAL_AUTHORIZE",["LEGAL_REP_AUTH"],['ORGANIZATION_LEGAL_WILL_AUTH'],"沽源县富东黑猪养殖专业合作社","93130724074892304T","","邓永海","","v2",[]]
            - ["有限合伙企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_hehuo1)}","${ENV(creatorOid)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
            - ["有限合伙企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_hehuo1)}","${ENV(agentAccountId_hehuo1)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
            - ["有限合伙企业（法人是企业），开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_hehuo2)}","${ENV(creatorOid)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"深圳市腾讯信达有限合伙企业（有限合伙）","91440300MA5ENQ5K0C","","深圳市腾讯博远科技有限公司","","v2",[]]
            - ["12企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_12)}","${ENV(creatorOid)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["12企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_12)}","${ENV(agentAccountId_12fr)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["11企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_11)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],[ 'ORGANIZATION_ZMXY'],"广西科学院","12450000498505056J","","元昌安","","v2",[]]
            - ["31律所，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_31)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],[ 'ORGANIZATION_ZMXY'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["31律所，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_31)}","${ENV(agentAccountId_31fr)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_REVERSE_PAYMENT'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["其他组织，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_qita)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_ZMXY'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
            - ["其他组织，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId4)}","${ENV(accountId_qita)}","${ENV(agentAccountId_qita)}","ORG_BANK_TRANSFER",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_ZMXY'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
#关闭智能排序，无交集场景，91、92、93没有无交集场景
            - ["有限合伙企业，关闭智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_hehuo1)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_ZMXY'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
            - ["有限合伙企业，关闭智能排序-经办人是法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_hehuo1)}","${ENV(agentAccountId_hehuo1)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_ZMXY'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
            - ["有限合伙企业（法人是企业），关闭智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_hehuo2)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_ZMXY'],"深圳市腾讯信达有限合伙企业（有限合伙）","91440300MA5ENQ5K0C","","深圳市腾讯博远科技有限公司","","v2",[]]
            - ["12企业，关闭智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_12)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['LEGAL_REP_SIGN_AUTHORIZE'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["12企业，关闭智能排序-经办人是法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_12)}","${ENV(agentAccountId_12fr)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['LEGAL_REP_SIGN_AUTHORIZE'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["11企业，关闭智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_11)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['LEGAL_REP_SIGN_AUTHORIZE'],"广西科学院","12450000498505056J","","元昌安","","v2",[]]
            - ["31律所，关闭智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_31)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['LEGAL_REP_SIGN_AUTHORIZE'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["31律所，关闭智能排序-经办人是法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_31)}","${ENV(agentAccountId_31fr)}","ORG_LEGAL_AUTHORIZE",["ORG_LEGAL_AUTHORIZE"],['LEGAL_REP_SIGN_AUTHORIZE'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["其他组织，关闭智能排序-经办人非法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_qita)}","${ENV(creatorOid)}","ORG_LEGAL_AUTHORIZE",["ORG_LEGAL_AUTHORIZE"],['LEGAL_REP_SIGN_AUTHORIZE'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
            - ["其他组织，关闭智能排序-经办人是法人，可用认证方式和智能推荐无交集","${ENV(appId5)}","${ENV(accountId_qita)}","${ENV(agentAccountId_qita)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['LEGAL_REP_SIGN_AUTHORIZE'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
#关闭智能排序，有交集场景-交集小部分
            - ["91企业，关闭智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_91)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['LEGAL_REP_SIGN_AUTHORIZE'],"深圳天谷信息科技有限公司","91440300MA5ERJGK30","","张晋","","v2",[]]
            - ["91企业，关闭智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_91)}","${ENV(agentAccountId_91fr)}","ORG_LEGAL_AUTHORIZE",["ORG_LEGAL_AUTHORIZE"],['ORGANIZATION_LEGAL_WILL_AUTH'],"深圳天谷信息科技有限公司","91440300MA5ERJGK30","","张晋","","v2",[]]
            - ["92企业，关闭智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_92)}","${ENV(creatorOid)}","ORG_LEGAL_AUTHORIZE",["ORG_LEGAL_AUTHORIZE"],["LEGAL_REP_SIGN_AUTHORIZE"],"孔铁矿","92130183MA7DAFQQ5A","","孔铁矿","","v2",[]]
            - ["92企业，关闭智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_92)}","${ENV(agentAccountId_92fr)}","LEGAL_REP_AUTH",["LEGAL_REP_AUTH"],['ORGANIZATION_LEGAL_WILL_AUTH'],"孔铁矿","92130183MA7DAFQQ5A","","孔铁矿","","v2",[]]
            - ["93农信社，关闭智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_93)}","${ENV(creatorOid)}","LEGAL_REP_AUTH",["ORG_LEGAL_AUTHORIZE"],['LEGAL_REP_SIGN_AUTHORIZE'],"沽源县富东黑猪养殖专业合作社","93130724074892304T","","邓永海","","v2",[]]
            - ["93农信社，关闭智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_93)}","${ENV(agentAccountId_93fr)}","ORG_LEGAL_AUTHORIZE",["LEGAL_REP_AUTH"],['ORGANIZATION_LEGAL_WILL_AUTH'],"沽源县富东黑猪养殖专业合作社","93130724074892304T","","邓永海","","v2",[]]
            - ["有限合伙企业，关闭智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_hehuo1)}","${ENV(creatorOid)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
            - ["有限合伙企业，关闭智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_hehuo1)}","${ENV(agentAccountId_hehuo1)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
            - ["有限合伙企业（法人是企业），关闭智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_hehuo2)}","${ENV(creatorOid)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"深圳市腾讯信达有限合伙企业（有限合伙）","91440300MA5ENQ5K0C","","深圳市腾讯博远科技有限公司","","v2",[]]
            - ["12企业，关闭智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_12)}","${ENV(creatorOid)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["12企业，关闭智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_12)}","${ENV(agentAccountId_12fr)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["11企业，关闭智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_11)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],[ 'ORGANIZATION_ZMXY'],"广西科学院","12450000498505056J","","元昌安","","v2",[]]
            - ["31律所，关闭智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_31)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],[ 'ORGANIZATION_ZMXY'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["31律所，关闭智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_31)}","${ENV(agentAccountId_31fr)}","ORG_BANK_TRANSFER",["ORG_BANK_TRANSFER"],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["其他组织，关闭智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_qita)}","${ENV(creatorOid)}","ORG_ZM_AUTHORIZE",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_ZMXY'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
            - ["其他组织，关闭智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式开启部分","${ENV(appId5)}","${ENV(accountId_qita)}","${ENV(agentAccountId_qita)}","ORG_BANK_TRANSFER",["ORG_ZM_AUTHORIZE"],['ORGANIZATION_ZMXY'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
#关闭智能排序，有交集场景-默认全开
            - ["91企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_91)}","${ENV(creatorOid)}","",[],["LEGAL_REP_SIGN_AUTHORIZE", "ORGANIZATION_TRANSFER", "ORGANIZATION_TRANSFER_RANDOM_AMOUNT", "ORGANIZATION_ZMXY"],"深圳天谷信息科技有限公司","91440300MA5ERJGK30","","张晋","","v2",[]]
            - ["91企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_91)}","${ENV(agentAccountId_91fr)}","",[],['ORGANIZATION_LEGAL_WILL_AUTH', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_ZMXY'],"深圳天谷信息科技有限公司","91440300MA5ERJGK30","","张晋","","v2",[]]
            - ["92企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_92)}","${ENV(creatorOid)}","",[],["LEGAL_REP_SIGN_AUTHORIZE", "ORGANIZATION_TRANSFER", "ORGANIZATION_TRANSFER_RANDOM_AMOUNT", "ORGANIZATION_ZMXY"],"孔铁矿","92130183MA7DAFQQ5A","","孔铁矿","","v2",[]]
            - ["92企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_92)}","${ENV(agentAccountId_92fr)}","",[],['ORGANIZATION_LEGAL_WILL_AUTH', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_ZMXY'],"孔铁矿","92130183MA7DAFQQ5A","","孔铁矿","","v2",[]]
#            - ["有限合伙企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_hehuo1)}","${ENV(creatorOid)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
#            - ["有限合伙企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_hehuo1)}","${ENV(agentAccountId_hehuo1)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_LEGAL_WILL_AUTH', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"杭州书契网络科技合伙企业(有限合伙)","91330108MA2KGB2B9B","","金宏洲","","v2",[]]
#            - ["有限合伙企业（法人是企业），开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_hehuo2)}","${ENV(creatorOid)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"深圳市腾讯信达有限合伙企业（有限合伙）","91440300MA5ENQ5K0C","","深圳市腾讯博远科技有限公司","","v2",[]]
            - ["12企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_12)}","${ENV(creatorOid)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_ZMXY'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["12企业，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_12)}","${ENV(agentAccountId_12fr)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_ZMXY'],"岑巩县公证处","12522626MB0L508919","","周超","","v2",[]]
            - ["11企业，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_11)}","${ENV(creatorOid)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_ZMXY'],"广西科学院","12450000498505056J","","元昌安","","v2",[]]
            - ["31律所，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_31)}","${ENV(creatorOid)}","",[],['ORGANIZATION_ZMXY', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["31律所，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_31)}","${ENV(agentAccountId_31fr)}","",[],['ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_ZMXY'],"北京财华律师事务所","31110000MD02434566","","李立柱","","v2",[]]
            - ["93农信社，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_93)}","${ENV(creatorOid)}","",[],['LEGAL_REP_SIGN_AUTHORIZE', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_ZMXY'],"沽源县富东黑猪养殖专业合作社","93130724074892304T","","邓永海","","v2",[]]
            - ["93农信社，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_93)}","${ENV(agentAccountId_93fr)}","",[],['ORGANIZATION_LEGAL_WILL_AUTH', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT', 'ORGANIZATION_ZMXY'],"沽源县富东黑猪养殖专业合作社","93130724074892304T","","邓永海","","v2",[]]
            - ["其他组织，开启智能排序-经办人非法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_qita)}","${ENV(creatorOid)}","",[],['ORGANIZATION_ZMXY', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]
            - ["其他组织，开启智能排序-经办人是法人，可用认证方式和智能推荐有交集，可用认证方式V3默认全开","${ENV(appId5)}","${ENV(accountId_qita)}","${ENV(agentAccountId_qita)}","",[],['ORGANIZATION_ZMXY', 'ORGANIZATION_TRANSFER', 'ORGANIZATION_TRANSFER_RANDOM_AMOUNT'],"蒙城幸福普通专科门诊部","52341622MJA7096585","","李自新","","v2",[]]

-
    name: 对公打款授权书是否展示-$CaseName
    testcase: testcases/org/orgIdentityUrlOid_configAll_7488payAuth.yml
    parameters:
        - CaseName-appId-accountId-VALIDATE:
            - ["运营支撑开启默认配置，7488下创建的oid","${ENV(appId5)}","38c40fd8a26b42e19aebe11d902dbd95",true]
            - ["运营支撑开启默认配置，非7488下创建的oid","${ENV(appId5)}","edadc68de0924e6884849ec1871609d3",false]
            - ["运营支撑开启禁用模式，7488下创建的oid","${ENV(appId4)}","38c40fd8a26b42e19aebe11d902dbd95",false]
            - ["运营支撑开启禁用模式，非7488下创建的oid","${ENV(appId4)}","edadc68de0924e6884849ec1871609d3",false]
            - ["运营支撑开启模式，7488下创建的oid","${ENV(appId2)}","38c40fd8a26b42e19aebe11d902dbd95",true]
            - ["运营支撑开启模式，非7488下创建的oid","${ENV(appId2)}","edadc68de0924e6884849ec1871609d3",true]

-
    name: 获取企业实名认证地址-********迭代新增经办人银行卡可传参-$CaseName
    testcase: testcases/org/orgIdentityUrl_********case1.yml
    parameters:
        - CaseName-appId-name-idType-idNumber-mobile-email-cardNo-accountId-agentBankCardNo-agentMobile-orgUneditableInfo-orgEditableInfo:
            - ["账号没有数据，传参经办人手机号和银行卡号","${ENV(appId)}","测试一","","","","","","${ENV(accountId)}","6220303030303030303","***********",[""],["name","certNo"]]
            - ["账号有数据，传参经办人手机号和银行卡号与账号信息不一致","${ENV(appId)}","测试二","","******************","***********","","6220303030303030301","${ENV(accountId)}","6220303030303030303","***********",[""],["name","certNo"]]

-
    name: 获取企业实名认证地址-********迭代新增经办人可选实名方式-$CaseName
    testcase: testcases/org/orgIdentityUrl_********case2.yml
    parameters:
        - CaseName-appId-name-idType-idNumber-mobile-email-cardNo-accountId-defaultAgentAuthType-agentAvailableAuthTypes-VALIDATE1-VALIDATE2:
            - ["经办人可选方式等于默认方式","${ENV(appId)}","测试一","","","","","","${ENV(accountId)}","PSN_BANK4_AUTHCODE",["PSN_BANK4_AUTHCODE"],"INDIVIDUAL_BANKCARD_4_FACTOR",["INDIVIDUAL_BANKCARD_4_FACTOR"]]
            - ["经办人默认方式在可选方式里","${ENV(appId)}","测试二","","******************","***********","","6220303030303030301","${ENV(accountId)}","PSN_TELECOM_AUTHCODE",["PSN_TELECOM_AUTHCODE","PSN_FACEAUTH_BYURL"],"INDIVIDUAL_TELECOM_3_FACTOR",['INDIVIDUAL_TELECOM_3_FACTOR', 'INDIVIDUAL_FACEAUTH', 'FACEAUTH_ZMXY', 'FACEAUTH_TECENT_CLOUD', 'FACEAUTH_ESIGN', 'FACEAUTH_WE_CHAT_FACE']]
            - ["经办人默认方式不在可选方式里","${ENV(appId)}","测试二","","******************","***********","","6220303030303030301","${ENV(accountId)}","PSN_BANK4_AUTHCODE",["PSN_TELECOM_AUTHCODE","PSN_FACEAUTH_BYURL"],"INDIVIDUAL_TELECOM_3_FACTOR",['INDIVIDUAL_TELECOM_3_FACTOR', 'INDIVIDUAL_FACEAUTH', 'FACEAUTH_ZMXY', 'FACEAUTH_TECENT_CLOUD', 'FACEAUTH_ESIGN', 'FACEAUTH_WE_CHAT_FACE']]
            - ["经办人默认方式为空","${ENV(appId)}","测试二","","******************","***********","","6220303030303030301","${ENV(accountId)}","",["PSN_TELECOM_AUTHCODE","PSN_FACEAUTH_BYURL"],"INDIVIDUAL_TELECOM_3_FACTOR",['INDIVIDUAL_TELECOM_3_FACTOR', 'INDIVIDUAL_FACEAUTH', 'FACEAUTH_ZMXY', 'FACEAUTH_TECENT_CLOUD', 'FACEAUTH_ESIGN', 'FACEAUTH_WE_CHAT_FACE']]

-
    name: 获取企业实名认证地址-重复发起实名，主体信息变更-$CaseName
    testcase: testcases/org/orgIdentityUrl_********case3.yml
    parameters:
        - CaseName-appId-agentAccountId-accountId-agentBankCardNo1-agentMobile1-orgUneditableInfo1-orgEditableInfo1-agentBankCardNo2-agentMobile2-orgUneditableInfo2-orgEditableInfo2-VALIDATE1:
            - ["经办人手机号银行卡号都是空到传值，参数是否可编辑都是从不限制到指定","${ENV(appId)}","943f63abaa2747169763ecbf3eccdf3b","3d60842dbf094770933826e51a365b4a","","",[],[],"*****************","***********",["agentBankCardNo"],["agentMobileNo"],"agentBankCardNo"]
            - ["经办人手机号银行卡号都是A到B，参数否可编辑是反过来","${ENV(appId)}","943f63abaa2747169763ecbf3eccdf3b","3d60842dbf094770933826e51a365b4a","*****************","***********",["agentBankCardNo"],["agentMobile"],"*****************","***********",["agentMobileNo"],["agentBankCardNo"],"agentMobileNo"]

-
    name: 获取企业实名认证地址-重复发起实名，修改经办人认证方式-$CaseName
    testcase: testcases/org/orgIdentityUrl_********case4.yml
    parameters:
        - CaseName-appId-agentAccountId-accountId-defaultAgentAuthType1-agentAvailableAuthTypes1-defaultAgentAuthType2-agentAvailableAuthTypes2-VALIDATE1-VALIDATE2:
            - ["经办人认证方式从空到指定","${ENV(appId)}","943f63abaa2747169763ecbf3eccdf3b","3d60842dbf094770933826e51a365b4a","",[],"PSN_BANK4_AUTHCODE",["PSN_BANK4_AUTHCODE"],['INDIVIDUAL_BANKCARD_4_FACTOR'],"INDIVIDUAL_BANKCARD_4_FACTOR"]
            - ["经办人认证方式从指定到空","${ENV(appId)}","943f63abaa2747169763ecbf3eccdf3b","3d60842dbf094770933826e51a365b4a","PSN_BANK4_AUTHCODE",["PSN_BANK4_AUTHCODE"],"",[],['INDIVIDUAL_BANKCARD_4_FACTOR', 'INDIVIDUAL_TELECOM_3_FACTOR', 'INDIVIDUAL_FACEAUTH', 'FACEAUTH_TECENT_CLOUD', 'FACEAUTH_DING_TALK', 'FACEAUTH_WE_CHAT_FACE', 'FACEAUTH_ZMXY', 'FACEAUTH_ESIGN'],"INDIVIDUAL_BANKCARD_4_FACTOR"]
            - ["经办人认证方式从空到指定可用认证方式","${ENV(appId)}","943f63abaa2747169763ecbf3eccdf3b","3d60842dbf094770933826e51a365b4a","",[],"",["PSN_BANK4_AUTHCODE","PSN_TELECOM_AUTHCODE"],['INDIVIDUAL_BANKCARD_4_FACTOR', 'INDIVIDUAL_TELECOM_3_FACTOR'],"INDIVIDUAL_BANKCARD_4_FACTOR"]

-
    name: 获取企业实名认证地址(认证授权RPC地址)-$CaseName
    testcase: testcases/org/createOuidOrganizationIdentityAuthUrl_getInfo.yml
    parameters:
        - CaseName-appId-name-idType-idNumber-mobile-email-cardNo-accountId-agentBankCardNo-agentMobile-orgUneditableInfo-orgEditableInfo-defaultAgentAuthType-agentAvailableAuthTypes-VALIDATE1:
            - ["账号没有数据，传参经办人手机号和银行卡号","${ENV(appId)}","测试一","","","","","","${ENV(accountId)}","6220303030303030303","***********",["agentMobileNo"],[],"",[],"agentMobileNo"]
            - ["账号有数据，传参经办人手机号和银行卡号与账号信息不一致","${ENV(appId)}","测试二","","******************","***********","","6220303030303030301","${ENV(accountId)}","6220303030303030303","***********",["agentMobileNo","agentBankCardNo"],[],"",[],"agentBankCardNo"]

