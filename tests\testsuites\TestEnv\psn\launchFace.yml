config:
    name: 发起个人刷脸实名/核身认证并且查询认证结果

testcases:
#********迭代支持callback为空
-
    name: 发起个人刷脸核身认证-$CaseName
    testcase: testcases/psn/launchFace.yml
    parameters:
        - CaseName-appId-name-idNo-faceauthMode-callbackUrl-VALIDATE1:
            - ["芝麻刷脸","${ENV(appId)}","武玉华","******************","ZHIMACREDIT","http://www.baidu.com","FACEAUTH_ZMXY"]
            - ["微众刷脸","${ENV(appId)}","武玉华","******************","TENCENT","http://www.baidu.com","FACEAUTH_TECENT_CLOUD"]
            - ["自研刷脸","${ENV(appId)}","武玉华","******************","ESIGN","http://www.baidu.com","FACEAUTH_ESIGN"]
            - ["年龄大于14但小于18，appid配置了14岁以上","${ENV(appId2)}","测试年龄","110101200711158271","ESIGN","http://www.baidu.com","FACEAUTH_ESIGN"]
            - ["芝麻刷脸","${ENV(appId)}","武玉华","******************","ZHIMACREDIT","","FACEAUTH_ZMXY"]
            - ["微众刷脸","${ENV(appId)}","武玉华","******************","TENCENT","","FACEAUTH_TECENT_CLOUD"]
            - ["自研刷脸","${ENV(appId)}","武玉华","******************","ESIGN","","FACEAUTH_ESIGN"]

#********迭代支持callback为空
-
    name: 发起个人刷脸实名认证-$CaseName
    testcase: testcases/psn/launchFaceOid.yml
    parameters:
        - CaseName-appId-accountId-faceauthMode-repetition-callbackUrl-VALIDATE1-VALIDATE2:
            - ["芝麻刷脸","${ENV(appId)}","${ENV(creatorOid)}","ZHIMACREDIT",true,"http://www.baidu.com","FACEAUTH_ZMXY","武玉华"]
            - ["微众刷脸","${ENV(appId)}","${ENV(creatorOid)}","TENCENT",true,"http://www.baidu.com","FACEAUTH_TECENT_CLOUD","武玉华"]
            - ["自研刷脸","${ENV(appId)}","${ENV(creatorOid)}","ESIGN",true,"http://www.baidu.com","FACEAUTH_ESIGN","武玉华"]
            - ["芝麻刷脸","${ENV(appId)}","${ENV(creatorOid)}","ZHIMACREDIT",true,"http://www.baidu.com","FACEAUTH_ZMXY","武玉华"]
            - ["微众刷脸","${ENV(appId)}","${ENV(creatorOid)}","TENCENT",true,"http://www.baidu.com","FACEAUTH_TECENT_CLOUD","武玉华"]
            - ["自研刷脸","${ENV(appId)}","${ENV(creatorOid)}","ESIGN",true,"http://www.baidu.com","FACEAUTH_ESIGN","武玉华"]

-
    name: 查询刷脸结果-$CaseName
    testcase: testcases/psn/queryFaceResult.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2-VALIDATE3:
            - ["自研刷脸核身失败","1801404775661847192","FAIL","人脸核身失败",$VALIDATE2]
            - ["腾讯云刷脸核身人脸不完整","1801420424140309182","FAIL","人脸不完整，请保持人脸清晰无遮挡",$VALIDATE2]
            - ["自研刷脸核身成功","1802641379273501969","SUCCESS","认证成功",NULL]

-
    name: 发起个人刷脸核身认证,使用新biztoken接口（1202迭代）-$CaseName
    testcase: testcases/psn/checkBizToken.yml
    parameters:
        - CaseName-appId-name-idNo-faceauthMode-callbackUrl:
            - ["微信刷脸","${ENV(appId)}","武玉华","******************","WE_CHAT_FACE","http://www.baidu.com"]

-
    name: 发起个人刷脸核身认证&走意愿认证-$CaseName
    testcase: testcases/psn/launchFaceWill.yml
    parameters:
        - CaseName-appId-name-idNo-certType-mobileNo-authCode-message1-message2:
            - ["刷脸核身api-有手机号-走意愿认证","${ENV(appId10)}","刘志华","362430199311156921","INDIVIDUAL_CH_IDCARD","18506572526","123456","成功","成功" ]

