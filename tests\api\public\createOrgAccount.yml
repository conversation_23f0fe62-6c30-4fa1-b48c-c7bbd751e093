name: 创建E签宝组织账号api
request:
    url: ${ENV(base_url_user)}/v1/organizations/createByThirdPartyUserId
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        thirdPartyUserId: ${create_randomId()}
        creator: $creatorOid
        name: $name
        idType: $idType
        idNumber: $idNumber
        orgLegalIdNumber: $orgLegalIdNumber
        orgLegalName: $orgLegalName
