config:
    name: 获取个人实名认证地址-页面版接口完成实名认证

testcases:
-
    name: 获取个人实名认证地址-通过运营商三要素完成实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivIdentityUrl_telecom3_V2.yml
    parameters:
        - CaseName-appId-name-idType-idNumber-mobile-email-cardNo-certNo-certType-mobileNo-bankCardNo:
            - ["正常场景","${ENV(appId)}","许华建","CRED_PSN_CH_IDCARD","342622198905262396","***********","<EMAIL>","","342622198905262396","","***********",""]
            - ["mock场景","${ENV(appId)}","测试许华建","","342622198905262396","","","","342622198905262396","","***********",""]

-
    name: 获取个人实名认证地址-通过银行卡四要素完成实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivIdentityUrl_bank4_V2.yml
    parameters:
        - CaseName-appId-name-idType-idNumber-mobile-email-cardNo-certNo-certType-mobileNo-bankCardNo:
            - ["正常场景-身份证号","${ENV(appId)}","测试许华建","CRED_PSN_CH_IDCARD","342622198905262396","***********","<EMAIL>","","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************"]
            - ["正常场景-mock场景","${ENV(appId)}","测试许华建","CRED_PSN_CH_IDCARD","342622198905262396","***********","<EMAIL>","","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************"]
            - ["正常场景mock-港澳通行证","${ENV(appId)}","测试许华建","","","","","","M08596583","INDIVIDUAL_CH_HONGKONG_MACAO","***********","****************"]
            - ["正常场景mock-台胞证","${ENV(appId)}","测试许华建","","","","","","********","INDIVIDUAL_CH_TWCARD","***********","****************"]
            - ["正常场景mock-护照","${ENV(appId)}","测试许华建","","","","","","H10561683","INDIVIDUAL_PASSPORT","***********","****************"]

-
    name: 获取个人实名认证地址-二要素后指定刷脸实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivIdentityUrl_faceauth_identity2_V2.yml
    parameters:
        - CaseName-appId-name-idType-idNumber-mobile-email-cardNo-certNo-certType-mobileNo-bankCardNo-faceAuthMode-subjectType-isPC:
            - ["正常场景-腾讯云刷脸","${ENV(appId)}","许华建","CRED_PSN_CH_IDCARD","342622198905262396","***********","<EMAIL>","","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************",1,1,ture]

-
    name: 获取个人实名认证地址-三要素后指定刷脸实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivIdentityUrl_faceauth_identity3_V2.yml
    parameters:
        - CaseName-appId-name-idType-idNumber-mobile-email-cardNo-certNo-certType-mobileNo-bankCardNo-faceAuthMode-subjectType-isPC:
            - ["正常场景-支付宝刷脸","${ENV(appId2)}","许华建","CRED_PSN_CH_IDCARD","342622198905262396","***********","<EMAIL>","","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************",2,1,ture]

-
    name: 获取个人实名认证地址-三要素+验证码后指定刷脸实名（页面版接口）-$CaseName
    testcase: testcases/psn/indivIdentityUrl_faceauth_identity3SendCode_V2.yml
    parameters:
        - CaseName-appId-name-idType-idNumber-mobile-email-cardNo-certNo-certType-mobileNo-bankCardNo-faceAuthMode-subjectType-isPC:
            - ["正常场景-自研刷脸","${ENV(appId4)}","许华建","CRED_PSN_CH_IDCARD","342622198905262396","***********","<EMAIL>","","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************",10,1,ture]


-
    name: 获取个人实名认证地址-通过人工实名完成-$CaseName
    testcase: testcases/psn/indivIdentityUrl_manual_V2.yml
    parameters:
        - CaseName-appId-name-idType-idNumber-mobile-email-cardNo-certNo-certType-mobileNo-bankCardNo-serviceTypeName-contentMd5-contentType-fileName-fileSize-authCode-certBack-certFront-holdCertFront:
            - ["正常场景-身份证","${ENV(appId)}","许华建","CRED_PSN_CH_IDCARD","342622198905262396","***********","<EMAIL>","","342622198905262396","INDIVIDUAL_CH_IDCARD","***********","****************","PSN_ARTIFICIAL","I466a60evf9T6lUHF/eXyA==","application/octet-stream","ceshi.jpg",37071,123456,"${getfileKey()}","${getfileKey()}","${getfileKey()}"]


