- config:
    name: 查询商品搭售活动信息-异常场景
    base_url: ${ENV(base_url)}
    variables:
        - personal_gid: ${ENV(personal_gid)}
        - tenant_id: ${ENV(tenant_id)}
        - sale_schema_id: ${ENV(sale_schema_id)}

- test:
    name: 异常场景-缺少必填参数saleSchemaId
    variables:
        - gid: ${personal_gid}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: ""
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 异常场景-无效的gid格式
    variables:
        - gid: "invalid_gid_format"
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 200]
        - ne: ["content.code", "0"]

- test:
    name: 异常场景-不存在的saleSchemaId
    variables:
        - gid: ${personal_gid}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=999999999"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 200]
        - ne: ["content.code", "0"]

- test:
    name: 异常场景-无效的客户端ID
    variables:
        - gid: ${personal_gid}
        - tenant_id: ${tenant_id}
        - client_id: "INVALID_CLIENT"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 200]
        - ne: ["content.code", "0"]

- test:
    name: 异常场景-缺少租户ID请求头
    variables:
        - gid: ${personal_gid}
        - tenant_id: ""
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 业务异常-个人账号已挂靠销售且需校验挂靠
    variables:
        - gid: ${ENV(personal_gid_with_sales)}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.data.canOrder", false]
        - contains: ["content.data.limitReason", "已挂靠销售"]

- test:
    name: 业务异常-企业账号已挂靠销售且需校验挂靠
    variables:
        - gid: ${ENV(enterprise_gid_with_sales)}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.data.canOrder", false]
        - contains: ["content.data.limitReason", "已挂靠销售"]

- test:
    name: 业务异常-账号已使用过搭售优惠
    variables:
        - gid: ${ENV(personal_gid_used_tying)}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.data.canOrder", false]
        - contains: ["content.data.limitReason", "已使用过搭售优惠"]
