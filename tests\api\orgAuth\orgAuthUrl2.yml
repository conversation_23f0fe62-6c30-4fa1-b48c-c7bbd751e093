name: 获取组织机构核身认证地址api-20210902迭代之后新增参数，启用新接口
request:
    url: /v2/identity/auth/web/orgAuthUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: $appId
        X-Tsign-Open-Auth-Mode: simple
    json:
      contextInfo:
        contextId: "测试"
        notifyUrl: "http://baidu.com"
        origin: "BROWSER"
        redirectUrl: "http://baidu.com"
        showResultPage: "true"
        showAgreement: $showAgreement
      orgEntity:
        certNo: $certNo
        organizationType: $organizationType
        name: $name
        legalRepCertType: $legalRepCertType
        legalRepCertNo: $legalRepCertNo
        legalRepName: $legalRepName
