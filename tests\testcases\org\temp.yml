config:
    name: 临时用，后期老版本企业实名增加后删掉
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlOid4.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 页面版本信息比对
    api: api/orgAuth/infoVerify.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
