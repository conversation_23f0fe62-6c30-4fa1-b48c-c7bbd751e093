config:
    name: 获取组织机构实名认证地址V3--全半角转换（含有中文的企业名称，半角都转为全角；全英文名称全角转为半角）
    base_url: ${ENV(base_url_rpc)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    variables:
      name: ""
      idType: null
      idNumber: ""
      orgLegalIdNumber: ""
      orgLegalName: ""
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlV3.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.url, "tsignversion=eve"]

-
    name: 页面版信息比对
    api: api/orgAuth/infoVerify.yml
    variables:
      name: $nameInfoVerity
      idNumber: $certNo
      orgLegalName: $legalRepName
      legalRepCertType: ""
      orgLegalIdNumber: ""
      version: "V3"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询企业信息
    api: api/orgAuth/queryOrganizationInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.data.name, $VALIDATE]