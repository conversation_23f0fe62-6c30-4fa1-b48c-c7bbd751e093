config:
    name: 个人运营商三要素比对(实名)
testcases:
-
    name: 个人运营商三要素比对-$CaseName
    testcase: testcases/psn/telecom3_oid.yml
    parameters:
        - CaseName-accountId-mobileNo-authcode-VALIDATE1-VALIDATE2-VALIDATE3:
            - ["正常场景-> 验证码正确","c2c89a67ab134a8996d4e93c90a4dbec","***********","123456",0,"成功","成功"]
            - ["异常场景-> 验证码不正确","c2c89a67ab134a8996d4e93c90a4dbec","***********","123451",0,"成功","验证码校验失败"]
        