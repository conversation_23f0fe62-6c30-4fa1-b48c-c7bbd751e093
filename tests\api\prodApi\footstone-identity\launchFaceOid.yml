name: 发起个人刷脸实名认证api
request:
    url: /v2/identity/auth/api/individual/$accountId/face
    method: POST
#    headers:
#        Content-Type: application/json
#        X-Tsign-Open-App-Id: ${ENV(appId)}
#        X-Tsign-Open-Auth-Mode: simple
    headers: ${gen_headers2()}
    json:
        faceauthMode: $faceauthMode
        repetition: $repetition
        callbackUrl: $callbackUrl
        contextId: "123456"
        notifyUrl: ${ENV(notifyUrl)}