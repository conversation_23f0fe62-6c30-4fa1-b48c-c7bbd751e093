config:
    name: singpass企业实名流程：负面测试用例
    variables:
        accountIdPsn_realname: ${ENV(singpass_psn_oid_realname)}
        accountIdPsn_no_realname: ${ENV(singpass_psn_oid_notrealnamed)}
        accountIdOrg_realname: ${ENV(singpass_org_oid_realname)}
        accountIdOrg_no_realname: ${ENV(singpass_org_oid_notrealnamed)}
        name: "singpass org ${create_randomIdStr(3)}"
        certNo: "220322NEGA"
        legalRepName: "mogui"
        operatorName: "singpass test2"
        operatorCertNo: "S8300002G"
        mobile: "***********"
        mobile2: "***********"

teststeps:
    -   name: 重置个人oid实名
        api: api/public/resetIdentity.yml
        variables:
            accountId: $accountIdPsn_no_realname
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]

    -   name: 重置企业oid实名
        api: api/public/resetIdentity.yml
        variables:
            accountId: $accountIdOrg_no_realname
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]

    -   name: 发起企业实名-用户oid未实名
        api: api/rpc/singpassOrgApply.yml
        variables:
            accountId: $accountIdOrg_no_realname
            agentAccountId: $accountIdPsn_no_realname
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, ********]
            -   contains: [content.message, "经办人账号未实名,请先完成个人实名认证"]


    -   name: 发起企业实名-企业oid已经实名
        # skip: "bug http://jira.timevale.cn:8081/browse/PEAC-115"
        api: api/rpc/singpassOrgApply.yml
        variables:
            accountId: $accountIdOrg_realname
            agentAccountId: $accountIdPsn_realname
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, ********]
            -   contains: [content.message, "当前账户已实名"]


    -   name: 发起企业实名-用个人oid发起
        api: api/rpc/singpassOrgApply.yml
        variables:
            accountId: $accountIdPsn_no_realname
            agentAccountId: $accountIdPsn_realname
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, ********]
            -   eq: [content.message, "${accountId}为个人账号，请传入组织机构账号"]

    -   name: 发起企业实名-oid不存在
        api: api/rpc/singpassOrgApply.yml
        variables:
            accountId: "test1234567890"
            agentAccountId: $accountIdPsn_realname
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, ********]
            -   eq: [content.message, "账号不存在,请检查accountId的正确性"]