- config:
    name: 发起刷脸认证并校验刷脸结果
    base_url: ${ENV(footstone_will_rpc)}

- test:
    name: 发起刷脸认证
    api: api/prodApi/footstone-will/createFaceAuth_rpc.yml
    extract:
        - willAuthId: content.data.willAuthId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.bizId, None]

- test:
    name: 校验刷脸结果
    api: api/prodApi/footstone-will/pollFaceAuth_rpc.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.passed, false]
