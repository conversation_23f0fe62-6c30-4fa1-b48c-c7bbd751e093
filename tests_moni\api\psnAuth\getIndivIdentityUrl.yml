name: 获取个人实名认证地址
request:
    url: /v2/identity/auth/web/$accountId/indivIdentityUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        authType: "PSN_BANK4_AUTHCODE"
        availableAuthTypes:
          - "PSN_TELECOM_AUTHCODE"
          - "PSN_BANK4_AUTHCODE"
          - "PSN_FACEAUTH_BYURL"
        contextInfo:
          contextId: "57cc9602-d1b6-48bd-bdbb-61e9f8443955"
          notifyUrl: "https://www.notifyUrl.cn"
          origin: "BROWSER"
          redirectUrl: "https://www.redirectUrl.cn"
          showResultPage: "true"
indivInfo:
  name: "张三"
  nationality: "MAINLAND"
  certType: "INDIVIDUAL_CH_IDCARD"
  certNo: "362428199001010010"
  mobileNo: "***********"
  bankCardNo: "6227123412341234123"
configParams:
  indivUneditableInfo:
  - "name"
  - "certNo"
  - "mobileNo"
  - "bankCardNo"
repeatIdentity: "true"