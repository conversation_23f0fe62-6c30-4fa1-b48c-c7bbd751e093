config:
    name: 获取组织机构核身认证地址
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取组织机构核身认证地址
    api: api/orgAuth/orgAuthUrl.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 根据flowid获取实名链接
    api: api/public/getFlowUrl.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.shortUrl, null]
      - ne: [content.data.url, null]
