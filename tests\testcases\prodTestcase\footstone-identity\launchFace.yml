config:
    name: 发起个人刷脸核身认证
    base_url: ${ENV(footstone_identity_url)}

teststeps:
-
    name: 发起个人刷脸核身认证
    api: api/prodApi/footstone-identity/launchFace.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, None]
      - ne: [content.data.authUrl, None]

-
    name: 校验刷脸结果-对外接口（废弃，新用户不再对接）
    api: api/prodApi/footstone-identity/queryFaceStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "ING"]
      - eq: [content.data.message, "刷脸认证未完成"]

-
    name: 校验刷脸结果-前端接口
    api: api/prodApi/footstone-identity/queryFaceauthStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, 0]

-
    name: 校验刷脸结果-前端轮询接口
    api: api/prodApi/footstone-identity/queryFaceauthPolling.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, 0]

-
    name: 查询认证信息--代替查询刷脸结果对外接口
    api: api/prodApi/footstone-identity/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.authType, $VALIDATE1]
      - eq: [content.data.indivInfo.name, $name]
