config:
    name: 发起企业核身认证3要素校验-企业支付宝一键实名认证(核身)
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起企业核身认证3要素校验
    api: api/orgAuth/threeFactors.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 申请企业支付宝一键实名
    api: api/orgAuth/alipayOneclickApply.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - ne: [content.data.forwardUrl, null]

-
    name: 查询企业支付宝一键实名结果
    api: api/orgAuth/alipayOneclickResult.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.completed, False]
      - eq: [content.data.success, False]
      - eq: [content.data.message, "认证未完成"]

-
    name: 企业支付宝一键实名
    api: api/orgAuth/alipayOneclickConfirm.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.success, True]
      - eq: [content.data.message, "认证成功"]

-
    name: 查询企业支付宝一键实名结果
    api: api/orgAuth/alipayOneclickResult.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.success, True]
      - eq: [content.data.message, "认证成功"]
-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.subFlows.0.subFlowType, "ENTERPRISE_CERTIFICATION_ONE_KEY_WITH_ALIPAY"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "ENTERPRISE_CERTIFICATION_ONE_KEY_WITH_ALIPAY"]
      - eq: [content.data.organInfo.name, $name]
