config:
    name: 发起法人授权签署&修改手机号
    base_url: ${ENV(base_url_rpc)}

teststeps:
-
    name: 上传营业执照
    api: api/public/fileUploadUrl.yml
    variables:
      - contentMd5: "/VG37e65zxv0Tu+S2kCl+A=="
      - contentType: "application/octet-stream"
      - fileName: "授权书.png"
      - fileSize: 76461
    extract:
      - fileKey1: content.data.fileKey
    validate:
      - eq: [ status_code,200 ]
      - eq: [ content.code, 0 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data.uploadUrl, null ]
-
    name: 上委托授权书
    api: api/public/fileUploadUrl.yml
    variables:
      - contentMd5: "/VG37e65zxv0Tu+S2kCl+A=="
      - contentType: "application/octet-stream"
      - fileName: "授权书.png"
      - fileSize: 76461
    validate:
      - eq: [ status_code,200 ]
      - eq: [ content.code, 0 ]
      - eq: [ content.message, "成功" ]
      - ne: [ content.data.uploadUrl, null ]
-
    name: 提交人工审核
    api: api/orgAuth/manualSubmit.yml
    variables:
      - mobile: "18755957250"
      - authCode: "123456"
      - orgName: "esigntest吴不删的测试企业"
      - orgCertNo: "918888888823456666"
      - email: ""
      - orgType: 1
      - orgCertType: "ORGANIZATION_USC_CODE"
      - authType: "LEGAL_REP_SIGN_AUTHORIZE"
      - photoInfo: ${ENV(proxyphoto_fileKey)}
      - legalRepCertNo: "34100420011123061X"
      - legalRepName: "测试小吴"
      - proxyPhoto: ${ENV(proxyphoto_fileKey)}
      - legalRepCertType: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 获取人工实名审核结果
    api: api/orgAuth/getOrgManualRst.yml
    extract:
      - serviceId: content.data.realnameId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.data.serviceStatus, 1]


-
    name: 发起法人授权签署
    api: api/realname/startOrgManualFrAuth.yml
    extract:
      - signFlowId: content.data.signFlowId
    validate:
      - eq: [status_code,200]
      - eq: [content.data.signFlowStatusText, "签署中"]
      - eq: [content.message, "执行成功"]
      - ne: [content.data.signFlowUrl, null]

-
    name: 修改手机号
    api: api/realname/modifyMobileOfOrgManualFrAuth.yml
    validate:
      - eq: [ status_code,200 ]
      - eq: [ content.data.signFlowStatusText, "签署中" ]
      - eq: [ content.message, "执行成功" ]
      - ne: [ content.data.signFlowUrl, null ]

-
    name: 查询法人授权签署记录
    api: api/realname/queryOrgManualFrAuth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.message, "执行成功"]
      - eq: [content.success, true]

#    name: 运营支撑平台拒绝审批
#    api: api/realname/audting.yml
#    variables:
#      - auditorResult: "拒绝"
#      - auditingRemark: "1、填写的认证组织信息与证件照不一致"
#      - transactionId: $flowId
#      - auditor: "测试南鸿"
#      - serviceId: $serviceId
#    validate:
#      - eq: [status_code,200]




