config:
    name: 添加修改删除城市
    base_url: ${ENV(base_url_realname)}

teststeps:
-
    name: 添加城市
    api: api/realname/addBankCityCode.yml
    validate:
      - eq: [status_code,200]

-
    name: 查询地区
    api: api/realname/queryBankCityCode.yml
    variables:
      cityCode_query: $cityCode
    extract:
      - id: content.0.id
    validate:
      - eq: [status_code,200]
      - eq: [content.0.district, $district]
      - eq: [content.0.districtCode, $districtCode]
      - str_eq: [content.0.cityCode, $cityCode]

-
    name: 修改城市
    api: api/realname/updateBankCityCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content, 1]

-
    name: 查询城市
    api: api/realname/queryBankCityCode.yml
    variables:
      cityCode_query: $cityCode_update
    validate:
      - eq: [status_code,200]
      - eq: [content.0.district, $district]
      - eq: [content.0.districtCode, $districtCode]
      - str_eq: [content.0.cityCode, $cityCode_update]

-
    name: merge城市
    api: api/realname/mergeBankCityCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content, 1]

-
    name: 查询城市
    api: api/realname/queryBankCityCode.yml
    variables:
      cityCode_query: $cityCode_update
    validate:
      - eq: [status_code,200]
      - eq: [content.0.district, $district]
      - eq: [content.0.districtCode, $districtCode]
      - str_eq: [content.0.cityCode, $cityCode_update]

-
    name: 删除城市
    api: api/realname/deleteBankCityCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content, true]
