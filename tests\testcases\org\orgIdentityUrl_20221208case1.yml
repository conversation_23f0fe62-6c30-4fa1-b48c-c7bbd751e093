config:
    name: 企业实名支持新增传入
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建账号
    api: api/public/createByThirdPartyUserId_psnV1.yml
    extract:
      - agentAccountId: content.data.accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.accountId, null]
-
    name: 获取企业实名认证地址
    api: api/orgAuth/orgIdentityUrlOid2.yml
    variables:
      - agentAccountId: $agentAccountId
      - certNo: ""
      - organizationType: ""
      - name: ""
      - legalRepCertType: ""
      - legalRepCertNo: ""
      - legalRepName: ""
      - repeatIdentity: true
      - showAgreement: true
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询经办人信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.mobileNo, $agentMobile]
      - eq: [content.data.bankCardNo, $agentBankCardNo]
