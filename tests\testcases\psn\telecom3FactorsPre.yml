config:
    name: 发起运营商3要素核身认证，校验短信验证码
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 首次核身-发起运营商3要素核身认证
    api: api/psnAuth/telecom3FactorsPre.yml
    variables:
      name: '刘志华'
      idNo: '362430199311156921'
      mobileNo: '18506572526'
      source: 0
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验短信验证码-正确验证码
    api: api/psnAuth/telecom3VerifyCode.yml
    variables:
      flowId: $flowId
      authcode: 123456
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 再次核身-发起运营商3要素核身认证
    api: api/psnAuth/telecom3FactorsPre.yml
    variables:
      name: '刘志华'
      idNo: '362430199311156921'
      mobileNo: '18506572526'
      source: 1
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.behavior.source, 1]
      - ne: [content.data.behavior.refFlowId, null]
      - ne: [content.data.behavior.refFlowTime, null]
      - ne: [content.data.behavior.refFlowExpired, null]

-
    name: 校验短信验证码-正确验证码
    api: api/psnAuth/telecom3VerifyCode.yml
    variables:
      flowId: $flowId
      authcode: 123456
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.subFlows.0.subFlowType, "INDIVIDUAL_VERIFY_WILL_TELECOM_3"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.authType, "INDIVIDUAL_VERIFY_WILL_TELECOM_3"]