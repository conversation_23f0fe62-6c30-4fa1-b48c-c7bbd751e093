config:
     name: IDVERIFY-4701需要新增个人/企业信息校验rpc接口
testcases:
-
     name: 个人二要素比对rpc接口
     testcase: testcases/rpc/psn2.yml
     parameters:
         - CaseName-idNo-name-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","******************","武玉华",0,"成功"]
             - ["正常场景-> 身份证尾数带X","14273019911003072X","高美君",0,"成功"]
             - ["异常场景-> idNo不正确","362302198609175011","武玉华",********,"不通过"]
             - ["异常场景-> name不正确","******************","王思聪",********,"不通过"]
             - ["异常场景-> idNo为空","","武玉华",********,"不能为空"]
             - ["异常场景-> name为空","******************","",********,"不能为空"]
             - ["异常场景-> 身份证号码小于18位","*****************","武玉华",********,"参数错误"]
             - ["异常场景-> 身份证号码大于18位","******************2","武玉华",********,"参数错误"]
#             - ["异常场景-> 姓名小于2位","******************","武",********,"参数错误"]
#0527版本支持姓名大于20位              - ["异常场景-> 姓名大于20位","******************","武去微软推哦怕时代法国红酒看来自行车不能你们",********,"参数错误"]
             - ["异常场景-> 姓名大于20位","******************","武去微软推哦怕时代法国红酒看来自行车不能你们",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 姓名中含有空格","******************","武 玉尘",********,"格式校验不通过"]
-
     name: 个人运营商三要素比对rpc接口
     testcase: testcases/rpc/telecom3.yml
     parameters:
         - CaseName-name-idNo-mobileNo-VALIDATE1-VALIDATE2:
            - ["正常场景-> name正确","武玉华","******************","***********",0,"成功"]
            - ["异常场景-> idNo不正确","武玉华","14273019911003072X","***********",********,"不通过"]
            - ["异常场景-> mobileNo不正确","武玉华","******************","13699846000",********,"不通过"]
            - ["异常场景-> name不正确","王思聪","******************","***********",********,"不通过"]
            - ["异常场景-> 必填项idNo为空","武玉华","","***********",********,"不能为空"]
            - ["异常场景-> 必填项mobileNo为空","武玉华","******************","",********,"不能为空"]
            - ["异常场景-> 必填项name为空","","******************","***********",********,"不能为空"]
            - ["异常场景-> 身份证号码小于18位","武玉华","*****************","***********",30500118,"请输入正确的身份证号码"]
            - ["异常场景-> 身份证号码大于18位","武玉华","******************2","***********",30500118,"请输入正确的身份证号码"]
#            - ["异常场景-> 姓名小于2位","武","******************","***********",********,"参数错误：请输入正确长度的姓名（2-100位）"]
#0527版本支持姓名大于20位             - ["异常场景-> 姓名大于20位","武去微软推哦怕时代法国红酒看来自行车不能你们","******************","***********",********,"参数错误"]
            - ["异常场景-> 姓名大于20位","武去微软推哦怕时代法国红酒看来自行车不能你们","341224197611256850","***********",********,"信息比对不通过,请检查信息的正确性"]
#0923迭代优化报错信息遗留问题待修复            - ["异常场景-> 姓名中含有空格","武 玉华","******************","***********",********,"格式校验不通过"]
            - ["异常场景-> 手机号小于11位","武玉华","******************","**********",********,"请输入正确的手机号码"]
            - ["异常场景-> 手机号大于11位","武玉华","******************","***********1",********,"请输入正确的手机号码"]
-
     name: 个人银行卡三要素比对rpc接口
     testcase: testcases/rpc/bank3.yml
     parameters:
         - CaseName-idNo-name-cardNo-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","342622198905262396","许华建","****************",0,"成功"]
             - ["正常场景-> 身份证尾数带X","13240119700530542X","刘焕娣","6212260200211055682",0,"成功"]
             - ["异常场景-> name不正确","******************","王思聪","****************",********,"不通过"]
             - ["异常场景-> cardNo不正确","******************","武玉华","****************",********,"不通过"]
             - ["异常场景-> idNo不正确","******************","武玉华","****************",********,"不通过"]
             - ["异常场景-> 必填项name为空","14273019911003072X","","****************",********,"不能为空"]
             - ["异常场景-> 必填项idNo为空","","武玉华","****************",********,"不能为空"]
             - ["异常场景-> 必填项cardNo为空","******************","武玉华","",********,"不能为空"]
             - ["异常场景-> 必填项cardNo为空","******************","武玉华","",********,"不能为空"]
             - ["异常场景-> 身份证号码小于18位","*****************","武玉华","****************",********,"参数错误"]
             - ["异常场景-> 身份证号码大于18位","******************2","武玉华","****************",********,"参数错误"]
#             - ["异常场景-> 姓名小于2位","******************","武","****************",********,"参数错误"]
#0527版本支持姓名大于20位             - ["异常场景-> 姓名大于20位","******************","武去微软推哦怕时代法国红酒看来自行车不能你们","****************",********,"参数错误"]
             - ["异常场景-> 姓名大于20位","******************","武去微软推哦怕时代法国红酒看来自行车不能你们","****************",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 姓名中间含有空格","******************","武 玉华","****************",********,"格式校验不通过"]
             - ["异常场景-> 手机号小于11位","武","******************","**********",********,"参数错误"]
-
     name: 个人银行卡四要素比对rpc接口
     testcase: testcases/rpc/bank4.yml
     parameters:
         - CaseName-idNo-name-cardNo-mobileNo-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","342622198905262396","测试许华建","****************","***********",0,"成功"]
             - ["异常场景-> idNo不正确","14273019911003072X","林书芳","6222081203009064490","***********",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name不正确","******************","王思聪","****************","***********",********,"不通过"]
             - ["异常场景-> cardNo不正确","******************","武玉华","****************","***********",********,"不通过"]
             - ["异常场景-> mobileNo不正确","******************","武玉华","****************","***********",********,"不通过"]
             - ["异常场景-> 必填项name为空","******************","","****************","***********",********,"不能为空"]
             - ["异常场景-> 必填项idNo为空","","武玉华","****************","***********",********,"不能为空"]
             - ["异常场景-> 必填项cardNo为空","******************","武玉华","","***********",********,"不能为空"]
             - ["异常场景-> 必填项mobileNo为空","******************","武玉华","****************","",********,"不能为空"]
             - ["异常场景-> 身份证号码小于18位","*****************","武玉华","****************","***********",********,"参数错误"]
             - ["异常场景-> 身份证号码大于18位","******************2","武玉华","****************","***********",********,"参数错误"]
#             - ["异常场景-> 姓名小于2位","******************","武","****************","***********",********,"参数错误"]
#0527版本支持姓名大于20位              - ["异常场景-> 姓名大于20位","******************","武去微软推哦怕时代法国红酒看来自行车不能你们","****************","***********",********,"参数错误"]
             - ["异常场景-> 姓名大于20位","******************","武去微软推哦怕时代法国红酒看来自行车不能你们","****************","***********",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 姓名中间含有空格","******************","武 玉华","****************","***********",********,"格式校验不通过"]
             - ["异常场景-> 手机号小于11位","武","******************","**********","***********",********,"参数错误"]

-
     name: 企业二要素比对rpc接口
     testcase: testcases/rpc/enterprise2.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州天谷信息科技有限公司","913301087458306077",0,"成功"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","330108000003512",0,"成功"]
             - ["异常场景-> name不正确","上海寻梦信息技术有限公司","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode-填18位codeUSC不正确","杭州天谷信息科技有限公司","91310115MA1K3J0A7C",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode-填15位codeREG不正确","杭州天谷信息科技有限公司","440301103097413",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","913301087458306077",********,"不能为空"]
             - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","",********,"不能为空"]
             - ["异常场景-> name长度小于2","杭","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间含有空格","杭 州天谷信息科技有限公司","913301087458306077",********,"空格"]
             - ["异常场景-> 英文name中间含有空格（支持），信息比对不通过","abc d","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","44030110309741",********,"参数错误"]
             - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","91330108745830607",********,"参数错误"]
             - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","9133010874583060778",********,"参数错误"]
-
     name: 企业三要素比对rpc接口
     testcase: testcases/rpc/enterprise3.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确，填18位codeUSC","杭州天谷信息科技有限公司","金宏洲","913301087458306077",0,"成功"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","金宏洲","330108000003512",0,"成功"]
             - ["异常场景-> name不正确","上海寻梦信息技术有限公司","何一兵","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode-填18位codeUSC不正确","杭州天谷信息科技有限公司","金宏洲","91310115MA1K3J0A7C",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode-填15位codeREG不正确","杭州天谷信息科技有限公司","金宏洲","440301103097413",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","何一兵","913301087458306077",********,"不能为空"]
             - ["异常场景-> legalRepName为空","杭州天谷信息科技有限公司","","913301087458306077",********,"不能为空"]
             - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","金宏洲","",********,"不能为空"]
             - ["异常场景-> name长度小于2","杭","何一兵","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间含有空格","杭 州天谷信息科技有限公司","金宏洲","913301087458306077",********,"空格"]
             - ["异常场景-> 英文name中间含有空格","abc d","金宏洲","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","何一兵","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","金宏洲","44030110309741",********,"参数错误"]
             - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","金宏洲","91330108745830607",********,"参数错误"]
             - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","金宏洲","9133010874583060778",********,"参数错误"]
             - ["异常场景-> legalRepName长度小于2","杭州天谷信息科技有限公司","何","913301087458306077",********,"参数错误"]
-
     name: 企业四要素比对rpc接口
     testcase: testcases/rpc/enterprise4.yml
     parameters:
         - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州天谷信息科技有限公司","金宏洲","330722197904110013","913301087458306077",0,"成功"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","金宏洲","330722197904110013","330108000003512",0,"成功"]
             - ["异常场景-> name不正确","上海寻梦信息技术有限","何一兵","******************","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName不正确","杭州天谷信息科技有限公司","哈哈哈哈","330722197904110013","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 企业社会统一信用代码非91/92/93开头","杭州天谷信息科技有限公司","金宏","940722197904110013","332427188403021107",30502071,"暂不支持该类型企业"]
             - ["异常场景-> 填18位codeUSC不正确","杭州天谷信息科技有限公司","金宏洲","330722197904110013","91310115MA1K3J0A7C",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 填15位codeREG不正确","杭州天谷信息科技有限公司","金宏洲","330722197904110013","440301103097413",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","金宏洲","110108196710262291","913301087458306077",********,"不能为空"]
             - ["异常场景-> legalRepName为空","杭州天谷信息科技有限公司","","330722197904110013","913301087458306077",********,"不能为空"]
             - ["异常场景-> legalRepCertNo为空","杭州天谷信息科技有限公司","金宏洲","","913301087458306077",********,"不能为空"]
             - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","金宏洲","330722197904110013","",********,"不能为空"]
             - ["异常场景-> 中文name中间含有空格","杭 州天谷信息科技有限公司","金宏洲","330722197904110013","913301087458306077",********,"空格"]
             - ["异常场景-> 英文name中间含有空格","abc d E","金宏洲","330722197904110013","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天信-+~。息科（技有)  限公司 e ee 11 112","何一兵","330722197904110013","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","金宏洲","330722197904110013","44030110309741",********,"参数错误"]
             - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","金宏洲","330722197904110013","91330108745830607",********,"参数错误"]
             - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","金宏洲","330722197904110013","9133010874583060778",********,"参数错误"]
             - ["异常场景-> orgCode中间有空格","杭州天谷信息科技有限公司","金宏洲","330722197904110013","913301087 4583060778",********,"格式校验不通过"]
             - ["异常场景-> legalRepName长度小于2位","杭州天谷信息科技有限公司","何","110108196710262291","913301087458306077",********,"参数错误"]
             - ["异常场景-> legalRepCertNo中间有空格","杭州天谷信息科技有限公司","金宏洲","330722197904110013","91330108745830 6077",********,"格式校验不通过"]
-
     name: 律所三要素比对rpc接口
     testcase: testcases/rpc/lawFirm3.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","山东明朗律师事务所","王文博","313700000973293896",0,"成功"]
             - ["异常场景-> name不正确","杭州天谷信息科技有限公司","王文博","313700000973293896",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> codeUSC不正确","山东明朗律师事务所","王文博","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","王文博","313700000973293896",********,"不能为空"]
             - ["异常场景-> legalRepName为空","山东明朗律师事务所","","313700000973293896",********,"不能为空"]
             - ["异常场景-> codeUSC为空","山东明朗律师事务所","王文博","",********,"不能为空"]
             - ["异常场景-> name长度小于2","北","王文博","313700000973293896",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间含有空格","山东 明朗律师事务所","王文博","313700000973293896",********,"空格"]
             - ["异常场景-> 英文name中间含有空格,校验不通过","abc d","王文博","313700000973293896",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","王文博","313700000973293896",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName长度小于2","山东明朗律师事务所","王","313700000973293896",********,"参数错误：请输入正确长度的法定代表人姓名（2-100位）"]
             - ["异常场景-> legalRepName中间含有空格","山东明朗律师事务所","王 文博","313700000973293896",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> codeUSC位数不正确","山东明朗律师事务所","王文博","31370000097329389",********,"参数错误"]
             - ["异常场景-> codeUSC中间有空格","山东明朗律师事务所","王文博","31370000097329389 6",********,"格式校验不通过"]
-
     name: 组织机构三要素比对rpc接口
     testcase: testcases/rpc/organization3.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","杭州天谷信息科技有限公司","金宏洲","913301087458306077",0,"成功"]
             - ["异常场景-> name不正确","北京理工大学教育基金会","何一兵","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName不正确","杭州天谷信息科技有限公司","杨宾","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode不正确","杭州天谷信息科技有限公司","金宏洲","53100000500021676K",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","杨宾","53100000500021676K",********,"不能为空"]
             - ["异常场景-> legalRepName为空","北京理工大学教育基金会","","53100000500021676K",********,"不能为空"]
             - ["异常场景-> orgCode为空","北京理工大学教育基金会","杨宾","",********,"不能为空"]
             - ["异常场景-> name长度小于2","北","杨宾","53100000500021676K",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间含有空格","北 京理工大学教育基金会","杨宾","53100000500021676K",********,"空格"]
             - ["异常场景-> 英文name中间含有空格，信息比对不通过","abc d","杨宾","53100000500021676K",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","杨宾","53100000500021676K",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName长度小于2","北京理工大学教育基金会","郭","53100000500021676K",********,"参数错误"]
             - ["异常场景-> legalRepName中间含有空格","杭州天谷信息科技有限公司","何一 兵","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode位数不正确","北京理工大学教育基金会","杨宾","53100000500021676",********,"参数错误"]
             - ["异常场景-> codeUSC中间有空格","北京理工大学教育基金会","杨宾","5310000050 0021676K",********,"格式校验不通过"]
-
     name: 非工商组织三要素比对rpc接口
     testcase: testcases/rpc/orgSocial3.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2:
             - ["正常场景-> 信息都正确","天津大学","柴立元","12100000401359321Q",0,"成功"]
             - ["异常场景-> name不正确","北京理工大学教育基金会","柴立元","12100000401359321Q",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName不正确","天津大学","王思聪","12100000401359321Q",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> orgCode不正确","天津大学","柴立元","913301087458306077",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> name为空","","柴立元","12100000401359321Q",********,"不能为空"]
             - ["异常场景-> legalRepName为空","天津大学","","12100000401359321Q",********,"不能为空"]
             - ["异常场景-> orgCode为空","天津大学","柴立元","",********,"不能为空"]
             - ["异常场景-> name长度小于2","天","柴立元","12100000401359321Q",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 中文name中间有空格","天 津大学","柴立元","12100000401359321Q",********,"参数错误"]
             - ["异常场景-> 英文name中间有空格，信息比对不通过","abc d","柴立元","12100000401359321Q",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","金东寒","12100000401359321Q",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> legalRepName长度小于2","天津大学","金","12100000401359321Q",********,"参数错误"]
             - ["异常场景-> legalRepName中间有空格","天津大学","金 东寒","12100000401359321Q",********,"信息比对不通过,请检查信息的正确性"]
             - ["异常场景-> codeUSC长度位数不正确","天津大学","柴立元","1210000040135931Q",********,"参数错误"]
#浅忆那边暂时用不到带appid的接口，因为部分逻辑有问题这部分先注释
#-
#     name: 企业四要素比对_有付费appid
#     testcase: testcases/rpc/enterprise4withAppId.yml
#     parameters:
#         - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2:
#             - ["正常场景-> 信息都正确,填18位codeUSC","杭州天谷信息科技有限公司","何一兵","110108196710262291","913301087458306077",0,"成功"]
#             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","何一兵","110108196710262291","330108000003512",0,"成功"]
#             - ["异常场景-> name不正确","上海寻梦信息技术有限公司","何一兵","******************","913301087458306077",********,"不通过"]
#             - ["异常场景-> legalRepName不正确","杭州天谷信息科技有限公司","王思聪","******************","913301087458306077",********,"不通过"]
#             - ["异常场景-> legalRepCertNo不正确","杭州天谷信息科技有限公司","何一兵","******************","913301087458306077",********,"不通过"]
#             - ["异常场景-> 填18位codeUSC不正确","杭州天谷信息科技有限公司","何一兵","******************","91310115MA1K3J0A7C",********,"不通过"]
#             - ["异常场景-> 填15位codeREG不正确","杭州天谷信息科技有限公司","何一兵","******************","440301103097413",********,"不通过"]
#             - ["异常场景-> name为空","","何一兵","110108196710262291","913301087458306077",998,"缺少参数"]
#             - ["异常场景-> legalRepName为空","杭州天谷信息科技有限公司","","110108196710262291","913301087458306077",********,"参数错误"]
#             - ["异常场景-> legalRepCertNo为空","杭州天谷信息科技有限公司","何一兵","","913301087458306077",********,"不能为空"]
#             - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","何一兵","110108196710262291","",********,"不能为空"]
#             - ["异常场景-> name长度小于2","杭","何一兵","110108196710262291","913301087458306077",********,"不通过"]
#             - ["异常场景-> 中文name中间含有空格","杭 州天谷信息科技有限公司","何一兵","110108196710262291","913301087458306077",********,"空格"]
#             - ["异常场景-> 英文name中间含有空格","abc d","何一兵","110108196710262291","913301087458306077",********,"不通过"]
#             - ["异常场景-> 空格特殊字符等校验（全部支持），信息比对不通过","杭州cj天谷信-+~。息科（技有)  限公司 e ee 11 11","何一兵","110108196710262291","913301087458306077",********,"不通过"]
#             - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","何一兵","110108196710262291","44030110309741",********,"参数错误"]
#             - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","何一兵","110108196710262291","91330108745830607",********,"参数错误"]
#             - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","何一兵","110108196710262291","9133010874583060778",********,"参数错误"]
#             - ["异常场景-> orgCode中间有空格","杭州天谷信息科技有限公司","何一兵","110108196710262291","913301087 4583060778",********,"格式校验不通过"]
#             - ["异常场景-> legalRepName长度小于2位","杭州天谷信息科技有限公司","何","110108196710262291","913301087458306077",********,"参数错误"]
#             - ["异常场景-> legalRepCertNo长度小于18位","杭州天谷信息科技有限公司","何一兵","11010819671026229","913301087458306077",********,"不通过"]
#             - ["异常场景-> legalRepCertNo长度大于18位","杭州天谷信息科技有限公司","何一兵","1101081967102622912","913301087458306077",********,"不通过"]
#             - ["异常场景-> legalRepCertNo中间有空格","杭州天谷信息科技有限公司","何一兵","110108196710262291","91330108745830 6077",********,"格式校验不通过"]