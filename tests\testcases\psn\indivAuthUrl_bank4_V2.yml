config:
    name: 获取个人核身认证地址+页面版通过银行卡四要素完成核身
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取个人核身认证地址
    api: api/psnAuth/indivAuthUrl3.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版查询个人认证信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.bankCardNo, $bankCardNo]
      - eq: [content.data.name, $name]
      - eq: [content.data.certNo, $certNo]

-
    name: 查询个人认证流程状态1
    api: api/psnAuth/queryIndivFlowidStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "INIT"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版发起银行卡四要素
    api: api/psnAuth/bankCard4FactorInfoAuth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版发起银行卡四要素验证码
    api: api/psnAuth/bankCard4FactorAuthCode_web.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版银行卡四要素验证码校验
    api: api/psnAuth/bankCard4Factor.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]