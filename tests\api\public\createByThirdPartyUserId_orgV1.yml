name: 创建机构账号-使用第三方userId
request:
    url: ${ENV(base_url_user)}/v1/organizations/createByThirdPartyUserId
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        creator: $creator
        thirdPartyUserId: ${create_randomId()}
        name: $name
        idType: $idType
        idNumber: $idNumber
        orgLegalIdNumber: $orgLegalIdNumber
        orgLegalName: $orgLegalName
