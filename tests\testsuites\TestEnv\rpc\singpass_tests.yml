config:
    name: singpass实名认证测试用例

testcases:
#    -   name: singpass个人实名流程：否认信息
#        testcase: testcases/rpc/singpass_psn_realname_flow_confirm_no.yml
#
#    -   name: singpass个人实名流程：确认信息正确
#        testcase: testcases/rpc/singpass_psn_realname_flow_confirm_yes.yml

    -   name: singpass个人实名流程：负面异常测试用例
        testcase: testcases/rpc/singpass_psn_realname_flow_negative_cases.yml

#    -   name: singpass企业实名流程：否认信息
#        testcase: testcases/rpc/singpass_org_realname_flow_confirm_no.yml
#
#    -   name: singpass企业实名流程：确认信息正确
#        testcase: testcases/rpc/singpass_org_realname_flow_confirm_yes.yml

    -   name: singpass企业实名流程：负面异常测试用例
        testcase: testcases/rpc/singpass_org_realname_flow_negative_cases.yml

