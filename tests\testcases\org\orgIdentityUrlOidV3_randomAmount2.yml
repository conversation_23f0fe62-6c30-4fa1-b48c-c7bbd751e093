config:
    name: 获取组织机构实名认证地址V3-随机打款认证,走网商银行银企直连
    base_url: ${ENV(base_url)}

teststeps:
#创建企业oid，发起实名，随机打款认证，提交后重新发起，再随机打款认证，提交后授权书审批拒绝，再次重新发起，随机打款认证，授权书审批通过
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlV3.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.url, "tsignversion=eve"]

-
    name: 页面版企业经办人认证状态查询
    api: api/orgAuth/agentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.authorizedInV21, true]
      - eq: [content.data.authorisedInUserCenter, true]

-
    name: 页面版信息比对
    api: api/orgAuth/infoVerify.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]
      - eq: [content.data.operatorType, $operatorType]

-
    name: 页面版对公打款发送授权书
    api: api/orgAuth/sendAuthorizationEmail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

-
    name: 页面版随机金额对公打款
    api: api/orgAuth/transferRandomAmountWeb.yml
    variables:
      cardNo: "11003491675701"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版随机金额校验
    api: api/orgAuth/randomAmountWeb.yml
    variables:
      cash: 0.01
      finishCash: false
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]
