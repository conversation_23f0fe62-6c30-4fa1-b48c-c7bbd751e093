config:
    name: 查询个人刷脸完成后的存证数据
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 查询个人刷脸完成后的存证数据
    api: api/public/getEvidenceData.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.0.extDataMap.idCardNo, $VALIDATE1]
      - eq: [content.data.0.extDataMap.name, $VALIDATE2]
      - eq: [content.data.0.extDataMap.phone, $VALIDATE5]
      - eq: [content.data.0.realNameStep, $VALIDATE3]
      - eq: [content.data.0.realNameWay, $VALIDATE4]
      - eq: [content.data.1.extDataMap.phoneNo, $VALIDATE5]
      - ne: [content.data.1.extDataMap.smsSend, null]
