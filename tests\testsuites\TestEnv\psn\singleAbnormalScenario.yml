config:
    name: 单接口的异常场景
#主通道
testcases:
-
    name: 发起个人刷脸实名认证异常场景-$CaseName
    testcase: testcases/psn/launchFaceOid_ul.yml
    parameters:
        - CaseName-appId-accountId-faceauthMode-repetition-callbackUrl-VALIDATE1-VALIDATE2:
            - ["异常场景-> accountId不正确","${ENV(appId)}","f3ef1ebc0dd34348b1fafce0b30f0b0","TENCENT",true,"http://www.baidu.com",********,"账号不存在,请检查accountId的正确性"]
            - ["异常场景-> accountId存在空格","${ENV(appId)}","f3ef1ebc0dd34348b1fafce0b30f0b04 ","ZHIMACREDIT",true,"http://www.baidu.com",********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["异常场景-> accountId传入了组织账号","${ENV(appId)}","${ENV(accountId)}","ESIGN",true,"http://www.baidu.com",********,"为组织机构账号，请传入个人账号"]
            - ["异常场景-> faceauthMode为空","${ENV(appId)}","${ENV(creatorOid)}","",true,"http://www.baidu.com",********,"缺少参数：faceauthMode不能为空"]
            - ["异常场景-> faceauthMode不存在","${ENV(appId)}","${ENV(creatorOid)}","123",true,"http://www.baidu.com",********,"参数错误：人脸认证方式传参错误"]
            - ["异常场景-> 账号已实名，不允许重复实名","${ENV(appId)}","${ENV(creatorOid)}","TENCENT",false,"http://www.baidu.com",********,"当前账户已实名"]
#********迭代支持callback为空            - ["异常场景-> callbackUrl为空","${ENV(appId)}","${ENV(creatorOid)}","TENCENT",true,"",********,"缺少参数：callbackUrl字段不能为空"]
            - ["异常场景-> appid余额不足","${ENV(appId6)}","${ENV(creatorOid)}","TENCENT",true,"http://www.baidu.com",********,"微众人脸实名认证服务已欠费，请您选择其他认证方式，或联系发起方进行充值。"]
            - ["异常场景-> 未满18岁","${ENV(appId)}","4b28b8a588b64753bc5ebce32c9db0d1","TENCENT",true,"http://www.baidu.com",********,"未成年用户不允许使用当前产品"]
            - ["异常场景-> 未满14岁","${ENV(appId3)}","0d412927f5044c61849e29690fec25ae","TENCENT",true,"http://www.baidu.com",********,"未成年用户不允许使用当前产品"]

-
    name: 发起个人刷脸核身认证异常场景-$CaseName
    testcase: testcases/psn/launchFace_ul.yml
    parameters:
        - CaseName-appId-name-idNo-faceauthMode-callbackUrl-VALIDATE1-VALIDATE2:
            - ["异常场景-> name为空","${ENV(appId)}","","******************","TENCENT","http://www.baidu.com",********,"缺少参数：name字段不能为空"]
            - ["异常场景-> name中间有空格","${ENV(appId)}","武 玉华","******************","ZHIMACREDIT","http://www.baidu.com",30503058,"姓名包含不可识别字符，请核对"]
            - ["异常场景-> name是非中文字符","${ENV(appId)}","abc123","******************","ZHIMACREDIT","http://www.baidu.com",********,"参数错误：姓名格式不正确"]
            - ["异常场景-> name是非中文字符，自研刷脸也增加了判断-20210624迭代","${ENV(appId)}","abc123","******************","ESIGN","http://www.baidu.com",********,"参数错误：姓名格式不正确"]
            - ["异常场景-> idNo为空","${ENV(appId)}","武玉华","","TENCENT","http://www.baidu.com",********,"缺少参数：idNo字段不能为空"]
            - ["异常场景-> idNo位数不正确","${ENV(appId)}","武玉华","******************1","ESIGN","http://www.baidu.com",********,"参数错误：请输入正确的身份证号码"]
            - ["异常场景-> faceauthMode为空","${ENV(appId)}","武玉华","******************","","http://www.baidu.com",********,"缺少参数：faceauthMode不能为空"]
            - ["异常场景-> faceauthMode不存在","${ENV(appId)}","武玉华","******************","123","http://www.baidu.com",********,"参数错误：人脸认证方式传参错误"]
#********迭代支持callback为空             - ["异常场景-> callbackUrl为空","${ENV(appId)}","武玉华","******************","ESIGN","",********,"缺少参数：callbackUrl字段不能为空"]
            - ["异常场景-> appid余额不足","${ENV(appId6)}","武玉华","******************","ESIGN","http://www.baidu.com",********,"e签宝人脸实名认证服务已欠费，请您选择其他认证方式，或联系发起方进行充值。"]
            - ["异常场景-> 未满18岁","${ENV(appId)}","测试十八","110101202003073778","TENCENT","http://www.baidu.com",********,"未成年用户不允许使用当前产品"]
            - ["异常场景-> 未满14岁","${ENV(appId3)}","测试十四","11010120180507329X","ESIGN","http://www.baidu.com",********,"未成年用户不允许使用当前产品"]

-
    name: 查询刷脸结果异常场景-$CaseName
    testcase: testcases/psn/queryFaceStatus_ul.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
            - ["异常场景-> flowId不存在","180264137927350196",30503055,"未查询到刷脸认证记录"]
            - ["异常场景-> flowId不是通过刷脸接口发起","1802694082766859549",30500000,"操作出错,请联系服务人员处理"]

-
    name: 发起运营商3要素核身认证异常场景-$CaseName
    testcase: testcases/psn/telecom3Factors_ul.yml
    parameters:
        - CaseName-name-idNo-mobileNo-grade-VALIDATE1-VALIDATE2:
            - ["异常场景-> 信息核验失败","武玉","******************","***********","",********,"运营商三要素信息核验失败，请检查信息正确性"]
            - ["异常场景-> 参数中含有空格","武玉 华","******************","***********","",********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["异常场景-> 身份证号格式不正确","武玉华","*************7407","***********","",********,"参数错误：请输入正确的身份证号码"]
            - ["异常场景-> 手机号格式不正确","武玉华","******************","**********","",********,"参数错误：请输入正确的手机号码"]
#20240401已支持1位            - ["异常场景-> 姓名长度不正确","武","******************","***********","",********,"参数错误：请输入正确长度的姓名（2-100位）"]
            - ["异常场景-> 姓名为空","","******************","***********","",********,"缺少参数：name不能为空"]
            - ["异常场景-> 身份证为空","武玉华","","***********","",********,"缺少参数：idNo不能为空"]
            - ["异常场景-> 手机号为空","武玉华","******************","","",********,"缺少参数：mobileNo不能为空"]
            - ["异常场景-> 未满18岁","测试十八","110101202003073778","***********","",********,"未成年用户不允许使用当前产品"]

-
    name: 发起运营商3要素实名认证异常场景-$CaseName
    testcase: testcases/psn/telecom3FactorsOid_ul.yml
    parameters:
        - CaseName-accountId-mobileNo-repetition-grade-VALIDATE1-VALIDATE2:
            - ["异常场景-> 信息核验失败","${ENV(creatorOid)}","***********",true,"",********,"运营商三要素信息核验失败"]
            - ["异常场景-> 手机号为空","${ENV(creatorOid)}","",true,"",********,"缺少参数：手机号不能为空"]
            - ["异常场景-> 不能重复实名","${ENV(creatorOid)}","***********",false,"",********,"当前账户已实名"]
            - ["异常场景-> 手机号格式不正确","${ENV(creatorOid)}","***********1",true,"",********,"参数错误：请输入正确的手机号码"]
            - ["异常场景-> 参数中含有空格","${ENV(creatorOid)}","********** 4",true,"",********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["异常场景-> 个人oid传了企业oid","3c8b9f57bdc1409d9e71a73ce876afef","***********",true,"",********,"为组织机构账号，请传入个人账号"]
            - ["异常场景-> 个人oid中缺少姓名","c3f409c39b104f189efce8a6a58587ce","***********",true,"",********,"个人用户基本信息缺少必要属性：姓名"]
            - ["异常场景-> 个人oid中缺少身份证号","806987531bd24aad9d6c7b198a1d4943","***********",true,"",********,"个人用户基本信息缺少必要属性：身份证号"]
            - ["异常场景-> 个人oid不存在","806987531bd24aad9d6c7b198a1d494","***********",true,"",********,"账号不存在,请检查accountId的正确性"]
            - ["异常场景-> 姓名和身份证号不一致","817d2ac9931a41df978babef529ac98f","***********",true,"ADVANCED",********,"姓名和身份证号不匹配"]
            - ["异常场景-> 手机号和身份证号不一致","d7904bf188cf44ae8d7e7a3bfe67d3dc","***********",true,"ADVANCED",********,"手机号并非使用此身份证办理"]
            - ["异常场景-> 未满18岁","4b28b8a588b64753bc5ebce32c9db0d1","***********",true,"",********,"未成年用户不允许使用当前产品"]

-
    name: 运营商短信验证码校验异常场景-$CaseName
    testcase: testcases/psn/telecom3VerifyCode_ul.yml
    parameters:
        - CaseName-flowId-authcode-VALIDATE1-VALIDATE2:
            - ["异常场景-> flowId不存在","123456","123456",********,"认证流程不存在"]
            - ["异常场景-> 参数中含有空格","1807054120142794077","1234 56",********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["异常场景-> 验证码为空","1807054120142794077","",********,"缺少参数：短信验证码不能为空"]
            - ["异常场景-> 认证流程已结束","1807054120142794077","123456",********,"认证流程已结束"]
            - ["异常场景-> 未完成三要素","1807072985484651865","123456",********,"请先完成运营商三要素信息核验"]

-
    name: 发起银行4要素核身认证异常场景-$CaseName
    testcase: testcases/psn/bankCard4Factors_ul.yml
    parameters:
        - CaseName-name-idNo-mobileNo-bankCardNo-certType-grade-VALIDATE1-VALIDATE2:
            - ["异常场景-> 信息核验失败","武玉","******************","***********","****************","","",********,"银行卡四要素信息核验失败，请核实银行相关信息的准确性"]
            - ["异常场景-> 姓名和身份证号不一致","武玉","******************","***********","****************","","ADVANCED",********,"姓名与⾝份证号不⼀致"]
            - ["异常场景-> 银行卡号与身份证号不一致","武玉华","******************","***********","****************","","ADVANCED",********,"银行卡号与身份证号不一致"]
            - ["异常场景-> 姓名为空","","******************","***********","****************","","",********,"缺少参数：姓名不能为空"]
            - ["异常场景-> 手机号为空","武玉华","******************","","****************","","",********,"缺少参数：手机号不能为空"]
            - ["异常场景-> 身份证号为空","武玉华","","***********","****************","","",********,"缺少参数：证件号不能为空"]
            - ["异常场景-> 银行卡号为空","武玉华","******************","***********","","","",********,"缺少参数：银行卡号不能为空"]
            - ["异常场景-> 身份证号格式不正确","武玉华","******************1","***********","****************","","",********,"参数错误：请输入正确的身份证号码"]
            - ["异常场景-> 手机号格式不正确","武玉华","******************","***********1","****************","","",********,"参数错误：请输入正确的手机号码"]
            - ["异常场景-> 银行卡号不正确","武玉华","******************","***********","621","","",********,"参数错误：请输入正确的银行卡号"]
#20240401已支持1位            - ["异常场景-> 姓名长度不正确","武","******************","***********","****************","","",********,"参数错误：请输入正确长度的姓名（2-100位）"]
            - ["异常场景-> 中文姓名不支持特殊字符","武 玉华","******************","***********","****************","","",********,"姓名只支持中文/点符号·/半角逗号"]
            - ["异常场景-> 姓名不支持全角逗号","武玉华，","******************","***********","****************","","",********,"姓名只支持中文/点符号·/半角逗号"]
            - ["异常场景-> 姓名支持半角逗号","武玉华,","******************","***********","****************","","",********,"银行卡四要素信息核验失败，请核实银行相关信息的准确性"]
            - ["异常场景-> 参数含有多余空格","武玉华","************* 74071","***********","****************","","",********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["异常场景-> 证件号格式不符合证件类型要求","武玉华","******************","***********","****************","INDIVIDUAL_CH_HONGKONG_MACAO","",********,"参数错误：证件号格式错误"]
            - ["异常场景-> 外籍信息核验失败","李逸群","********","***********","6222620910051004855","INDIVIDUAL_CH_TWCARD","",********,"核验失败"]
            - ["异常场景-> 未满18岁","测试十八","110101202003073778","***********","****************","","",********,"未成年用户不允许使用当前产品"]

-
    name: 发起银行4要素实名认证异常场景-$CaseName
    testcase: testcases/psn/bankCard4FactorsOid_ul.yml
    parameters:
        - CaseName-accountId-mobileNo-bankCardNo-repetition-grade-VALIDATE1-VALIDATE2:
            - ["异常场景-> 银行卡号与身份证号不一致","${ENV(creatorOid)}","***********","****************",true,"ADVANCED",********,"银行卡号与身份证号不一致"]
            - ["异常场景-> 参数含有多余空格","${ENV(creatorOid)}","1330105 3553","****************",true,"",********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["异常场景-> 手机号格式不正确","${ENV(creatorOid)}","************","****************",true,"",********,"参数错误：请输入正确的手机号码"]
            - ["异常场景-> 手机号为空","${ENV(creatorOid)}","","****************",true,"",********,"缺少参数：手机号不能为空"]
            - ["异常场景-> 银行卡号为空","${ENV(creatorOid)}","***********","",true,"",********,"缺少参数：银行卡号不能为空"]
            - ["异常场景-> 银行卡号不正确","${ENV(creatorOid)}","***********","6214",true,"",********,"参数错误：请输入正确的银行卡号"]
            - ["异常场景-> 当前账户已实名","${ENV(creatorOid)}","***********","****************",false,"",********,"当前账户已实名"]
            - ["异常场景-> 缺少姓名","41e937ef353c44459dff230b51cee1fa","***********","****************",true,"",********,"个人用户基本信息缺少必要属性：姓名"]
            - ["异常场景-> 缺少姓名","c8c5dc688f8b496fb17a683660937653","***********","****************",true,"",********,"个人用户基本信息缺少必要属性：身份证号"]
            - ["异常场景-> 信息核验失败","${ENV(creatorOid)}","***********","****************",true,"",********,"银行卡四要素信息核验失败，请核实银行相关信息的准确性"]
            - ["异常场景-> 个人oid传入了企业oid","3c8b9f57bdc1409d9e71a73ce876afef","***********","****************",true,"",********,"为组织机构账号，请传入个人账号"]
            - ["异常场景-> 个人oid不存在","806987531bd24aad9d6c7b198a1d494","***********","****************",true,"",********,"账号不存在,请检查accountId的正确性"]
            - ["异常场景-> 未满18岁","4b28b8a588b64753bc5ebce32c9db0d1","***********","****************",true,"ADVANCED",********,"未成年用户不允许使用当前产品"]

-
    name: 银行预留手机号验证码校验异常场景-$CaseName
    testcase: testcases/psn/bank4VerifyCode_ul.yml
    parameters:
        - CaseName-flowId-authcode-VALIDATE1-VALIDATE2:
            - ["异常场景-> flowId不存在","123456","123456",********,"认证流程不存在"]
            - ["异常场景-> 参数中含有空格","1807054120142794077","1234 56",********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["异常场景-> 验证码为空","1807054120142794077","",********,"缺少参数：短信验证码不能为空"]
            - ["异常场景-> 认证流程已结束","1807054120142794077","123456",********,"认证流程已结束"]
            - ["异常场景-> 未完成四要素","1807072985484651865","123456",********,"请先完成银行卡四要素信息核验"]

-
    name: 获取个人核身认证地址异常场景-$CaseName
    testcase: testcases/psn/indivAuthUrl_ul.yml
    parameters:
        - CaseName-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-project-VALIDATE1-VALIDATE2:
            - ["异常场景-> 不支持的认证方式","123456",[],[],"武玉华","******************","","***********","****************",[],"",********,"参数错误：不支持的认证方式"]
            - ["异常场景-> 手机号不正确","",[],[],"武玉华","******************","","**********","****************",[],"",********,"参数错误：手机号格式不正确"]
            - ["异常场景-> 银行卡号格式不正确","",[],[],"武玉华","******************","","***********","621",[],"",********,"参数错误：银行卡号格式不正确"]
            - ["异常场景-> authAdvancedEnabled非法枚举","",[],[""],"武玉华","******************","","***********","****************",[],"",********,"参数错误：authAdvancedEnabled非法枚举"]
            - ["异常场景-> 含有多余空格","PSN_BANK4_AUTHC ODE",[],[],"武玉华","******************","","***********","****************",[],"",********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["异常场景-> 姓名格式校验","",[],[],"武玉 华","******************","INDIVIDUAL_CH_IDCARD","***********","****************",[],"",********,"姓名只支持中文/点符号·/半角逗号"]
            - ["异常场景-> 中文姓名不支持全角逗号","",[],[],"武玉华，","******************","INDIVIDUAL_CH_IDCARD","***********","****************",[],"",********,"姓名只支持中文/点符号·/半角逗号"]
            - ["异常场景-> 证件号格式大于20位","",[],[],"武玉华","******************1133333","","***********","****************",[],"",********,"参数错误：请输入正确长度的证件号"]
#暂时不做校验            - ["异常场景-> 证件类型不正确","",[],[],"武玉华","******************","123","***********","****************",[],********,"请输入正确的证件类型"]
            - ["异常场景-> 外籍名字不符合规范","",[],[],"###","","INDIVIDUAL_PASSPORT","","",[],"",********,"姓名格式只支持中文/大小写英文/点符号·/破折号-/全角半角括号/半角逗号/点符号."]
            - ["异常场景-> 未满18岁","",[],[],"测试十八","110101202003073778","","***********","****************",[],"",********,"未成年用户不允许使用当前产品"]

-
    name: 获取个人实名认证地址异常场景-$CaseName
    testcase: testcases/psn/indivIdentityUrl_ul.yml
    parameters:
        - CaseName-appId-accountId-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-repeatIdentity-VALIDATE1-VALIDATE2:
            - ["异常场景-> 不支持的认证方式","${ENV(appId)}","${ENV(creatorOid)}","123456",[],[],"武玉华","******************","","***********","****************",[],true,********,"参数错误：不支持的认证方式"]
            - ["异常场景-> 姓名不支持全角逗号","${ENV(appId)}","${ENV(creatorOid)}","",[],[],"武玉华，","******************","","***********","****************214830112162622",[],true,********,"姓名格式只支持中文/大小写英文/点符号·/破折号-/全角半角括号/半角逗号/点符号."]
            - ["异常场景-> 银行卡号格式不正确","${ENV(appId)}","${ENV(creatorOid)}","",[],[],"武玉华","*************7407","","***********","****************",[],true,********,"参数错误：输入证件号与账号不匹配"]
            - ["异常场景-> authAdvancedEnabled非法枚举","${ENV(appId)}","${ENV(creatorOid)}","",[],[""],"武玉华","******************","","***********","****************",[],true,********,"参数错误：authAdvancedEnabled非法枚举"]
            - ["异常场景-> 含有多余空格","${ENV(appId)}","${ENV(creatorOid)}","PSN_BANK4_AUTHC ODE",[],[],"武玉华","******************","","***********","****************",[],true,********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["异常场景-> 个人oid传入了企业oid","${ENV(appId)}","3c8b9f57bdc1409d9e71a73ce876afef","",[],[],"武玉华","******************","","***********","****************",[],true,********,"为组织机构账号，请传入个人账号"]
            - ["异常场景-> 当前账户已实名","${ENV(appId)}","${ENV(creatorOid)}","",[],[],"武玉华","******************","","***********","****************",[],false,********,"当前账户已实名"]
            - ["异常场景-> 未满18","${ENV(appId)}","4b28b8a588b64753bc5ebce32c9db0d1","",[],[],"","","","***********","****************",[],true,********,"未成年用户不允许使用当前产品"]
            #- ["异常场景-> 在1-14之间，appid配置的1岁以上","${ENV(appId4)}","3eb24b98367444bfa393e2042ba12762","",[],[],"","","","***********","****************",[],true,********,"未成年用户不允许使用当前产品"]

-
    name: 获取个人实名认证地址异常场景2-$CaseName
    testcase: testcases/psn/indivIdentityUrl2_ul.yml
    parameters:
        - CaseName-appId-accountId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-indivEditableInfo-repeatIdentity-showAgreement-VALIDATE1-VALIDATE2:
            - ["可编辑和不可编辑参数冲突","${ENV(appId)}","${ENV(psnOid)}","武玉华","******************","","***********","****************",["name"],["name"],true,true,********,"参数错误：indivUneditableInfo和indivEditableInfo包含重复信息"]

-
    name: 发起运营商3要素认证发送验证码异常场景-$CaseName
    testcase: testcases/psn/telecom3FactorAuthCode_ul.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
            - ["认证流程已结束","2381197229760717027",********,"认证流程已结束"]
            - ["认证流程不存在","238119722976071702",********,"认证流程不存在"]

-
    name: 发起银行卡四要素认证发送验证码异常场景-$CaseName
    testcase: testcases/psn/bankCard4FactorAuthCode_ul.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
            - ["认证流程已结束","2381197229760717027",********,"认证流程已结束"]
            - ["认证流程不存在","238119722976071702",********,"认证流程不存在"]

-
    name: 根据flowid获取实名链接异常场景-$CaseName
    testcase: testcases/psn/getFlowUrl_ul.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
            - ["认证流程已结束","2381197229760717027",********,"认证流程已结束"]
            - ["认证流程不存在","238119722976071702",********,"认证流程不存在"]