config:
    name: 运营支撑平台添加和删除支行
    base_url: ${ENV(base_url_rpc)}

teststeps:
-
    name: 添加支行
    api: api/rpc/addSubBank.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data, 0]

-
    name: 查询支行
    api: api/rpc/querySubBank.yml
    extract:
      - sequence: content.data.list.0.id
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.list.0.subBankName, $subBankName]

-
    name: 删除支行
    api: api/rpc/deleteSubBank.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data, true]
