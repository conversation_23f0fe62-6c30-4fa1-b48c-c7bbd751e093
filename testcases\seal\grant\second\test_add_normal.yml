- config:
    name: 新增二级授权-正常场景
    base_url: ${ENV(base_url)}
    variables:
        - orgId: ${ENV(org_id)}
        - sealGrantBizId: ${ENV(seal_grant_biz_id)}
        - grantedAccountId: ${ENV(granted_account_id)}
        - templateId: ${ENV(template_id)}
        - timestamp: ${get_timestamp()}
        - effectiveTime: ${get_current_timestamp()}
        - expireTime: ${get_future_timestamp(30)}

- test:
    name: 正常新增二级授权-指定成员SEAL_USER角色
    variables:
        - params: "t=${timestamp}"
        - json: {
            "orgId": "${orgId}",
            "sealGrantBizId": "${sealGrantBizId}",
            "grantedAccountIds": ["${grantedAccountId}"],
            "roleKey": "SEAL_USER",
            "scopeList": ["${templateId}"],
            "autoFall": false,
            "effectiveTime": ${effectiveTime},
            "expireTime": ${expireTime},
            "grantRedirectUrl": "https://example.com/redirect"
          }
    api: api/seal/grant/second/add.yml
    extract:
        - secSealGrantBizIds: content.data.secSealGrantBizIds
        - flowId: content.data.flowId
        - signUrl: content.data.signUrl
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]
        - eq: ["content.message", "成功"]
        - type_match: ["content.data.secSealGrantBizIds", list]
        - type_match: ["content.data.flowId", str]

- test:
    name: 正常新增二级授权-指定成员SEAL_EXAMINER角色
    variables:
        - params: "t=${timestamp}"
        - json: {
            "orgId": "${orgId}",
            "sealGrantBizId": "${sealGrantBizId}",
            "grantedAccountIds": ["${grantedAccountId}"],
            "roleKey": "SEAL_EXAMINER",
            "scopeList": ["${templateId}"],
            "autoFall": false,
            "effectiveTime": ${effectiveTime},
            "expireTime": ${expireTime}
          }
    api: api/seal/grant/second/add.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", 0]

- test:
    name: 异常场景-缺少必填参数orgId
    variables:
        - params: "t=${timestamp}"
        - json: {
            "sealGrantBizId": "${sealGrantBizId}",
            "grantedAccountIds": ["${grantedAccountId}"],
            "roleKey": "SEAL_USER",
            "scopeList": ["${templateId}"],
            "effectiveTime": ${effectiveTime},
            "expireTime": ${expireTime}
          }
    api: api/seal/grant/second/add.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 异常场景-无效的角色类型
    variables:
        - params: "t=${timestamp}"
        - json: {
            "orgId": "${orgId}",
            "sealGrantBizId": "${sealGrantBizId}",
            "grantedAccountIds": ["${grantedAccountId}"],
            "roleKey": "INVALID_ROLE",
            "scopeList": ["${templateId}"],
            "effectiveTime": ${effectiveTime},
            "expireTime": ${expireTime}
          }
    api: api/seal/grant/second/add.yml
    validate:
        - eq: ["status_code", 200]
        - ne: ["content.code", 0]
