config:
    name: 发起银行卡4要素核身认证，校验短信验证码
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 首次-发起银行卡4要素核身认证
    api: api/psnAuth/bankCard4FactorsPre.yml
    variables:
      idNo: '362430199311156921'
      name: '刘志华'
      bankCardNo: '****************'
      certType: ''
      mobileNo: '***********'
      source: 0
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验短信验证码-正确验证码
    api: api/psnAuth/bank4VerifyCode.yml
    variables:
      flowId: $flowId
      authcode: 123456
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.subFlows.0.subFlowType, "INDIVIDUAL_BANKCARD_4_FACTOR"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "INDIVIDUAL"]
      - eq: [content.data.authType, "INDIVIDUAL_BANKCARD_4_FACTOR"]



