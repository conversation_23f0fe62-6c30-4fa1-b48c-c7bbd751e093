config:
    name: 发起企业实名认证3要素校验-法人授权书认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 发起企业实名认证3要素校验
    api: api/orgAuth/threeFactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 发起授权签署实名认证
    api: api/orgAuth/legalRepSignAuth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 获取签署授权链接
    api: api/orgAuth/signUrl.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.signUrl, null]

-
    name: 查询授权书签署状态
    api: api/orgAuth/legalRepSignResult.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SIGNING"]
      - eq: [content.data.message, "签署中"]
    teardown_hooks:
      - ${teardown_hook_sleep_n_secs(3)}
-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "ING"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.subFlows.0.subFlowType, "LEGAL_REP_SIGN_AUTHORIZE"]
      - eq: [content.data.subFlows.0.status, "ING"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "ING"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "LEGAL_REP_SIGN_AUTHORIZE"]
      - eq: [content.data.organInfo.name, $name]
