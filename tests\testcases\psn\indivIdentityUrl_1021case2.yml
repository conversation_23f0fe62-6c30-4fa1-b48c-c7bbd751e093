config:
    name: 实名入参中修改账号信息导致发起实名失败
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建账号
    api: api/public/createByThirdPartyUserId_psnV1.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.accountId, null]

-
    name: 获取个人实名认证地址
    api: api/psnAuth/indivIdentityUrl3.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, $VALIDATE1]
      - contains: [content.message, $VALIDATE2]
