config:
    name: 发起企业核身认证4要素校验
#备注：1.核身没有人工审核 2.四要素发起，没有法定代表人认证 3.除发起企业核身认证4要素校验单接口，其他异常场景和实名一致，不重复写
#116
testcases:
-
    name: 发起企业核身认证4要素校验-法人授权书认证-$CaseName
    testcase: testcases/org/fourFactors_legalRepSign.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-agentName-agentIdNo-mobileNo:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD","武玉华","******************","***********"]
            - ["企业信息都正确(15位工商注册码)","${ENV(appId)}","杭州天谷信息科技有限公司","***************","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD","武玉华","******************","***********"]
# 暂时注释，模板不支持这么长的法人名字，发起报错，优化后放开，先用下面mock的数据代替           - ["企业信息都正确(外籍法人-护照)","百达晶心精密机械（无锡）有限公司","${ENV(appId)}","91320214733305872J","黄亮茳（WEE LIANG KIANG）","E6409934C","INDIVIDUAL_PASSPORT","武玉华","******************","***********"]
            - ["企业信息都正确(外籍法人-护照)","${ENV(appId)}","百达晶心精密机械（无锡）有限公司","91320214733305872J","黄亮茳（WEE LIANG KIANG）","E6409934C","INDIVIDUAL_PASSPORT","武玉华","******************","***********"]
            - ["企业信息都正确(外籍法人-港澳通行证)","${ENV(appId)}","深圳市锐玲商贸有限公司","91440300060276033X","高英娣","H60159767","INDIVIDUAL_CH_HONGKONG_MACAO","武玉华","******************","***********"]
            - ["企业信息都正确(企业名称中支持特殊字符，法人证件类型传身份证)","${ENV(appId)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","何一兵","110108196710262291","INDIVIDUAL_CH_IDCARD","武玉华","******************","***********"]

-
    name: 发起企业核身认证4要素校验-随机金额打款认证-$CaseName
    testcase: testcases/org/fourFactors_transferRandomAmount.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-cardNo-subbranch-amount-bank:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD","**************","平安银行杭州高新支行","0.01","平安银行"]
            - ["企业信息都正确(15位工商注册码)","${ENV(appId)}","杭州天谷信息科技有限公司","***************","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD","**************","平安银行杭州高新支行","0.01","平安银行"]
            - ["企业信息都正确(外籍法人-护照)","${ENV(appId)}","百达晶心精密机械（无锡）有限公司","91320214733305872J","黄亮茳（WEE LIANG KIANG）","E6409934C","INDIVIDUAL_PASSPORT","**************","平安银行杭州高新支行","0.01","平安银行"]
            - ["企业信息都正确(外籍法人-港澳通行证)","${ENV(appId)}","深圳市锐玲商贸有限公司","91440300060276033X","高英娣","H60159767","INDIVIDUAL_CH_HONGKONG_MACAO","**************","平安银行杭州高新支行","0.01","平安银行"]
            - ["企业信息都正确(企业名称中支持特殊字符，法人证件类型传身份证)","${ENV(appId)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","何一兵","110108196710262291","INDIVIDUAL_CH_IDCARD","**************","平安银行杭州高新支行","0.01","平安银行"]

-
    name: 发起企业核身认证4要素校验-反向打款认证-$CaseName
    testcase: testcases/org/fourFactors_reversePayment.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD"]
            - ["企业信息都正确(15位工商注册码)","${ENV(appId)}","杭州天谷信息科技有限公司","***************","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD"]
            - ["企业信息都正确(外籍法人-护照)","${ENV(appId)}","百达晶心精密机械（无锡）有限公司","91320214733305872J","黄亮茳（WEE LIANG KIANG）","E6409934C","INDIVIDUAL_PASSPORT"]
            - ["企业信息都正确(外籍法人-港澳通行证)","${ENV(appId)}","深圳市锐玲商贸有限公司","91440300060276033X","高英娣","H60159767","INDIVIDUAL_CH_HONGKONG_MACAO"]
            - ["企业信息都正确(企业名称中支持特殊字符，法人证件类型传身份证)","${ENV(appId)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","何一兵","110108196710262291","INDIVIDUAL_CH_IDCARD"]

-
    name: 发起企业核身认证4要素校验-企业支付宝一键实名认证-$CaseName
    testcase: testcases/org/fourFactors_alipayOneclickConfirm.yml
    parameters:
        - CaseName-appId-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-device-token-authCodeAlipay:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","杭州易签宝网络科技有限公司","91330108MA2GKFYB1X","程亮","330821198202051817","INDIVIDUAL_CH_IDCARD","PC","xingchenAccessTokenAO001","xingchenAuthcodeZbf001"]
#            - ["企业信息都正确(外籍法人-护照)","${ENV(appId)}","百达晶心精密机械（无锡）有限公司","91320214733305872J","黄亮茳（WEE LIANG KIANG）","E6409934C","INDIVIDUAL_PASSPORT","H5","xingchenAccessTokenAO006","xingchenAuthcodeZbf006"]
            - ["企业信息都正确(特殊字符都支持，全半角括号兼容)","${ENV(appId)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","张晋","330702198409120432","INDIVIDUAL_CH_IDCARD","PC","xingchenAccessTokenAO005","xingchenAuthcodeZbf005"]

-
    name: 发起企业核身认证4要素校验-加入经办人flowid-$CaseName
    testcase: testcases/org/fourFactors_addAgentFlowid.yml
    parameters:
        - CaseName-appId-orgName-orgCode-legalRepName-legalRepIdNo-legalRepCertType:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","esigntest杭州易签宝","9100000004437959W2","许华建","342622198905262396","INDIVIDUAL_CH_IDCARD"]

#-
#    name: 发起企业核身认证4要素校验-加入场景类型scenesMode-无agentFlowId-$CaseName
#    testcase: testcases/org/fourFactors_addScenesMode_noAgentId.yml
#    parameters:
#        - CaseName-appId-orgName-orgCode-legalRepName-legalRepIdNo-legalRepCertType-agentFlowId-scenesMode-VALIDATE1-VALIDATE2:
#            - ["企业信息都正确-独立认证-agentFlowId为空","${ENV(appId)}","esigntest杭州易签宝","9100000004437959W2","许华建","342622198905262396","INDIVIDUAL_CH_IDCARD",'',1,0,'成功']
#            - ["企业信息都正确-印章使用授权-agentFlowId为空","${ENV(appId)}","esigntest杭州易签宝","9100000004437959W2","许华建","342622198905262396","INDIVIDUAL_CH_IDCARD",'',2,30500101,'参数错误：办理人认证流程ID不能为空']

#-
#    name: 发起企业核身认证4要素校验-加入场景类型scenesMode-有agentFlowId-$CaseName
#    testcase: testcases/org/fourFactors_addScenesMode_agentId.yml
#    parameters:
#        - CaseName-appId-orgName-orgCode-legalRepName-legalRepIdNo-legalRepCertType-agentFlowId-scenesMode-VALIDATE1-VALIDATE2:
#            - ["企业信息都正确-独立认证-agentFlowId不为空","${ENV(appId)}","esigntest杭州易签宝","9100000004437959W2","许华建","342622198905262396","INDIVIDUAL_CH_IDCARD",'',1,0,'成功']
#            - ["企业信息都正确-印章使用授权-agentFlowId不为空","${ENV(appId)}","esigntest杭州易签宝","9100000004437959W2","许华建","342622198905262396","INDIVIDUAL_CH_IDCARD",'',2,0,'成功']
