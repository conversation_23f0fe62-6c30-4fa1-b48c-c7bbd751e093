config:
     name: V0适配接口集合

testcases:
-
     name: 个人信息比对
     testcase: testcases/V0/individualverifies.yml
     parameters:
         - CaseName-cardNo-idNo-mobile-name-verifyType:
             - ["正常场景-身份证二要素比对","6225885868968462","342622198905262396","19057265772","许华建","0"]
             - ["正常场景-运营商三要素比对","6225885868968462","342622198905262396","19057265772","许华建","1"]
             - ["正常场景-银行卡三要素比对","6225885868968462","342622198905262396","19057265772","许华建","2"]
             - ["正常场景-银行卡四要素比对","6225885868968462","342622198905262396","19057265772","测试许华建","3"]
             - ["正常场景-身份证二要素比对（姓名含有生僻字）","","310108198404062845","","江㛃琼","0"]
             - ["正常场景-身份证二要素比对（姓名含有点号）","","652723198412102516","","铁流巴依·铁流白尔干","0"]
             - ["正常场景-银行卡三要素比对（身份证含有X）","6212260200211055682","13240119700530542X","","刘焕娣","2"]
