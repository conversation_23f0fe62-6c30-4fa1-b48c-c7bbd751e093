config:
    name: 发起企业实名认证3要素校验
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 发起企业实名认证3要素校验
    api: api/orgAuth/threeFactorsOid.yml
    validate:
        - eq: [status_code,200]
        - eq: [content.code, $VALIDATE1]
        - contains: [content.message, $VALIDATE2]
