config:
    name: singpass个人实名流程：负面测试用例
    variables:
        accountIdPsn: ${ENV(singpass_psn_oid_realname)}
        accountIdOrg: ${ENV(singpass_org_oid_notrealnamed)}
        name: "singpass test ${create_randomIdStr(3)}"
        certNo: "S8800003G"
        mobile: "***********"
        mobile2: "***********"

teststeps:
    -   name: 发起个人实名-用户oid已经实名
        api: api/rpc/singpassPsnApply.yml
        variables:
            accountId: $accountIdPsn
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, ********]
            -   contains: [content.message, "当前账户已实名"]

    -   name: 发起个人实名-用企业oid发起
        api: api/rpc/singpassPsnApply.yml
        variables:
            accountId: $accountIdOrg
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, ********]
            -   eq: [content.message, "${accountIdOrg}为组织机构账号，请传入个人账号"]

    -   name: 发起个人实名-oid不存在
        api: api/rpc/singpassPsnApply.yml
        variables:
            accountId: "test1234567890"
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, ********]
            -   eq: [content.message, "账号不存在,请检查accountId的正确性"]