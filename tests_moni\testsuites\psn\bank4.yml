config:
    name: 个人银行四要素
testcases:
-
    name: 个人银行四要素-$CaseName
    testcase: testcases/psn/bank4.yml
    parameters:
        - CaseName-name-idNo-mobileNo-bankCardNo-VALIDATE1-VALIDATE2:
            - ["正常场景-> 信息都正确","姜娇","******************","***********","****************",0,"成功"]
            - ["异常场景-> 姓名不正确","武玉","******************","***********","****************",********,"失败"]
            - ["异常场景-> 身份证不正确","武玉华","******************","***********","****************",********,"失败"]
            - ["异常场景-> 手机号不正确","武玉华","******************","***********","****************",********,"失败"]
            - ["异常场景-> 银行卡不正确","武玉华","******************","***********","****************",********,"失败"]
            
            

