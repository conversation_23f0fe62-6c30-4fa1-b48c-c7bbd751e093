config:
    name: 刷脸意愿认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 创建个人意愿刷脸认证
    api: api/psnAuth/createWillingnessFaceauth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.authUrl, null]

-
    name: 查询个人意愿刷脸结果
    api: api/psnAuth/getWillingnessFaceauth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.result.0.faceAuthMode, $VALIDATE1]
      - eq: [content.data.result.0.passed, $VALIDATE2]
      - eq: [content.data.result.0.status, $VALIDATE3]
      - eq: [content.data.result.0.message, $VALIDATE4]
-
    name: 轮询个人意愿刷脸结果
    api: api/psnAuth/getWillingnessFaceauthPolling.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.result.0.faceAuthMode, $VALIDATE1]
      - eq: [content.data.result.0.passed, $VALIDATE2]
      - eq: [content.data.result.0.status, $VALIDATE3]
