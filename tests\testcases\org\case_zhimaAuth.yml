config:
    name: 发起企业实名认证-芝麻认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlOid6.yml
    variables:
      accountId: ${ENV(orgId_zhima)}
      authType: ""
      availableAuthTypes: []
      agentAuthAdvancedEnabled: []
      agentAccountId: ${ENV(psnId_zhima)}
      certNo: ""
      organizationType: ""
      name: ""
      legalRepCertType: ""
      legalRepCertNo: ""
      legalRepName: ""
      repeatIdentity: 6
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [ status_code,200 ]
      - eq: [ content.code, 0 ]
      - contains: [ content.message, "成功" ]
      - ne: [ content.data.flowId, null ]

-
    name: 企业经办人认证状态查询
    api: api/orgAuth/getAgentStatus2.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ status_code,200 ]
      - eq: [ content.code, 0 ]
      - contains: [ content.data.status, "SKIP" ]

-
    name: 发起企业芝麻认证申请
    api: api/orgAuth/api_zhimaApply.yml
    variables:
      flowId: $flowId
      certNo: "91310117MA1J5D3R9Q"
      device: ""  #客户端类型
      dnsAppId: ""  #动态域名 APPID
      lang: ""  #国际化语言类型
      name: "上海灵契软件中心"
      redirectUrl: "http://baidu.com"  #认证完成后页面跳转地址
      timestamp: ""  #请求时间戳，单位精确到毫秒
      version: ""  #请求版本
    validate:
      - eq: [ status_code,200 ]
      - eq: [ content.code, 0 ]
      - contains: [ content.message, "成功" ]
      - ne: [ content.data.forwardUrl, null ]

-
    name: 轮询企业芝麻认证结果
    api: api/orgAuth/api_zhimaQueryPolling.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ status_code,200 ]
      - eq: [ content.code, 0 ]
      - eq: [ content.data.authStatus, 'INIT' ]

-
    name: 查询企业芝麻认证结果
    api: api/orgAuth/api_zhimaQuery.yml
    variables:
      flowId: $flowId
    validate:
      - eq: [ status_code,200 ]
      - eq: [ content.code, 0 ]
      - eq: [ content.data.authStatus, 'INIT' ]

-
    name: 失效flowid
    api: api/public/loseEfficacyByFlowId.yml
    variables:
      - flowId: $flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]