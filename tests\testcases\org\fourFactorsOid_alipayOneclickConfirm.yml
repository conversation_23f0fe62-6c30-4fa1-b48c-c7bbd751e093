config:
    name: 发起企业实名认证4要素校验-企业支付宝一键认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserIdV1.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 发起企业实名认证4要素校验
    api: api/orgAuth/fourFactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 申请企业支付宝一键实名
    api: api/orgAuth/alipayOneclickApply.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - ne: [content.data.forwardUrl, null]

-
    name: 查询企业支付宝一键实名结果
    api: api/orgAuth/alipayOneclickResult.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.completed, False]
      - eq: [content.data.success, False]
      - eq: [content.data.message, "认证未完成"]

-
    name: 企业支付宝一键实名
    api: api/orgAuth/alipayOneclickConfirm.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.success, True]
      - eq: [content.data.message, "认证成功"]

-
    name: 查询企业支付宝一键实名结果
    api: api/orgAuth/alipayOneclickResult.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - eq: [content.data.success, True]
      - eq: [content.data.message, "认证成功"]
-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.subFlows.0.subFlowType, "ENTERPRISE_CERTIFICATION_ONE_KEY_WITH_ALIPAY"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "ENTERPRISE_CERTIFICATION_ONE_KEY_WITH_ALIPAY"]
      - eq: [content.data.organInfo.name, $name]
      - eq: [content.data.organInfo.legalRepCertType, $VALIDATE1]


-
    name: 根据flowId查询其认证子service的详情 对内服务GET方式
    api: api/public/queryFlowServiceInnerLineGet.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "成功"]
      - eq: [content.data.objectType, "企业"]
      - eq: [content.data.subFlows.0.subFlowType, "企业支付宝一键实名"]

-
    name: 根据flowId查询其认证子service的详情 对内服务Post方式
    api: api/public/queryFlowServiceInnerLinePost.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "成功"]
      - eq: [content.data.objectType, "企业"]
      - eq: [content.data.subFlows.0.subFlowType, "企业支付宝一键实名"]

-
    name: 查询存证数据
    api: api/public/getEvidenceData.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.0.extDataMap.companyName, $name]
      - eq: [content.data.0.extDataMap.codeUsc, $idNumber]
      - eq: [content.data.0.extDataMap.legalPersonIdno, $orgLegalIdNumber]
      - eq: [content.data.0.extDataMap.legalPersonName, $orgLegalName]
      - eq: [content.data.1.extDataMap.name, $name]
      - eq: [content.data.1.extDataMap.certNo, $idNumber]
      - eq: [content.data.1.extDataMap.legalRepCertNo, $orgLegalIdNumber]
      - eq: [content.data.1.extDataMap.legalRepName, $orgLegalName]
      - eq: [content.data.1.extDataMap.legalRepCertType, $VALIDATE2]
      - eq: [content.data.1.realNameStep, "ENTERPRISE_ALIPAY_AUTH"]
      - eq: [content.data.1.realNameWay, "ENTERPRISE_ALIPAY_AUTH"]

-
    name: 查询用户最近一次认证信息
    api: api/public/lastDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.data.flowId, $flowId]
      - eq: [content.data.status, SUCCESS]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "ENTERPRISE_CERTIFICATION_ONE_KEY_WITH_ALIPAY"]
      - eq: [content.data.accountId, $accountId]
