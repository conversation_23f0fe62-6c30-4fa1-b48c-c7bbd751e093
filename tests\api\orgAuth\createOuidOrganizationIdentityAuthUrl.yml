name: 获取组织机构实名认证地址RPC接口-认证授权在用
request:
    url: /createOuidOrganizationIdentityAuthUrl/accountId/request?accountId=${accountId}
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: $appId
        X-Tsign-Open-Auth-Mode: simple
    json:
      authType: $authType
      availableAuthTypes: $availableAuthTypes
      agentAccountId: $agentAccountId
      defaultAgentAuthType: $defaultAgentAuthType
      agentAvailableAuthTypes: $agentAvailableAuthTypes
      contextInfo:
        contextId: "测试"
        notifyUrl: ${ENV(notifyUrl)}
        origin: "BROWSER"
        redirectUrl: "http://baidu.com"
        showResultPage: true
      repeatIdentity: true
      orgEntity:
        certNo: $certNo
        organizationType: $organizationType
        name: $name
        legalRepCertType: $legalRepCertType
        legalRepCertNo: $legalRepCertNo
        legalRepName: $legalRepName
        agentBankCardNo: $agentBankCardNo
        agentMobile: $agentMobile
      configParams:
        orgUneditableInfo: $orgUneditableInfo
        orgEditableInfo: $orgEditableInfo
        bizAppId: $appId
