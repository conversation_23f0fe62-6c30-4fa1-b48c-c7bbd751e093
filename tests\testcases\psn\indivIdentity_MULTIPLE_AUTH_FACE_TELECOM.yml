config:
    name: 获取个人实名认证地址
    base_url: ${ENV(base_url)}
    variables:
      certNo : "342425198911080888"
      mobileNo : "***********"
      name : "测试东东"
      appId: "**********"
      accountId: ${ENV(psnOid)}

teststeps:
-
    name: 获取个人实名认证地址2
    api: api/psnAuth/indivIdentityUrl2.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 发起多因子认证-手机认证
    api: api/psnAuth/apply_MULTIPLE_AUTH_FACE_TELECOM.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 发起多因子认证-银行卡认证
    api: api/psnAuth/apply_MULTIPLE_AUTH_FACE_BANK.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]