config:
    name: 获取个人实名认证地址+页面版通过人工完成实名（appid默认开启了全局的人工审核）
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建账号
    api: api/public/createByThirdPartyUserId_psnV1.yml
    extract:
      - accountId: content.data.accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.accountId, null]

-
    name: 获取个人实名认证地址
    api: api/psnAuth/indivIdentityUrl4.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版查询个人认证信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.bankCardNo, $bankCardNo]
      - eq: [content.data.oid, $accountId]

-
    name: 查询个人认证流程状态
    api: api/psnAuth/queryIndivFlowidStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "INIT"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版获取人工实名审核结果(进入人工审核页面后查询一次)
    api: api/psnAuth/getPsnManualRst.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.serviceStatus, -1]

-
    name: 页面版填写邮箱地址后发送验证码
    api: api/psnAuth/manualAuthCode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - contains: [content.data, "-"]

-
    name: 页面版人工实名上传照片1
    api: api/public/fileUploadUrl.yml
    extract:
      - fileKey1: content.data.fileKey
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.uploadUrl, null]

-
    name: 页面版人工实名上传照片2
    api: api/public/fileUploadUrl.yml
    extract:
      - fileKey2: content.data.fileKey
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.uploadUrl, null]

-
    name: 页面版人工实名上传照片3
    api: api/public/fileUploadUrl.yml
    extract:
      - fileKey3: content.data.fileKey
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.uploadUrl, null]

#上传文件暂未解决，此处校验提交审核失败的场景
-
    name: 页面版提交人工审核
    api: api/psnAuth/submitAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 30501003]
      - contains: [content.message, "个人人工认证资料未完全上传完成"]
