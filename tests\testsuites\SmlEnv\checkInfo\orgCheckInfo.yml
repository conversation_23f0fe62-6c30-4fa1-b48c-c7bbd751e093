config:
     name: 企业信息比对
#重点：错误码根据不同供应商的返回有所不同，以下错误码需指定供应商主通道：
#企业四要素主通道-敬众，企业二要素主通道-企信宝，律所三要素主通道-企信宝
#非工商组织3要素主通道-企信宝，企业三要素主通道-企信宝
testcases:
-
     name: 组织机构二要素比对
     testcase: testcases/checkInfo/enterprise2_1.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州天谷信息科技有限公司","913301087458306077",0,"成功","-"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","330108000003512",0,"成功","-"]
             - ["正常场景-> 34开头内部调用社会组织二要素接口","福建正中司法鉴定所","34350000796081060U",0,"成功","-"]
             - ["正常场景-> 31开头内部调用律所二要素接口","山东明朗律师事务所","313700000973293896",0,"成功","-"]

-
     name: 企业三要素比对
     testcase: testcases/checkInfo/enterprise3_1.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确，填18位codeUSC","杭州天谷信息科技有限公司","金宏洲","913301087458306077",0,"成功","-"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","金宏洲","330108000003512",0,"成功","-"]

-
     name: 企业四要素比对
     testcase: testcases/checkInfo/enterprise4_1.yml
     parameters:
         - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州天谷信息科技有限公司","金宏洲","330722197904110013","913301087458306077",0,"成功","-"]
             - ["正常场景-> 信息都正确,填15位codeREG","杭州天谷信息科技有限公司","金宏洲","330722197904110013","330108000003512",0,"成功","-"]
             - ["正常场景-> legalRepName中间含有空格，供应商会自动去掉空格校验","杭州天谷信息科技有限公司","金 宏洲","330722197904110013","913301087458306077",0,"成功","-"]

-
     name: 律所三要素比对
     testcase: testcases/checkInfo/lawFirm3_1.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","山东明朗律师事务所","王文博","313700000973293896",0,"成功","-"]

-
     name: 组织机构三要素比对
     testcase: testcases/checkInfo/organization3_1.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","杭州天谷信息科技有限公司","金宏洲","913301087458306077",0,"成功","-"]
             - ["正常场景-> 34开头内部调用社会组织三要素接口","福建正中司法鉴定所","金尔启","34350000796081060U",0,"成功","-"]
             - ["正常场景-> 31开头内部调用律所三要素接口","山东明朗律师事务所","王文博","313700000973293896",0,"成功","-"]

-
     name: 非工商组织三要素比对
     testcase: testcases/checkInfo/orgSocial3_1.yml
     parameters:
         - CaseName-name-legalRepName-codeUSC-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","天津大学","柴立元","12100000401359321Q",0,"成功","-"]

-
     name: 企业二要素比对_律所二要素
     testcase: testcases/checkInfo/enterprise2_1.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","浙江浙临律师事务所","31330000725275079F",0,"成功","-"]

-
     name: 企业二要素比对_社会组织二要素
     testcase: testcases/checkInfo/enterprise2_1.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","天津大学","12100000401359321Q",0,"成功","-"]
