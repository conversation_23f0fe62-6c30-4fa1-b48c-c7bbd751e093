config:
    name: 其他非对外文档接口

testcases:
-
    name: 个人实名分页查询-$CaseName
    testcase: testcases/psn/queryIndividualList.yml
    parameters:
        - CaseName-appId-certNo-name-flowId-pageIndex-pageSize-status:
            - ["查询一条记录","${ENV(appId)}","******************","武玉华","",0,1,2]

-
    name: 通过oid失效流程-$CaseName
    testcase: testcases/psn/loseEfficacyByAccountId.yml
    parameters:
        - CaseName-name-idType-idNumber-mobile-email-bankCardNo-mobileNo-repetition-grade:
            - ["正常流程","测试集测使用","","330327199106085684","***********","","6222081203009064490","***********",true,""]

-
    name: 通过flowid失效流程-$CaseName
    testcase: testcases/psn/loseEfficacyByFlowId.yml
    parameters:
        - CaseName-name-idType-idNumber-mobile-email-bankCardNo-mobileNo-repetition-grade:
            - ["正常流程","测试集测使用","","330327199106085684","***********","","6222081203009064490","***********",true,""]

#无法并发，暂时注释
#-
#    name: 保存/更新个人信息-$CaseName
#    testcase: testcases/psn/updateIndividualInfo.yml
#    parameters:
#        - CaseName-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-project-updateBankCardNo-updateCertNo-updateCertType-updateMobileNo-updateName-VALIDATE1-VALIDATE2-VALIDATE3-VALIDATE4-VALIDATE5:
#            - ["更新场景1","",[],[],"武玉华","******************","","***********","****************",[],"测试","****************","********","INDIVIDUAL_CH_TWCARD","***********","集测","集测","********","***********","****************","INDIVIDUAL_CH_TWCARD"]
#            - ["更新场景2","",[],[],"","","","","",[],"","****************","******************","","***********","武玉华","武玉华","******************","***********","****************",null]

-
    name: 查询刷脸后的存证数据-$CaseName
    testcase: testcases/psn/faceauth_evidenceData.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2-VALIDATE3-VALIDATE4:
            - ["正常流程-刷脸前二要素","2560940494692027286","362302198609175011","陈威宇","PERSON_2FACTOR_CHECK","FACE_AUTH"]
            - ["正常流程-刷脸前三要素","2560794919510221889","362302198609175011","陈威宇","PERSON_3FACTOR_CHECK","FACE_AUTH"]

-
    name: 查询刷脸后的存证数据2-$CaseName
    testcase: testcases/psn/faceauth_evidenceData.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2-VALIDATE3-VALIDATE4-VALIDATE5:
            - ["正常流程-刷脸前三要素+验证码","2560798548438819853","362302198609175011","陈威宇","PERSON_3FACTOR_CHECK","FACE_AUTH","***********"]

#token失效，暂时注释
#-
#    name: 创建个人支付宝一键实名认证-$CaseName
#    testcase: testcases/psn/individualAlipay.yml
#    parameters:
#        - CaseName-accountId:
#            - ["正常流程","${ENV(psnOid)}"]
