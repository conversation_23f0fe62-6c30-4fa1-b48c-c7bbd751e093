config:
    name: 发起运营商3要素错误信息
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取个人核身认证地址
    api: api/psnAuth/indivAuthUrl3.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 发起运营商3要素认证web
    api: api/psnAuth/telecom3FactorInfoAuth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 30504042]
      - contains: [content.message, "姓名、身份证或手机号不一致，请检查后重试"]