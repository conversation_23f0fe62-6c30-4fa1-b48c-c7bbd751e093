config:
    name: 法人线下授权书认证
    base_url: ${ENV(base_url)}
    variables:

teststeps:

- name: 创建企业oid
  api: api/public/createByThirdPartyUserId.yml
  extract:
    - accountId: content.data.orgId
  validate:
    - eq: [ status_code,200 ]
    - eq: [ content.code, 0 ]
    - contains: [ content.message, "成功" ]
    - ne: [ content.data.orgId, null ]

- name: 获取组织机构实名认证地址
  api: api/orgAuth/orgIdentityUrlV2.yml
  extract:
    - flowId: content.data.flowId
  validate:
    - eq: [ status_code,200 ]
    - eq: [ content.code, 0 ]
    - contains: [ content.message, "成功" ]
    - ne: [ content.data.url, null ]

#-
#    name: 上传营业执照
#    api: api/public/fileUploadUrl.yml
#    variables:
#      - contentMd5: "7kmVFM4/N1pG7aXPFTNt4Q=="
#      - contentType: "application/octet-stream"
#      - fileName: "42ec017f7dc66324e58d25819307afb1.jpeg"
#      - fileSize: 93391
#    extract:
#      - fileKey1: content.data.fileKey
#    validate:
#      - eq: [ status_code,200 ]
#      - eq: [ content.code, 0 ]
#      - eq: [ content.message, "成功" ]
#      - ne: [ content.data.uploadUrl, null ]

#-
#    name: 获取营业执照信息
#    api: api/orgAuth/授权书认证/企业实名信息核验营业执照ocr.yml
#    variables:
#          imgKey: '$$9edfe1c4-2606-4c71-949b-235e715d495d2632154212'
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 0]
#      - contains: [content.message, "成功"]
#      - ne: [content.data.flowId, null]

-
    name: 页面版信息比对
    api: api/orgAuth/infoVerify.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, $code]

-
    name: 页面版企业流程中查询企业信息
    api: api/orgAuth/organizationInfo.yml
    validate:
      - eq: [status_code,200]

-
    name: 查询授权书详情
    api: api/orgAuth/organizationInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]

-
    name: 授权书确认
    api: api/orgAuth/授权书认证/线下授权书确认.yml
    variables:
      certNo: $idNumber
      legalRepName: $orgLegalName
    validate:
      - eq: [status_code,200]
      - eq: [content.data.flowId, $flowId]



