config:
    name: 个人实名协议
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取个人实名认证地址
    api: api/psnAuth/indivIdentityUrl.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
    extract:
      flowId: content.data.flowId

-
    name: 获取协议配置列表
    api: api/agreement/userAgreementList.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.userAgreementList, null]
    extract:
      #e签宝CA
      userAgreementId: content.data.userAgreementList.2.userAgreementId
      userAgreementTitle: content.data.userAgreementList.2.userAgreementTitle
      objectType: content.data.userAgreementList.2.objectType

-
    name: 获取协议详情
    api: api/agreement/userAgreementDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.userAgreementId, null]
      - eq: [content.data.userAgreementTitle, "数字证书服务协议"]

-
    name: 同意协议
    api: api/agreement/userAgreementAgree.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]