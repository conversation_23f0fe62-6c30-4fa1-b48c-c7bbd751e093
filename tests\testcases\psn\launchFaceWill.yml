config:
    name: 发起个人实名核身认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起个人刷脸核身认证
    api: api/psnAuth/launchFace2.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
      - ne: [content.data.authUrl, null]

-   name: 查询个人刷脸状态
    api: api/psnAuth/queryFaceStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "ING"]
      - eq: [content.data.message, "刷脸认证未完成"]

-   name: 获取可信账号数据
    api: api/psnAuth/getIndividualInfo_willing.yml
    extract:
      - mobileNo: content.data.mobileNo
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.mobileNo, $mobileNo]

-   name: 发起意愿验证码
    api: api/psnAuth/sendCode_willing.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, $message1]

-   name: 重新发起意愿验证码
    api: api/psnAuth/resendCode_willing.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-   name: 短信验证码校验
    api: api/psnAuth/verifySendcode_willing.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, $message2]



