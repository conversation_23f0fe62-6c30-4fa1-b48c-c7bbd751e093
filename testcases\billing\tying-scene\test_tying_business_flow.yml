- config:
    name: 商品搭售业务流程场景
    base_url: ${ENV(base_url)}
    variables:
        - personal_gid_new: ${ENV(personal_gid_new)}
        - enterprise_gid_new: ${ENV(enterprise_gid_new)}
        - tenant_id: ${ENV(tenant_id)}
        - sale_schema_id: ${ENV(sale_schema_id)}

- test:
    name: 个人账号完整搭售流程-无需校验挂靠-首次使用
    variables:
        - gid: ${personal_gid_new}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    extract:
        - personalTyingProducts: content.data.tyingProducts
        - personalCanOrder: content.data.canOrder
        - personalFirstProductName: content.data.tyingProducts.0.productName
        - personalDiscountFlag: content.data.tyingProducts.0.discountFlag
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.success", true]
        - eq: ["content.data.canOrder", true]
        - type_match: ["content.data.tyingProducts", list]
        - gt: ["len(content.data.tyingProducts)", 0]
        - eq: ["content.data.tyingProducts.0.discountFlag", true]
        - eq: ["content.data.tyingProducts.0.specialOfferFlag", true]

- test:
    name: 企业账号完整搭售流程-无需校验挂靠-首次使用
    variables:
        - gid: ${enterprise_gid_new}
        - tenant_id: ${tenant_id}
        - client_id: "WE_CHAT"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    extract:
        - enterpriseTyingProducts: content.data.tyingProducts
        - enterpriseCanOrder: content.data.canOrder
        - enterpriseProductPrice: content.data.tyingProducts.0.currentPrice
        - enterpriseOriginalPrice: content.data.tyingProducts.0.originalPrice
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.data.canOrder", true]
        - lt: ["content.data.tyingProducts.0.currentPrice", "content.data.tyingProducts.0.originalPrice"]

- test:
    name: 验证不同客户端搭售信息一致性-支付宝小程序
    variables:
        - gid: ${personal_gid_new}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    extract:
        - alipayProductCount: len(content.data.tyingProducts)
        - alipayFirstProductId: content.data.tyingProducts.0.productId
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.data.canOrder", true]

- test:
    name: 验证不同客户端搭售信息一致性-微信小程序
    variables:
        - gid: ${personal_gid_new}
        - tenant_id: ${tenant_id}
        - client_id: "WE_CHAT"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    extract:
        - wechatProductCount: len(content.data.tyingProducts)
        - wechatFirstProductId: content.data.tyingProducts.0.productId
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.data.canOrder", true]
        - eq: ["${alipayProductCount}", "${wechatProductCount}"]
        - eq: ["${alipayFirstProductId}", "${wechatFirstProductId}"]

- test:
    name: 搭售商品优惠信息验证
    variables:
        - gid: ${personal_gid_new}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - contains: ["content.data.tyingProducts.0", "discountType"]
        - contains: ["content.data.tyingProducts.0", "discountValue"]
        - or: [
            ["eq", ["content.data.tyingProducts.0.discountType", "DISCOUNT"]],
            ["eq", ["content.data.tyingProducts.0.discountType", "DIRECT_REDUCTION"]]
          ]
