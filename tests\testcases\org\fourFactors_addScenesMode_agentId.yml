config:
    name: 发起企业核身认证4要素校验-增加场景类型scenesMode入参-带agentId
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起运营商3要素核身认证
    api: api/psnAuth/telecom3Factors.yml
    variables:
          name: "许华建"
          idNo: "342622198905262396"
          mobileNo: "19057265772"
          grade: "STANDARD"
    extract:
      - agentFlowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 校验短信验证码-正确验证码
    api: api/psnAuth/telecom3VerifyCode.yml
    variables:
      authcode: "123456"
      flowId: $agentFlowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 发起企业核身认证4要素校验
    api: api/orgAuth/fourFactors_addScenesMode.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, $VALIDATE1]
      - contains: [content.message, $VALIDATE2]
 #     - ne: [content.data.flowId, null]
