config:
    name: 获取组织机构实名认证地址-查询认证方式
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlOid5.yml
    variables:
      - defaultAgentAuthType: []
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 页面版信息比对
    api: api/orgAuth/infoVerify.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.authType.allOrgAuthType, $VALIDATE]

#-
#    name: 失效flowid
#    api: api/public/loseEfficacyByFlowId.yml
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 0]
#      - contains: [content.message, "成功"]