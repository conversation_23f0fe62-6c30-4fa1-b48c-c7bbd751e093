config:
    name: 发起企业实名认证4要素校验-随机打款认证
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserIdV1.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 发起企业实名认证4要素校验
    api: api/orgAuth/fourFactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询打款银行信息
    api: api/orgAuth/subbranch.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.list.0.bankName, $subbranch]

-
    name: 发起随机金额打款认证
    api: api/orgAuth/transferRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询随机金额打款进度1
    api: api/orgAuth/transferProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.process, "PAID"]
      - eq: [content.data.cardNo, $cardNo]
      - eq: [content.data.message, "打款申请成功"]

-
    name: 随机金额校验
    api: api/orgAuth/verifyRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询随机金额打款进度2
    api: api/orgAuth/transferProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.process, "ORGANFINISHED"]
      - eq: [content.data.message, "打款申请成功"]

-
    name: 查询认证主流程明细
    api: api/public/queryFlowidOutline.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.subFlows.0.subFlowType, "ORGANIZATION_TRANSFER_RANDOM_AMOUNT"]
      - eq: [content.data.subFlows.0.status, "SUCCESS"]

-
    name: 查询认证信息
    api: api/public/queryFlowidDetail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "SUCCESS"]
      - eq: [content.data.objectType, "ORGANIZATION"]
      - eq: [content.data.authType, "ORGANIZATION_TRANSFER_RANDOM_AMOUNT"]
      - eq: [content.data.organInfo.name, $name]
      - eq: [content.data.organInfo.legalRepCertType, $VALIDATE1]
      - eq: [content.data.organInfo.cardNo, $cardNo]
      - eq: [content.data.organInfo.bank, $bank]
      - eq: [content.data.organInfo.subbranch, $subbranch]

-
    name: 查询存证数据
    api: api/public/getEvidenceData.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.0.extDataMap.companyName, $name]
      - eq: [content.data.0.extDataMap.codeUsc, $idNumber]
      - eq: [content.data.0.extDataMap.legalPersonIdno, $orgLegalIdNumber]
      - eq: [content.data.0.extDataMap.legalPersonName, $orgLegalName]
      - eq: [content.data.1.extDataMap.accountName, $name]
      - eq: [content.data.1.extDataMap.bankCardNo, "**************"]
      - eq: [content.data.1.extDataMap.bankBranch, "平安银行杭州高新支行"]
      - contains: [content.data.1.extDataMap.bigCashNo, "2584"]
      - eq: [content.data.1.extDataMap.bankName, "平安银行"]
      - eq: [content.data.1.extDataMap.bankProvince, "浙江"]
      - eq: [content.data.1.extDataMap.cash, 0.01]
      - eq: [content.data.1.extDataMap.bankCity, "杭州市"]
      - eq: [content.data.1.realNameStep, "PAY_PUBLIC"]
      - eq: [content.data.1.realNameWay, "PAYMENT_PUBLIC_AUTH"]
      - eq: [content.data.2.extDataMap.filledCash, 0.01]
      - eq: [content.data.2.realNameStep, "FILL_CASH"]
      - eq: [content.data.2.realNameWay, "PAYMENT_PUBLIC_AUTH"]

#-
#    name: 根据企业oid查询实名详情--法人章使用的rpc接口
#    api: api/orgAuth/queryOrganizationDetailByAccountId.yml
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 0]
#      - eq: [content.data.flowId, $flowId]
#      - eq: [content.data.status, "SUCCESS"]
#      - eq: [content.data.objectType, "ORGANIZATION"]
#      - eq: [content.data.accountId, $accountId]
#      - eq: [content.data.authType, "ORGANIZATION_TRANSFER_RANDOM_AMOUNT"]
#      - eq: [content.data.organizationType, 1]
#      - eq: [content.data.name, $name]
#      - eq: [content.data.certNo, $idNumber]
#      - eq: [content.data.certType, "ORGANIZATION_USC_CODE"]
#      - eq: [content.data.legalRepName, $orgLegalName]
#      - eq: [content.data.legalRepCertNo, $orgLegalIdNumber]
