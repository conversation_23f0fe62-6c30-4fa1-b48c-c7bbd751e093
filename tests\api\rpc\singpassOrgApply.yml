name: 申请singpass企业实名
base_url: ${ENV(base_url_rpc)}
request:
    url: /v2/identity/auth/api/international/organization/singpass/apply/$accountId
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(abroad_appid)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        agentAccountId: $agentAccountId
        redirectUrl: ${ENV(singpass_org_callback)}
