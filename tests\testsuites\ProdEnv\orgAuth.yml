config:
    name: 企业实名线上脚本

testcases:
-
    name: 获取企业实名认证地址-$CaseName
    testcase: testcases/prodTestcase/orgIdentityUrl.yml
    parameters:
        - CaseName-accountId-agentAccountId:
            - ["正常场景","bd790f4734ed4ff8a9b10d3241e420c9","66eb5b60d1e6480c804cc7fdfd461339"]

-
    name: 对公打款完成企业实名-$CaseName
    testcase: testcases/prodTestcase/threeFactorsOid_transferRandomAmount.yml
    parameters:
        - CaseName-accountId-creatorOid-subbranch-cardNo-amount:
            - ["正常场景","bd790f4734ed4ff8a9b10d3241e420c9","66eb5b60d1e6480c804cc7fdfd461339","平安银行杭州高新支行","**************","0.01"]
