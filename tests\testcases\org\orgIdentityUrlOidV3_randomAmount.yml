config:
    name: 获取组织机构实名认证地址V3-随机打款认证
    base_url: ${ENV(base_url_rpc)}

teststeps:
#创建企业oid，发起实名，随机打款认证，提交后重新发起，再随机打款认证，提交后授权书审批拒绝，再次重新发起，随机打款认证，授权书审批通过
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlV3.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.url, "tsignversion=eve"]

-
    name: 页面版企业经办人认证状态查询
    api: api/orgAuth/agentStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.authorizedInV21, true]
      - eq: [content.data.authorisedInUserCenter, true]

-
    name: 页面版企业页面实名认证, 结果接口V2--前端打开页面之后查询实名的进程
    api: api/orgAuth/processonWeb.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版信息比对
    api: api/orgAuth/infoVerify.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]
      - eq: [content.data.operatorType, $operatorType]

-
    name: 页面版查询随机金额业务进度
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]

-
    name: 页面版查询反向打款业务进度
    api: api/orgAuth/reversePaymentProcess.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "INIT"]
      - eq: [content.data.mainAccountName, "杭州天谷信息科技有限公司"]

-
    name: 页面版查询银行信息
    api: api/orgAuth/querySubbranch.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - ne: [content.data.list.0.bank, null]
      - contains: [content.data.list.0.bankName, $keyWord]

-
    name: 页面版对公打款发送授权书
    api: api/orgAuth/sendAuthorizationEmail.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data, null]

-
    name: 页面版随机金额对公打款
    api: api/orgAuth/transferRandomAmountWeb.yml
    variables:
      cardNo: "*********"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版查询随机金额业务进度2
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "PAID"]
      - eq: [content.data.message, "打款申请成功"]

-
    name: 页面版随机金额校验
    api: api/orgAuth/randomAmountWeb.yml
    variables:
      cash: 0.01
      finishCash: false
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版查询随机金额业务进度3
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "ORGANFINISHED"]
      - eq: [content.data.message, "打款申请成功"]
 #     - eq: [content.data.taskGroupDoing.0, "AUTHORIZATION"]
#      - count_eq: [content.data.taskGroupDoing, 1]

-
    name: 页面版等待审核的中间页上点击重新认证
    api: api/orgAuth/paymentTaskFinish.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版查询随机金额业务进度4-提取remaintimes
    api: api/orgAuth/transferProcessForRandomAmount.yml
    extract:
      - remainTimes: content.data.remainTimes
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "ORGANFINISHED"]
#      - eq: [content.data.taskGroupTerminated.0, "AUTHORIZATION"]

-
    name: 页面版随机金额对公打款2--重新打款，但是银行卡号信息没有变化
    api: api/orgAuth/transferRandomAmountWeb.yml
    variables:
      cardNo: "*********"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

#重点检查重新打款，卡号不变的情况下，剩余打款次数也不变化
-
    name: 页面版查询随机金额业务进度5
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "ORGANFINISHED"]
      - eq: [content.data.message, "打款申请成功"]
      - eq: [content.data.remainTimes, $remainTimes]

-
    name: 页面版随机金额对公打款3--重新打款，银行卡号信息变化
    api: api/orgAuth/transferRandomAmountWeb.yml
    variables:
      cardNo: "123456780"
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

#重点检查重新打款，卡号变化的情况下，剩余打款次数减少
-
    name: 页面版查询随机金额业务进度6
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "PAID"]
      - eq: [content.data.message, "打款申请成功"]
      - le: [content.data.remainTimes, $remainTimes]

-
    name: 页面版随机金额校验2
    api: api/orgAuth/randomAmountWeb.yml
    variables:
      cash: 0.01
      finishCash: false
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版查询随机金额业务进度7
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "ORGANFINISHED"]
      - eq: [content.data.message, "打款申请成功"]
      - eq: [content.data.taskGroupDoing.0, "AUTHORIZATION"]

-
    name: 页面点击催审
    api: api/orgAuth/urge.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data, 1]

-
    name: realname接口查询运营支撑平台要审核的realnameid
    api: api/realname/queryOrgManualList2.yml
    extract:
      - realnameId1: content.data.list.0.realnameId
    validate:
      - eq: [status_code,200]
      - contains: [content.message, "成功"]
      - eq: [content.data.list.0.authType, 3]
      - eq: [content.data.list.0.name, $name]
      - eq: [content.data.list.0.bizSource, $bizSource]

-
    name: realname接口运营支撑平台拒绝审批
    api: api/realname/updatePsnManual.yml
    variables:
      serviceStatus: 1
      realnameId: $realnameId1
      auditorResult: 2
      auditingRemark: "1、填写的认证组织信息与证件照不一致；2、证件照不清晰"
    validate:
      - eq: [status_code,200]
      - eq: [content.serviceId, $realnameId1]

-
    name: 页面版查询随机金额业务进度8
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.auditing, "1、填写的认证组织信息与证件照不一致；2、证件照不清晰"]
      - eq: [content.data.process, "ORGANFINISHED"]
      - eq: [content.data.taskGroupFail.0, "AUTHORIZATION"]

-
    name: 页面版重新认证2
    api: api/orgAuth/paymentTaskFinish.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版查询随机金额业务进度9
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "ORGANFINISHED"]

#企业重构二期修改了需求，重新发起时无需二次打款
#-
#    name: 页面版随机金额对公打款2
#    api: api/orgAuth/transferRandomAmountWeb.yml
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 0]
#      - contains: [content.message, "成功"]
#      - contains: [content.data.flowId, $flowId]
#
#-
#    name: 页面版查询随机金额业务进度10
#    api: api/orgAuth/transferProcessForRandomAmount.yml
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 0]
#      - eq: [content.message, "成功"]
#      - eq: [content.data.process, "PAID"]
#      - eq: [content.data.message, "打款申请成功"]

#因为打款回填已经校验成功，重新认证的时候
-
    name: 页面版随机金额校验3
    api: api/orgAuth/randomAmountWeb.yml
    variables:
      cash: ""
      finishCash: true
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - contains: [content.data.flowId, $flowId]

-
    name: 页面版查询随机金额业务进度11
    api: api/orgAuth/transferProcessForRandomAmount.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.process, "ORGANFINISHED"]
      - eq: [content.data.message, "打款申请成功"]
      - eq: [content.data.taskGroupDoing.0, "AUTHORIZATION"]

-
    name: realname接口查询运营支撑平台要审核的realnameid2
    api: api/realname/queryOrgManualList2.yml
    extract:
      - realnameId2: content.data.list.0.realnameId
    validate:
      - eq: [status_code,200]
      - contains: [content.message, "成功"]
      - eq: [content.data.list.0.authType, 3]
      - eq: [content.data.list.0.name, $name]
      - eq: [content.data.list.0.bizSource, $bizSource]

-
    name: realname接口运营支撑平台同意审批
    api: api/realname/updatePsnManual.yml
    variables:
      realnameId: $realnameId2
      auditorResult: 1
      auditingRemark: null
      serviceStatus: 3
    validate:
      - eq: [status_code,200]
      - eq: [content.serviceId, $realnameId2]

#缓存问题，15分钟刷新后流程才会成功，待折袖修复
#    teardown_hooks:
#      - ${teardown_hook_sleep_n_secs(10)}
#
#-
#    name: 页面版查询随机金额业务进度12
#    api: api/orgAuth/transferProcessForRandomAmount.yml
#    validate:
#      - eq: [status_code,200]
#      - eq: [content.code, 0]
#      - eq: [content.message, "成功"]
#      - eq: [content.data.auditing, ""]
#      - eq: [content.data.process, "ORGANFINISHED"]
