config:
     name: 企业信息比对

testcases:
-
     name: 组织机构二要素比对
     testcase: testcases/prodTestcase/enterprise2.yml
     parameters:
         - CaseName-name-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州书契网络科技合伙企业（有限合伙）","91330108MA2KGB2B9B",0,"成功","-"]

-
     name: 企业三要素比对
     testcase: testcases/prodTestcase/enterprise3.yml
     parameters:
         - CaseName-name-legalRepName-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确，填18位codeUSC","杭州书契网络科技合伙企业（有限合伙）","金宏洲","91330108MA2KGB2B9B",0,"成功","-"]

-
     name: 企业四要素比对
     testcase: testcases/prodTestcase/enterprise4.yml
     parameters:
         - CaseName-name-legalRepName-legalRepCertNo-orgCode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确,填18位codeUSC","杭州书契网络科技合伙企业（有限合伙）","金宏洲","330722197904110013","91330108MA2KGB2B9B",0,"成功","-"]
