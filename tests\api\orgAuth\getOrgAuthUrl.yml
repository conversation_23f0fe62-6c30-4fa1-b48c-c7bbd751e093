name: 获取企业核身认证地址
request:
    url: /v2/identity/auth/web/orgAuthUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        authType: "ORG_BANK_TRANSFER"
        availableAuthTypes:
          - "ORG_BANK_TRANSFER"
          - "ORG_ZM_AUTHORIZE"
        agentAccountId: $agentAccountId
        contextInfo:
          contextId: "test"
          notifyUrl: ""
          origin: "BROWSER"
          redirectUrl: "http://www.baidu.com"
          showResultPage: "true"
        orgEntity:
          certNo: $certNo
          organizationType: "1"
          name: $name
          legalRepCertNo: $legalRepCertNo
          legalRepName: $legalRepName
        repeatIdentity: "true"