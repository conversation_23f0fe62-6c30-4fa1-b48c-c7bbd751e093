config:
    name: 获取个人实名的e签宝CA证书协议

testcases:
-
    name: 获取个人实名的e签宝CA证书协议-$CaseName
    testcase: testcases/agreement/psn_agreement.yml
    parameters:
        - CaseName-appId-accountId-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-repeatIdentity-objectType:
#            - ["v3认证，非7488发起个人实名，展示普通协议","${ENV(appId3)}","f3161ef149744366b2d5a9e4fa9588bc","",[],[],"武玉华","******************","INDIVIDUAL_CH_IDCARD","***********","****************",[],true,2]
            - ["v3认证，7488发起个人实名，展示标准协议","${ENV(7488appId)}","f3161ef149744366b2d5a9e4fa9588bc","",[],[],"武玉华","******************","INDIVIDUAL_CH_IDCARD","***********","****************",[],true,2]
