config:
    name: 个人银行卡四要素比对(实名)
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起银行四要素
    api: api/psnAuth/bankCard4FactorsOid.yml
    extract: 
        - flowId: content.data.flowId
    validate:
        - eq: [content.code, $VALIDATE1]
        - eq: [content.message, $VALIDATE2]

-
    name: 银行四要素验证码回填
    api: api/psnAuth/bank4VerifyCode.yml
    validate:
        - eq: [content.message, $VALIDATE3]



