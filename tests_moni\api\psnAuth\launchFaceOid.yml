name: 发起个人刷脸实名认证api
request:
    url: /v2/identity/auth/api/individual/$accountId/face
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        faceauthMode: $faceauthMode
        repetition: "true"
        callbackUrl: $callbackUrl
        contextId: $contextId
        notifyUrl: $notifyUrl