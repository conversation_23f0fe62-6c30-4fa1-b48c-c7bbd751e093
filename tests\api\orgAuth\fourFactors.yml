name: 发起企业实名核身4要素校验api
request:
    url: /v2/identity/auth/api/organization/enterprise/fourFactors
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        name: $name
        orgCode: $orgCode
        legalRepName: $legalRepName
        legalRepIdNo: $legalRepIdNo
        repetition: "true"
        contextId: ${create_randomId()}
        notifyUrl: ""
        legalRepCertType: $legalRepCertType
