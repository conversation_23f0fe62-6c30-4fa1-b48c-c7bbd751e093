config:
    name: 发起银行卡4要素核身认证-境外
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 发起银行卡4要素核身认证
    api: api/psnAuth/bankCard4Factors.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]
    json:
        bankCardNo: $bankCardNo
        mobileNo: $mobileNo
        contextId: "test"
        notifyUrl: ${ENV(notifyUrl)}
        repetition: $repetition
        grade: $grade