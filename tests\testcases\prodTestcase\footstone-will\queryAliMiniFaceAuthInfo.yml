- config:
    name: 发起刷脸认证并校验小程序刷脸结果
    base_url: ${ENV(footstone_will_url)}

- test:
    name: 发起刷脸认证
    api: api/prodApi/footstone-will/createFaceAuth_http.yml
    extract:
        - faceValue: content.data.faceValue
    validate:
      - eq: ["status_code",200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.bizId, None]

- test:
    name: 查询小程序刷脸认证信息
    api: api/prodApi/footstone-will/queryAliMiniFaceAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.status, "DOING"]
