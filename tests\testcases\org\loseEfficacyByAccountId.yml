config:
    name: 创建企业oid,发起企业实名认证4要素校验,通过oid使流程失效
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 创建企业oid
    api: api/public/createByThirdPartyUserId.yml
    extract:
        - accountId: content.data.orgId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.orgId, null]

-
    name: 发起企业实名认证4要素校验
    api: api/orgAuth/fourFactorsOid.yml
    extract:
      - flowId: content.data.flowId
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - contains: [content.message, "成功"]
        - ne: [content.data.flowId, null]

-
    name: 通过oid失效flowid
    api: api/public/loseEfficacyByAccountId.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]