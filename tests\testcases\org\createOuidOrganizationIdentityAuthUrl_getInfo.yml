config:
    name: 企业实名支持新增传入(RPC接口，认证授权对接)-验证经办人相关传参
    base_url: ${ENV(base_url_rpc)}

teststeps:
-
    name: 创建账号
    api: api/public/createByThirdPartyUserId_psnV1.yml
    extract:
      - agentAccountId: content.data.accountId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - ne: [content.data.accountId, null]

-
    name: 获取企业实名认证地址
    api: api/orgAuth/createOuidOrganizationIdentityAuthUrl.yml
    variables:
      - agentAccountId: $agentAccountId
      - certNo: ""
      - organizationType: 1
      - name: ""
      - legalRepCertType: ""
      - legalRepCertNo: ""
      - legalRepName: ""
      - authType: ""
      - availableAuthTypes: []
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]


-
    name: 查询经办人信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - eq: [content.data.mobileNo, $agentMobile]
      - eq: [content.data.bankCardNo, $agentBankCardNo]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.message, "成功"]
      - contains: [content.data.infoEditable.orgUneditableInfo, $VALIDATE1]