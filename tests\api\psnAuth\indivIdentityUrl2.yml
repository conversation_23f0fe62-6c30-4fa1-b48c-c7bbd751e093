name: 获取个人实名认证地址-********迭代之后新增参数，启用新接口
request:
    url: /v2/identity/auth/web/$accountId/indivIdentityUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: $appId
        X-Tsign-Open-Auth-Mode: simple
    json:
        contextInfo:
          contextId: "test"
          notifyUrl: ${ENV(notifyUrl)}
          origin: "BROWSER"
          redirectUrl: "https://www.baidu.cn"
          showResultPage: "true"
          showAgreement: $showAgreement
        indivInfo:
          name: $name
          certType: $certType
          certNo: $certNo
          mobileNo: $mobileNo
          bankCardNo: $bankCardNo
        configParams:
          indivUneditableInfo: $indivUneditableInfo
          indivEditableInfo: $indivEditableInfo
          billIsolationCode: "XESDEWXSAQAZ1"
        repeatIdentity: $repeatIdentity
