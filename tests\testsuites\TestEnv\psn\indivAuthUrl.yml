config:
    name: 获取个人核身认证地址

testcases:
-
    name: 获取个人核身认证地址-查询应用配置信息-$CaseName
    testcase: testcases/psn/indivAuthUrl_configAll.yml
    parameters:
        - CaseName-appId-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-project-VALIDATE1-VALIDATE2-VALIDATE3-VALIDATE4:
            - ["不指定默认认证类型和页面显示的认证方式","${ENV(appId)}","",[],[],"武玉华","******************","","***********","****************",[],"SaaS",8,"INDIVIDUAL_BANKCARD_4_FACTOR",['none'],"SaaS"]
            - ["指定默认认证类型为运营商三要素;name不可编辑","${ENV(appId)}","PSN_TELECOM_AUTHCODE",[],[],"武玉华","******************","","***********","****************",["name"],"钉签",8,"INDIVIDUAL_TELECOM_3_FACTOR",['name'],"钉签"]
            - ["指定默认认证类型为刷脸;certNo不可编辑","${ENV(appId)}","PSN_FACEAUTH_BYURL",[],[],"武玉华","******************","","***********","****************",[ "certNo"],"标准API",8,"INDIVIDUAL_FACEAUTH",['certNo'],"标准API"]
            - ["指定页面显示的认证方式,只有三要素；mobileNo不可以编辑","${ENV(appId)}","",["PSN_TELECOM_AUTHCODE"],[],"武玉华","******************","","***********","****************",["mobileNo"],"非标准API",1,"INDIVIDUAL_BANKCARD_4_FACTOR",['mobileNo'],"非标准API"]
            - ["指定页面显示的认证方式,只有三要素和四要素;bankCardNo不可编辑","${ENV(appId)}","",["PSN_TELECOM_AUTHCODE","PSN_BANK4_AUTHCODE"],[],"武玉华","******************","","***********","****************",["bankCardNo"],"天印混合云",2,"INDIVIDUAL_BANKCARD_4_FACTOR",['bankCardNo'],"天印混合云"]
            - ["指定默认认证类型为三要素，但可选方式中没有;全部参数不可编辑","${ENV(appId)}","PSN_TELECOM_AUTHCODE",["PSN_BANK4_AUTHCODE","PSN_FACEAUTH_BYURL"],[],"武玉华","******************","","***********","****************",["name", "certNo", "mobileNo", "bankCardNo"],"云平台",7,"INDIVIDUAL_BANKCARD_4_FACTOR",["name", "certNo", "mobileNo", "bankCardNo"],"云平台"]

-
    name: 获取个人核身认证地址-$CaseName
    testcase: testcases/psn/indivAuthUrl.yml
    parameters:
        - CaseName-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-project:
            - ["未填证件类型，默认身份证","",[],[],"武玉华","******************","","***********","****************",[],"测试"]
            - ["证件类型为身份证","",[],[],"武玉华","******************","INDIVIDUAL_CH_IDCARD","***********","****************",[],""]
            - ["证件类型为台胞证","",[],[],"田欣慈","********","INDIVIDUAL_CH_TWCARD","***********","6222620910051004855",[],""]
            - ["证件类型为护照","",[],[],"韩艾珊","H10561683","INDIVIDUAL_PASSPORT","***********","622908393301650316",[],""]
            - ["证件类型为澳门通行证","",[],[],"余洁婷","M0215994202","INDIVIDUAL_CH_HONGKONG_MACAO","***********","6214620321001228346",[],""]
            - ["英文名字中间支持空格","",[],[],"FAN HON SING","*********","INDIVIDUAL_PASSPORT","***********","****************",[],""]
            - ["英文名字中间支持半角逗号","",[],[],"FAN,","*********","INDIVIDUAL_PASSPORT","***********","****************",[],""]

-
    name: 页面版个人二要素-$CaseName
    testcase: testcases/psn/indivIdentity2FactorWeb.yml
    parameters:
        - CaseName-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-project:
            - ["未填证件类型，默认身份证","",[],[],"武玉华","******************","","***********","****************",[],"测试"]
            - ["证件类型为身份证","",[],[],"武玉华","******************","INDIVIDUAL_CH_IDCARD","***********","****************",[],""]

-
    name: 获取个人核身认证地址2-查询应用配置信息-$CaseName
    testcase: testcases/psn/indivAuthUrl2_configAll.yml
    parameters:
        - CaseName-appId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-showAgreement-VALIDATE1:
            - ["appid展示协议页面,参数配置true","${ENV(appId2)}","武玉华","******************","","***********","****************",[],"true",True]
            - ["appid展示协议页面,参数配置false","${ENV(appId)}","武玉华","******************","","***********","****************",[],"false",False]
            - ["appid展示协议页面,参数配置没有传参","${ENV(appId2)}","武玉华","******************","","***********","****************",[],"",True]
            - ["appid不展示协议页面,参数配置true","${ENV(appId)}","武玉华","******************","","***********","****************",[],"true",True]
            - ["appid不展示协议页面,参数配置false","${ENV(appId)}","武玉华","******************","","***********","****************",[],"false",False]
            - ["appid不展示协议页面,参数配置没有传参","${ENV(appId)}","武玉华","******************","","***********","****************",[],"",False]

-
    name: 根据flowId获取实名链接-$CaseName
    testcase: testcases/psn/getFlowUrl.yml
    parameters:
        - CaseName-authType-availableAuthTypes-authAdvancedEnabled-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-project:
            - ["未填证件类型，默认身份证","",[],[],"武玉华","******************","","***********","****************",[],"测试"]

-
    name: 获取个人核身认证地址2-查询认证方式是否要降级-$CaseName
    testcase: testcases/psn/indivAuthUrl2_downgrade.yml
    parameters:
        - CaseName-appId-name-certNo-certType-mobileNo-bankCardNo-indivUneditableInfo-showAgreement-VALIDATE1-VALIDATE2:
            - ["appid参数配置高优认证方式为刷脸","${ENV(appId10)}","武玉华","******************","","***********","****************",[],"true",True,True]
