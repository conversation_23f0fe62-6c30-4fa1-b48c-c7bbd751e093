config:
    name: 授权书-预览、获取、下载
    variables:
      - appId: '3438757422'
      - flowId0: '3098931938191739279'

teststeps:
-
    name: 预览授权书
    api: api/orgAuth/previewAuthorizationTemplate.yml
    variables:
        flowId: $flowId0
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - startswith: ["content.data", "https://"]

-
    name: 获取授权书-对公打款
    api: api/orgAuth/queryPassedFlowAuthFileInfo.yml
    variables:
        flowId: $flowId0
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - startswith: ["content.data.fileUrl", "https://"]

-
    name: 获取授权书-法人授权书
    api: api/orgAuth/queryPassedFlowAuthFileInfo.yml
    variables:
        flowId: '3130489446639078799'
    validate:
        - eq: [status_code,200]
        - eq: [content.code, 0]
        - startswith: ["content.data.fileUrl", "https://"]

#-
#    name: 下载授权书
#    api: api/orgAuth/sendAuthorizationEmail3.yml
#    variables:
#        flowId: $flowId0
#        email: <EMAIL>
#        authType: ''
#    validate:
#        - eq: [status_code,200]
#        - eq: [content.code, 0]
#        - startswith: ["content.data", "https://"]