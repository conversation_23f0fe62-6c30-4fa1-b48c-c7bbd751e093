config:
    name: 获取机构核身认证地址
    base_url: ${ENV(base_url)}
    variables:
      psnAccount: ***********
      orgName: esigntest0611测试

teststeps:
-
    name: 获取企业核身认证地址
    api: api/openApi/orgAuthUrl.yml
    variables:
      - json: {
    "authorizeConfig": {
        "authorizedScopes": [
            "manage_org_resource",
            "manage_psn_resource",
            "psn_initiate_sign"
        ]
    },
    "orgAuthConfig": {
        "orgAuthPageConfig": {
            "legalRepAvailableAuthModes": [
                "CODE_SMS", "PSN_MOBILE3"
            ]
        },
        "orgName": "${orgName}",
        "transactorInfo": {
            "psnAccount": "${psnAccount}"
        },
        "transactorAuthPageConfig": {
            "psnAvailableAuthModes": [
                "PSN_MOBILE3"
            ]
        }
    }
}
    extract:
      - authFlowId: content.data.authFlowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.authFlowId, null]
      - ne: [ content.data.authUrl, null ]
      - ne: [ content.data.authShortUrl, null ]

-
    name: 查询认证授权流程详情
    api: api/openApi/auth-flow_authFlowId.yml
    variables:
      authFlowId: ${authFlowId}
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.authFlowId, null]
      - ne: [ content.data.authUrl, null ]

