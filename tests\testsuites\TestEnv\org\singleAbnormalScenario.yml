config:
    name: 单接口的异常场景
#企业三要素主通道企信宝，四要素主通道敬众
testcases:
-
    name: 发起企业核身认证4要素校验异常场景-$CaseName
    testcase: testcases/org/fourFactors_ul.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-VALIDATE1-VALIDATE2:
            - ["异常场景-> name不正确","上海寻梦信息技术有限公司","913301087458306077","何一兵","110108196710262291","",********,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> orgCode-填18位codeUSC不正确","杭州天谷信息科技有限公司","91440300MA5ERJGK31","金宏洲","330722197904110013","",********,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> orgCode-填15位codeREG不正确","杭州天谷信息科技有限公司","440301103097411","金宏洲","330722197904110013","",********,"信息比对不通过,请检查信息的正确性"]
            - ["legalRepName不正确","杭州易签宝网络科技有限公司","91330108MA2KGB2B9B","张晋","330722197904110013","",********,"信息比对不通过,请检查信息的正确性"]
            - ["legalRepIdNo不正确","杭州天谷信息科技有限公司","913301087458306077","金宏洲","110108196710262291","",********,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> name为空","","91330108MA2KGB2B9B","金宏洲","330722197904110013","",********,"缺少参数：企业名称不能为空"]
            - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","","金宏洲","330722197904110013","",********,"缺少参数：组织机构代码不能为空"]
#330迭代做了空格处理            - ["异常场景-> 中文name中间含有空格","杭 州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","",********,"参数错误：组织机构名称含有空格，请检查企业名称正确性"]
            - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","44030110309741","金宏洲","330722197904110013","",********,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","91330108745830607","金宏洲","330722197904110013","",********,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","9133010874583060778","金宏洲","330722197904110013","",********,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> name大于190位","深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公","913301087458306077","何一兵","110108196710262291","",********,"参数错误：请输入正确长度的组织机构名称（不大于190位）"]

-
    name: 发起企业实名认证4要素校验异常场景-$CaseName
    testcase: testcases/org/fourFactorsOid_ul.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-VALIDATE1-VALIDATE2:
            - ["异常场景-企业名称为空","${ENV(creatorOid)}","","CRED_ORG_USCC","","330702198409120432","张晋",True,"",true,********,"缺少参数：企业名称不能为空"]
            - ["异常场景-企业证件号为空","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","","330702198409120432","张晋",True,"",true,********,"缺少参数：证件号"]
            - ["异常场景-企业法人证件号为空","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","","张晋",True,"",true,********,"缺少参数：法定代表人证件号"]
            - ["异常场景-企业法人名称为空","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","",True,"",true,********,"缺少参数：法定代表人姓名"]
            - ["异常场景-法人证件号不正确","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","432427189402182819","张晋",True,"",true,********,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-法人姓名不正确","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张张张",True,"",true,********,"信息比对不通过,请检查信息的正确性"]

-
    name: 发起企业实名认证4要素校验异常场景2-$CaseName
    testcase: testcases/org/fourFactorsOid_single_ul.yml
    parameters:
        - CaseName-creatorOid-accountId-frAuthEnable-legalCertType-repetition-VALIDATE1-VALIDATE2:
            - ["异常场景-经办人oid为空","","78d29fd4ea5d42799829578805d24a4d",True,"",true,********,"缺少参数：办理人账号不能为空"]
            - ["异常场景-经办人oid不正确","cbbd572081fe49abaa2a6be31d07dbf","78d29fd4ea5d42799829578805d24a4d",True,"",true,********,"账号不存在,请检查accountId的正确性"]
            - ["异常场景-经办人oid填入了企业oid","78d29fd4ea5d42799829578805d24a4d","78d29fd4ea5d42799829578805d24a4d",True,"",true,********,"为组织机构账号，请传入个人账号"]
            - ["异常场景-企业oid不正确","${ENV(creatorOid)}","78d29fd4ea5d42799829578805d24a4",True,"",true,********,"账号不存在,请检查accountId的正确性"]
            - ["异常场景-企业oid填入了经办人oid","${ENV(creatorOid)}","${ENV(creatorOid)}",True,"",true,********,"为个人账号，请传入组织机构账号"]
            - ["异常场景-入参格式不正确","${ENV(creatorOid)}","78d29fd4ea5d42799829578805d24a4d","","",true,********,"操作出错,请联系服务人员处理"]
            - ["异常场景-不允许重复实名","${ENV(creatorOid)}","78d29fd4ea5d42799829578805d24a4d",False,"",false,********,"当前账户已实名"]
            - ["异常场景-企业已注销","${ENV(creatorOid)}","6c5c4c7860d5452eb5074fd9087d5024",False,"",false,********,"账号不存在,请检查accountId的正确性"]
            - ["异常场景-经办人账号未实名","154a18e9939c49529dee2adf572bb53d","78d29fd4ea5d42799829578805d24a4d","","",true,********,"经办人账号未实名,请先完成个人实名认证"]

-
    name: 发起企业核身认证3要素校验异常场景-$CaseName
    testcase: testcases/org/threeFactors_ul.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-VALIDATE1-VALIDATE2:
            - ["异常场景-> name不正确","上海寻梦信息技术有限公司","913301087458306077","何一兵",********,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> orgCode-填18位codeUSC不正确","杭州天谷信息科技有限公司","91440300MA5ERJGK32","金宏洲",********,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> orgCode-填15位codeREG不正确","杭州天谷信息科技有限公司","***************","金宏洲",********,"信息比对不通过,请检查信息的正确性"]
            - ["legalRepName不正确","杭州天谷信息科技有限公司","913301087458306077","何哈哈哈",********,"信息比对不通过,请检查信息的正确性"]
            - ["异常场景-> name为空","","91330108MA2KGB2B9B","金宏洲",********,"缺少参数：企业名称不能为空"]
            - ["异常场景-> orgCode为空","杭州天谷信息科技有限公司","","金宏洲",********,"缺少参数：组织机构代码不能为空"]
            - ["异常场景-> 中文name中间含有空格","杭 州天谷信息科技有限公司","913301087458306077","金宏洲",********,"参数错误：组织机构名称含有空格，请检查企业名称正确性"]
            - ["异常场景-> orgCode长度小于15位","杭州天谷信息科技有限公司","44030110309741","金宏洲",********,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> orgCode长度大于15位但小于18位","杭州天谷信息科技有限公司","91330108745830607","金宏洲",********,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> orgCode长度大于18位","杭州天谷信息科技有限公司","9133010874583060778","金宏洲",********,"参数错误：请输入正确的组织机构证件号"]
            - ["异常场景-> name大于190位","深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公","913301087458306077","金宏洲",********,"参数错误：请输入正确长度的组织机构名称（不大于190位）"]

-
    name: 发起企业实名认证3要素校验异常场景-$CaseName
    testcase: testcases/org/threeFactorsOid_ul.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-repetition-VALIDATE1-VALIDATE2:
            - ["异常场景-企业名称为空","${ENV(creatorOid)}","","CRED_ORG_USCC","","330702198409120432","张晋",true,********,"缺少参数：企业名称不能为空"]
            - ["异常场景-企业证件号为空","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","","330702198409120432","张晋",true,********,"缺少参数：证件号"]
            - ["异常场景-企业法人名称为空","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","",true,********,"缺少参数：法定代表人姓名"]
            - ["异常场景-法人姓名不正确","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张一",true,********,"信息比对不通过,请检查信息的正确性"]

-
    name: 发起企业实名认证3要素校验异常场景2-$CaseName
    testcase: testcases/org/threeFactorsOid_single_ul.yml
    parameters:
        - CaseName-creatorOid-accountId-repetition-VALIDATE1-VALIDATE2:
            - ["异常场景-经办人oid为空","","78d29fd4ea5d42799829578805d24a4d",true,********,"缺少参数：办理人账号不能为空"]
            - ["异常场景-经办人oid不正确","cbbd572081fe49abaa2a6be31d07dbf","78d29fd4ea5d42799829578805d24a4d",true,********,"账号不存在,请检查accountId的正确性"]
            - ["异常场景-企业oid不正确","${ENV(creatorOid)}","78d29fd4ea5d42799829578805d24a4",true,********,"账号不存在,请检查accountId的正确性"]
            - ["异常场景-入参格式不正确","${ENV(creatorOid)}","78d29fd4ea5d42799829578805d24a4d","",********,"操作出错,请联系服务人员处理"]
            - ["异常场景-不允许重复实名","${ENV(creatorOid)}","78d29fd4ea5d42799829578805d24a4d",false,********,"当前账户已实名"]
            - ["异常场景-经办人账号未实名","154a18e9939c49529dee2adf572bb53d","78d29fd4ea5d42799829578805d24a4d",true,********,"经办人账号未实名,请先完成个人实名认证"]

-
    name: 申请企业支付宝一键实名异常场景-$CaseName
    testcase: testcases/org/alipayOneclickApply_ul.yml
    parameters:
        - CaseName-flowId-name-orgCode-device-VALIDATE1-VALIDATE2:
            - ["flowid不存在","123456","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","PC",********,"认证流程不存在"]
            - ["企业名称为空","${ENV(flowId1)}","","91330108MA2G042101","PC",********,"缺少参数：企业名称不能为空"]
            - ["企业证件号为空","${ENV(flowId1)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","","PC",********,"缺少参数：企业证件号不能为空"]
            - ["认证流程已结束","1756276786892954053","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","PC",30500002,"认证流程已结束"]

#-
#    name: 企业支付宝一键实名异常场景-$CaseName
#    testcase: testcases/org/alipayOneclickConfirm_ul.yml
#    parameters:
#        - CaseName-flowId-name-orgCode-device-token-authCodeAlipay-VALIDATE1-VALIDATE2:
#            - ["支付宝token为空","${ENV(flowId1)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","PC","","xingchenAuthcodeZbf001",30500001,"认证失败：查询支付宝企业信息失败"]
#            - ["企业名称不匹配","${ENV(flowId1)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","PC","xingchenAccessTokenAO001","xingchenAuthcodeZbf001",30500001,"企业认证失败, 企业名称不一致"]
#            - ["企业法人名称不匹配","${ENV(flowId1)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","PC","xingchenAccessTokenAO005","xingchenAuthcodeZbf005",30500001,"企业认证失败, 法定代表人姓名不一致"]
#            - ["企业法人证件号不匹配","${ENV(flowId1)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","PC","xingchenAccessTokenAO007","xingchenAuthcodeZbf007",30500001,"企业认证失败, 法定代表人证件号不一致"]
#            - ["企业证件号不匹配","${ENV(flowId1)}","esigntest杭z天-+~。（技有) 限 e ee 11 11","91330108MA2G042101","PC","xingchenAccessTokenAO008","xingchenAuthcodeZbf008",30500001,"企业认证失败, 企业证件号不一致"]
#可以补充case 认证流程已结束的 flowid用1756276786892954053
-
    name: 查询企业一键实名结果-$CaseName
    testcase: testcases/org/alipayOneclickResult_ul.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
            - ["认证流程不存在","123456",30500051,"请重新发起认证"]

-
    name: 发起授权签署核身认证异常场景-$CaseName
    testcase: testcases/org/legalRepSign_ul.yml
    parameters:
        - CaseName-flowId-agentIdNo-agentName-mobileNo-legalRepIdNo-VALIDATE1-VALIDATE2:
            - ["认证流程不存在","123456","410621198702274071","武玉华","13301053554","110108196710262291",********,"认证流程不存在"]
            - ["经办人身份证为空","${ENV(flowId2)}","","武玉华","13301053554","110108196710262291",********,"缺少参数：经办人身份证号不能为空"]
            - ["经办人身份证号格式不正确","${ENV(flowId2)}","41062119870227407","武玉华","13301053554","110108196710262291",********,"参数错误：经办人证件号暂不支持非大陆身份证"]
            - ["经办人名字为空","${ENV(flowId2)}","410621198702274071","","13301053554","110108196710262291",********,"缺少参数：经办人姓名不能为空"]
            - ["经办人手机号为空","${ENV(flowId2)}","410621198702274071","武玉华","","110108196710262291",********,"缺少参数：法定代表人手机号不能为空"]
            - ["经办人手机号格式不正确","${ENV(flowId2)}","410621198702274071","武玉华","1330105355","110108196710262291",********,"参数错误：请输入正确的手机号码"]
            - ["法人证件号格式不正确","${ENV(flowId2)}","410621198702274071","武玉华","13301053554","11010819671026229",********,"参数错误：证件格式异常"]
            - ["认证流程已结束","1756276786892954053","410621198702274071","武玉华","13301053554","110108196710262291",30500002,"认证流程已结束"]
            - ["格式校验不通过","${ENV(flowId2)}","4106211987 02274071","武玉华","13301053554","110108196710262291",********,"格式校验不通过，请检查填入信息是否有多余空格"]

-
    name: 发起授权签署实名认证异常场景-$CaseName
    testcase: testcases/org/legalRepSignAuth_ul.yml
    parameters:
        - CaseName-flowId-mobileNo-legalRepIdNo-VALIDATE1-VALIDATE2:
            - ["认证流程不存在","123456","13301053554","330702198409120432",********,"认证流程不存在"]
            - ["手机号为空","${ENV(flowId3)}","","330702198409120432",********,"缺少参数：法定代表人手机号不能为空"]
            - ["手机号格式不正确","${ENV(flowId3)}","1330105355","330702198409120432",********,"参数错误：请输入正确的手机号码"]
            - ["身份证号格式不正确","${ENV(flowId3)}","13301053554","33070219840912043",********,"参数错误：证件格式异常"]
            - ["认证流程已结束","1756276786892954053","13301053554","110108196710262291",30500002,"认证流程已结束"]
            - ["参数中含有空格","${ENV(flowId3)}","13301053554","33070219840 9120432",********,"格式校验不通过，请检查填入信息是否有多余空格"]

-
    name: 获取签署链接异常场景-$CaseName
    testcase: testcases/org/signUrl_ul.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
            - ["认证流程不存在","123456",********,"认证流程不存在"]
            - ["认证流程已结束","1756641205137234308",30500002,"认证流程已结束"]

#0915迭代回滚，暂时注释
-
    name: 获取签署链接异常场景2-$CaseName
    testcase: testcases/org/signUrl_ul2.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-VALIDATE1-VALIDATE2:
            - ["没有先调用发起授权接口而直接获取签署链接","深圳天谷信息科技有限公司","91440300MA5ERJGK30","张晋","330702198409120432","",30502031,"请先发起签署后，再获取签署链接"]

-
    name: 查询授权书签署状态异常场景-$CaseName
    testcase: testcases/org/signUrl_ul.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
            - ["认证流程不存在","123456",********,"认证流程不存在"]
            - ["认证流程已结束","1756641205137234308",30500002,"认证流程已结束"]

-
    name: 发起随机金额打款认证异常场景-$CaseName
    testcase: testcases/org/transferRandomAmount_ul.yml
    parameters:
        - CaseName-flowId-subbranch-cardNo-VALIDATE1-VALIDATE2:
            - ["认证流程不存在","123456","平安银行杭州高新支行","11003491675701",********,"认证流程不存在"]
            - ["认证流程已结束","1756276786892954053","平安银行杭州高新支行","11003491675701",30500002,"认证流程已结束"]
            - ["卡号为空","${ENV(flowId2)}","平安银行杭州高新支行","",********,"缺少参数：对公账号不能为空"]
            - ["开户支行为空","${ENV(flowId2)}","","11003491675701",********,"缺少参数：开户支行不能为空"]
            - ["入参含有空格","${ENV(flowId2)}","平安银行杭州高 新支行","11003491675701",********,"格式校验不通过，请检查填入信息是否有多余空格"]

-
    name: 查询随机金额打款进度异常场景-$CaseName
    testcase: testcases/org/transferProcess_ul.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
           - ["认证流程不存在","123456",********,"认证流程不存在"]

-
    name: 随机金额校验异常场景-$CaseName
    testcase: testcases/org/verifyRandomAmount_ul.yml
    parameters:
        - CaseName-flowId-amount-VALIDATE1-VALIDATE2:
            - ["认证流程不存在","123456","0.01",********,"认证流程不存在"]
            - ["认证流程已结束","1756276786892954053","0.01",30500002,"认证流程已结束"]
            - ["回填金额为空","${ENV(flowId2)}","",********,"缺少参数：打款金额不能为空"]

-
    name: 随机金额校验异常场景2-$CaseName
    testcase: testcases/org/transferRandomAmountAndVerify_ul.yml
    parameters:
        - CaseName-name-orgCode-legalRepName-legalRepIdNo-legalRepCertType-cardNo-subbranch-amount-VALIDATE1-VALIDATE2:
            - ["企业信息都正确(常规企业)","杭州天谷信息科技有限公司","913301087458306077","金宏洲","330722197904110013","INDIVIDUAL_CH_IDCARD","11003491675701","平安银行杭州高新支行","0.02",30502002,"金额校验失败"]

-
    name: 查询打款银行信息-$CaseName
    testcase: testcases/org/subbranch_ul.yml
    parameters:
        - CaseName-appId-flowId-subbranch-VALIDATE1-VALIDATE2:
            - ["认证流程不存在","${ENV(appId)}","123456","平安银行杭州高新支行",********,"认证流程不存在"]

-
    name: 获取组织机构核身认证地址-$CaseName
    testcase: testcases/org/orgAuthUrl_ul.yml
    parameters:
        - CaseName-authType-availableAuthTypes-agentAuthAdvancedEnabled-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-VALIDATE1-VALIDATE2:
            - ["认证方式不正确","123456",[],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",********,"参数错误：不支持的认证方式"]
            - ["agentAuthAdvancedEnabled非法枚举","ORG_BANK_TRANSFER",[],[""],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",********,"参数错误：agentAuthAdvancedEnabled非法枚举"]
#330迭代做了空格处理             - ["组织机构名称含有空格","ORG_BANK_TRANSFER",[],[],"91440300MA5ERJGK30",1,"深圳天谷信 息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",********,"参数错误：组织机构名称含有空格，请检查企业名称正确性"]
            - ["参数中含有多余空格","ORG_BANK_TRANS FER",[],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",********,"格式校验不通过，请检查填入信息是否有多余空格"]
            - ["企业名称大于190个字符","ORG_BANK_TRANSFER",[],[],"91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",********,"参数错误：请输入正确长度的组织机构名称（不大于190位）"]

-
    name: 获取组织机构实名认证地址-$CaseName
    testcase: testcases/org/orgIdentityUrlOid_ul.yml
    parameters:
        - CaseName-accountId-authType-availableAuthTypes-agentAuthAdvancedEnabled-agentAccountId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-repeatIdentity-VALIDATE1-VALIDATE2:
            - ["accountId不正确","123456","",[],[],"92cede5d8e924ca48c67c93137a04836","","","","","","","",********,"账号不存在,请检查accountId的正确性"]
            - ["认证方式不正确","78d29fd4ea5d42799829578805d24a4d","123456",[],[],"92cede5d8e924ca48c67c93137a04836","","","","","","","",********,"参数错误：不支持的认证方式"]
            - ["agentAuthAdvancedEnabled非法枚举","78d29fd4ea5d42799829578805d24a4d","",[],[""],"92cede5d8e924ca48c67c93137a04836","",1,"","INDIVIDUAL_CH_IDCARD","","",true,********,"参数错误：agentAuthAdvancedEnabled非法枚举"]
            - ["参数错误：输入证件号与账号不匹配","78d29fd4ea5d42799829578805d24a4d","ORG_BANK_TRANSFER",[],[],"92cede5d8e924ca48c67c93137a04836","91440300MA5ERJGK3",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",true,********,"参数错误：输入证件号与账号不匹配"]
            - ["经办人id为空","78d29fd4ea5d42799829578805d24a4d","ORG_BANK_TRANSFER",[],[],"","91440300MA5ERJGK30",1,"深圳天谷信息科技有限公司","INDIVIDUAL_CH_IDCARD","330702198409120432","张晋",true,********,"缺少参数：经办人账号Id不能为空"]

-
    name: 获取组织机构实名认证地址2-$CaseName
    testcase: testcases/org/orgIdentityUrlOid2_ul.yml
    parameters:
        - CaseName-appId-accountId-agentAccountId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-repeatIdentity-showAgreement-orgUneditableInfo-orgEditableInfo-VALIDATE1-VALIDATE2-agentBankCardNo-agentMobile:
            - ["可编辑和不可编辑参数冲突","${ENV(appId2)}","${ENV(accountId)}","92cede5d8e924ca48c67c93137a04836","","","","","","","",true,["name"],["name"],********,"参数错误：orgUneditableInfo和orgEditableInfo包含重复信息","",""]
            - ["企业名称大于190个字符","${ENV(appId2)}","${ENV(accountId)}","92cede5d8e924ca48c67c93137a04836","","","深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公司深圳天谷信息科技有限公","","","","",true,[],[],********,"参数错误：请输入正确长度的组织机构名称（不大于190位）","",""]

-
    name: 根据企业oid查询实名详情(法人章使用)-$CaseName
    testcase: testcases/org/queryOrganizationDetailByAccountId_ul.yml
    parameters:
        - CaseName-accountId-VALIDATE1-VALIDATE2:
            - ["accountId为空","",********,"缺少参数：accountId"]
            - ["accountId未实名","409a2703379d4f3a821ea867b0381633",********,"未实名"]
            - ["accountId不存在","409a2703379d4f3a821ea867b0381631",********,"账号不存在,请检查accountId的正确性"]

-
    name: 发起企业核身认证4要素校验_加入经办人flowid入参异常场景1-$CaseName
    testcase: testcases/org/fourFactors_addAgentFlowid_ul.yml
    parameters:
        - CaseName-orgName-orgCode-legalRepName-legalRepIdNo-legalRepCertType-agentFlowId-VALIDATE1-VALIDATE2:
            - ["异常场景-> agentFlowId不存在","杭州易签宝网络科技有限公司","91330108MA2GKFYB1X","程亮","330821198202051817","INDIVIDUAL_CH_IDCARD","12345",********,"认证流程不存在"]
            - ["异常场景-> agentFlowId传入了企业核身flowid","杭州天谷信息科技有限公司","91440300MA5ERJGK31","金宏洲","330722197904110013","","2408558897515733596",********,"经办人认证流程不可用，必须为个人认证流程，请重新进行经办人认证"]
            - ["异常场景-> agentFlowId传入了未完成的个人flowid","杭州天谷信息科技有限公司","91440300MA5ERJGK31","金宏洲","330722197904110013","","2408600770309524375",********,"经办人认证流程不可用，尚未完成认证，请重新进行经办人认证"]
            - ["异常场景-> agentFlowId传入了个人实名的flowid","esigntest杭州易签宝","9100000005673518HL","许华建","342622198905262396","","2408563252696457767",********,"经办人认证流程不可用，必须为核身认证流程，请重新进行经办人认证"]
            - ["异常场景-> agentFlowId传入了被其他流程关联的","esigntest杭州易签宝","9100000005809185C4","许华建","342622198905262396","","2408582474889694120",********,"经办人认证流程不可用，已关联过其他组织机构认证流程，请重新进行经办人认证"]

-
    name: 发起企业核身认证4要素校验_加入经办人flowid入参异常场景2-$CaseName
    testcase: testcases/org/fourFactors_addAgentFlowid_ul2.yml
    parameters:
        - CaseName-orgName-orgCode-legalRepName-legalRepIdNo-legalRepCertType-agentFlowId-VALIDATE1-VALIDATE2:
            - ["异常场景-> agentFlowId中个人名字与法人名字不一致","esigntest杭州易签宝","9100000004691814NR","程亮","342622198905262396","INDIVIDUAL_CH_IDCARD","2408582474889694120",30502020,"法定代表人与经办人姓名不一致"]
            - ["异常场景-> agentFlowId中个人证件号与法人证件号不一致","esigntest杭州易签宝","9100000004691814NR","许华建","330722197904110013","","2408582474889694120",30502021,"法定代表人与经办人证件号不一致"]
            - ["异常场景-> agentFlowId中个人证件类型与法人类型不一致","esigntest杭州易签宝","9100000005763562GW","许华建","342622198905262396","INDIVIDUAL_PASSPORT","2408582474889694120",30502022,"法定代表人与经办人证件类型不一致"]

-
    name: 获取企业实名流程中上传的打款授权书-$CaseName
    testcase: testcases/org/downloadAuthorization_ul.yml
    parameters:
        - CaseName-flowId-VALIDATE1-VALIDATE2:
            - ["异常场景-> flowid不存在","258373642469245089",********,"认证流程不存在"]
            - ["异常场景-> flowid不是企业流程","2579766038577549697",30502065,"flowId不是企业认证流程，请检查flowId后再试"]
            - ["异常场景-> flowid没有认证完成","2583806534966838347",30500010,"flowId未认证完成，请认证完成后再试"]
            - ["异常场景-> 传入的flowid完成了认证，但是不是打款的方式","2583809635077587978",30502066,"flowId认证方式不是打款，请检查flowId后再试"]
            - ["异常场景-> 传入的flowid是通过打款方式完成的认证，但没有上传授权书","2588344459922510233",30502067,"flowId未上传授权书，请检查flowId后再试"]

-
    name: 查询打款银行信息2-$CaseName
    testcase: testcases/org/subbranch2_ul.yml
    parameters:
        - CaseName-appId-flowId-subbranch-VALIDATE1-VALIDATE2:
            - ["联行号不是全数字","${ENV(appId)}","2766586264550907558","汉字",********,"参数错误：cnapsCode格式为全数字"]
            - ["联行号是空的","${ENV(appId)}","2766586264550907558","",********,"缺少参数：keyWord与cnapsCode不能同时为空"]
