config:
     name: realname rpc接口case集

testcases:
-
     name: 运营支撑平台查询供应商调用记录
     testcase: testcases/realname/recordQuery.yml
     parameters:
         - CaseName-timeStart-timeEnd-pageIndex-pageSize:
             - ["正常场景","1649902715000","1649949515000",0,10]

-
     name: 查询企业人工实名列表
     testcase: testcases/realname/queryOrgManualList.yml
     parameters:
         - CaseName-name-certType-certNo-realnameId-serviceStatus:
             - ["正常场景","杭州天谷信息科技有限公司","11","913301087458306077","500f27ae-f222-47cf-8a7a-35a821ee2389",1]

-
     name: 查询个人人工实名列表
     testcase: testcases/realname/queryOrgManualList.yml
     parameters:
         - CaseName-name-certType-certNo-realnameId-serviceStatus:
             - ["正常场景","毛生","1","513436200005119666","6ce48f6b-7ad1-4b5f-afb3-06e189bf2d85",3]

-
     name: 查询个人人工实名详情
     testcase: testcases/realname/queryPsnManualDetail.yml
     parameters:
         - CaseName-serviceId:
             - ["正常场景","6ce48f6b-7ad1-4b5f-afb3-06e189bf2d85"]

-
     name: 查询企业人工实名详情
     testcase: testcases/realname/queryOrgManualDetail.yml
     parameters:
         - CaseName-serviceId:
             - ["正常场景","6832244d-75db-4270-88b0-5131b396920e"]

-
     name: 根据serviceid查询单次实名业务
     testcase: testcases/realname/queryTidByRealnameId.yml
     parameters:
         - CaseName-serviceId-VALIDATE:
             - ["正常场景","d094006a-8005-48fe-8465-f5332c2ba472","PSN_BANKINFO_4"]
             - ["根据transactionId查","914094359094261602","PSN_BANKINFO_4"]
             - ["结果为空","947549211572860701",null]
             - ["正常场景2","9d3db04c-d6d6-4c6d-835a-7054677a8581","PSN_BANKINFO_4"]

-
     name: 根据transactionId获取serviceId list -存证用
     testcase: testcases/realname/querySidsByTransactionId.yml
     parameters:
         - CaseName-identityTransactionId:
             - ["正常场景-企业","914089470347736880"]
             - ["正常场景-个人","914090956087654234"]

-
     name: 查询最近一次反向打款订单记录
     testcase: testcases/realname/queryLastReversePaymentOrder.yml
     parameters:
         - CaseName-flowId:
             - ["正常场景-有记录","1192412052564638089"]
             - ["正常场景-无记录","1183790044952000184"]

-
     name: 查询反向打款记录
     testcase: testcases/realname/queryReversePaymentOrder.yml
     parameters:
         - CaseName-oppAccountName-tranTime-oppAccountNo:
             - ["正常场景","杭州天谷信息科技有限公司","********","***************"]

-
     name: 查询反向打款账单
     testcase: testcases/realname/queryReversePaymentBill.yml
     parameters:
         - CaseName-oppAccountName-flowId-oppAccountNo-currIndex-pageSize:
             - ["正常场景1","杭州天谷信息科技有限公司","","",0,20]
             - ["正常场景2","杭州天谷信息科技有限公司","1508675990048542556","",0,10]
             - ["正常场景3","杭州天谷信息科技有限公司","","***************",0,10]

-
     name: 查询打款金额校验记录
     testcase: testcases/realname/queryPayAuth.yml
     parameters:
         - CaseName-thprtpayOrderId:
             - ["正常场景-单条成功记录","f3cefcbb-e4bd-46d2-b207-c8b71b68c5d9"]
             - ["正常场景-多条失败记录","8c2e6d81-8bf0-4da7-a87c-0473edb091cc"]

-
     name: 查询打款订单实时状态
     testcase: testcases/realname/queryPayOrderRealtimeStatus.yml
     parameters:
         - CaseName-thprtpayOrderId:
             - ["正常场景","f3cefcbb-e4bd-46d2-b207-c8b71b68c5d9"]

-
     name: 查询打款订单记录
     testcase: testcases/realname/queryPayOrder.yml
     parameters:
         - CaseName-name-cardno-resultPayed-providerId-currentPage-pageSize:
             - ["正常场景1","","","","",1,10]
             - ["正常场景2","","","1","pingan",1,10]
             - ["正常场景3","杭州天谷信息科技有限公司","11003491675702","2","ZHAOSHANG",2,20]
#0522暂时屏蔽
#-
#     name: 快捷签个人认证记录查询
#     testcase: testcases/realname/quickSignQueryListByPersonal.yml
#     parameters:
#         - CaseName-queryType-idno-signTime-pageSize-currIndex:
#             - ["正常场景","2","362428198711110010","1578461678000",10,0]

-
     name: 快捷签企业认证记录查询
     testcase: testcases/realname/quickSignQueryListByOrg.yml
     parameters:
         - CaseName-queryType-certNo-signTime-pageSize-currIndex:
             - ["正常场景","1","913301087458306077","1582008414000",10,0]

-
     name: 根据serviceId查询刷脸信息
     testcase: testcases/realname/getAuthorizationDetail.yml
     parameters:
         - CaseName-serviceId:
             - ["正常场景","9bee18ac-4530-417e-9359-069cc822d573"]

#0522暂时屏蔽，数据太多，超时
#-
#     name: 快捷签个人实名记录查询
#     testcase: testcases/realname/queryPersonalQuickSign.yml
#     parameters:
#         - CaseName-queryType-idno-signTime-pageSize-currIndex:
#             - ["正常场景","2","362428198711110010","1578461678000",10,0]

-
     name: 快捷签企业实名记录查询
     testcase: testcases/realname/queryOrgInfoQuickSign.yml
     parameters:
         - CaseName-queryType-certNo-signTime-pageSize-currIndex:
             - ["正常场景","1","913301087458306077","1582008414000",10,0]

-
     name: 根据serviceId查询经办人信息
     testcase: testcases/realname/getOperatorInfoDetail.yml
     parameters:
         - CaseName-serviceId:
             - ["正常场景","f8806174-10c0-4de4-8c20-8ff6cb617116"]

-
     name: 个人根据证件号获取serviceId/比对信息
     testcase: testcases/realname/authInfoRecordQueryPsnInfo.yml
     parameters:
         - CaseName-queryType-idno-startTime-endTime-pageSize-currIndex:
             - ["正常场景1","2","360312199103011541","*************","1577955581000",10,0]
             - ["正常场景2","2","362428198711110010","1578461678000","1578462678000",10,0]
             - ["正常场景3","2","362428198711110010","1578461255000","1578461355000",10,0]
             - ["正常场景4","2","362428198711110010","1578461385000","",10,0]
             - ["正常场景5","2","362428198711110010","1578462011000","1578463028000",10,1]
             - ["正常场景6","2","","*************","1577955382000",10,0]
             - ["正常场景7","2","360312199103011541","","",10,0]

-
     name: 企业根据企业号获取serviceId
     testcase: testcases/realname/authInfoRecordQueryOrgInfo.yml
     parameters:
         - CaseName-queryType-certNo-startTime-endTime-pageSize-currIndex:
             - ["正常场景1-企业法人刷脸","1","91330302MA298CR4XP","1582098185000","1582099185000",1,0]
             - ["正常场景2-企业人工审核","1","913301087458306077","1582008414000","1582008424000",10,0]
             - ["正常场景3-企业信息比对","1","362428198711110010","1578461255000","1578461355000",10,0]
             - ["正常场景4-企业随机打款","1","91320506573772470Y","*************","*************",1,0]
             - ["正常场景5","1","913301087458306077","*************","*************",1,1]
             - ["正常场景6","1","92222403MA14UDL80B","*************","*************",1,0]
             - ["正常场景7","1","","*************","*************",10,0]
             - ["正常场景8","1","360312199103011541","","",10,0]

-
     name: 银行增加修改查询删除地区
     testcase: testcases/realname/bankCityDistrict.yml
     parameters:
         - CaseName-district-districtCode-parentCode-pageIndex-pageSize-district_update-districtCode_update:
             - ["正常场景1","集测区_${create_randomId()}","999999","420000",0,10,"集测区二_${create_randomId()}","888888"]

-
     name: 银行增加修改查询删除城市
     testcase: testcases/realname/bankCityCode.yml
     parameters:
         - CaseName-cityCode-district-districtCode-pageIndex-pageSize-cityCode_update:
             - ["正常场景1","${create_randomId()}","集测区_${create_randomId()}","999999",0,10,"${create_randomId()}"]

-
     name: 增加查询删除银行
     testcase: testcases/realname/bank.yml
     parameters:
         - CaseName-bankName-cityName-provinceName-cityCode-provinceCode-uniqueCode-subBankName:
             - ["正常场景1","test_BankName","test_CityName","test_ProvinceName","test_cityCode","test_ProvinceCode","${create_randomId()}","test_SubBankName"]

-
     name: 查询查询实名认证结果
     testcase: testcases/realname/authResult.yml
     parameters:
         - CaseName-serviceId-status:
             - ["正常场景1","fdc98074-b1d6-4153-9f84-4d1d90442057",3]

-
     name: 查询查询实名认证结果2
     testcase: testcases/realname/queryAuthResult.yml
     parameters:
         - CaseName-serviceId-status:
             - ["正常场景1","fdc98074-b1d6-4153-9f84-4d1d90442057",3]

-
     name: 终止正向打款流程
     testcase: testcases/realname/paymentTerminated.yml
     parameters:
         - CaseName-flowIds-realnameId:
             - ["正常场景","2246707503142800386","bede300c-42f6-446f-950a-f286aeeea5b1"]

-
     name: 终止反向打款流程
     testcase: testcases/realname/reversePaymentTerminated.yml
     parameters:
         - CaseName-flowIds-realnameId:
             - ["正常场景","2247659413135956530","8963568c-a6d8-4491-a3e4-b70c809ba28b"]

-
     name: 终止人工审核
     testcase: testcases/realname/artificialTerminated.yml
     parameters:
         - CaseName-flowIds-realnameId:
             - ["正常场景","2247668124084607493","338e1886-b5de-4104-a13c-5fea4b802de5"]

-
     name: 个人实名上传授权书
     testcase: testcases/realname/psnArtificial.yml
     parameters:
         - CaseName-name-idno-authMaterialType:
             - ["正常场景","测试个人","342622198905262396","authLetter"]

-
     name: 临时用例-查询反向打款记录
     testcase: testcases/realname/queryOrgReverseProcessPage.yml
     parameters:
         - CaseName:
             - ["正常场景"]

-
     name: 临时用例-查询反向打款数量
     testcase: testcases/realname/queryOrgReverseProcessCount.yml
     parameters:
         - CaseName-flowIds-realnameId:
             - ["正常场景"]

-
     name: 查询托管审批人
     testcase: testcases/realname/queryOperatorByParam.yml
     parameters:
         - CaseName-authType-appId:
             - ["正常场景",1,"${ENV(appId)}"]

-
     name: 查询企业人工实审核催审配置
     testcase: testcases/realname/orgManualConfigList.yml
     parameters:
         - CaseName-authType-pageIndex-pageSize:
             - ["正常场景",0,0,10]

#-
#     name: 发起法人授权签署&修改手机号
#     testcase: testcases/realname/startModOrgManualFrAuth.yml
#     parameters:
#       - CaseName-flowId-appId-frMobile-operator-operatorName-operatorMobile-newMobile:
#           - ["正常场景","3166878497176554025","7876697075","18755957250",
#              "nanhong","测试南鸿","13003761628","18075224390"]


