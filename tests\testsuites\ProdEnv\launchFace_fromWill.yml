config:
     name: 意愿认证刷脸相关case

testcases:

-
     name: HTTP发起刷脸认证并且校验刷脸结果-$CaseName
     testcase: testcases/prodTestcase/footstone-will/createFaceAuth_http.yml
     parameters:
         - CaseName-accountId-bizType-faceAuthMode-bizId:
             - ["发起刷脸认证-腾讯人脸","${ENV(accountId)}","SIGN","1",""]

-
     name: HTTP发起刷脸认证-$CaseName
     testcase: testcases/prodTestcase/footstone-will/createFaceAuth_http_single.yml
     parameters:
         - CaseName-accountId-bizType-faceAuthMode-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["发起刷脸认证-芝麻认证","${ENV(accountId)}","SIGN_HASH","2",0,"成功",null]
             - ["发起刷脸认证-芝麻支付宝小程序","${ENV(accountId)}","OPEN_API","3",0,"成功",null]
             - ["发起刷脸认证-支付宝小程序","${ENV(accountId)}","DING_APPROVAL_SEAL_AUTH","6",0,"成功",null]
             - ["发起刷脸认证-微信小程序","${ENV(accountId)}","USER_LOGOUT","12",0,"成功",null]
             - ["发起刷脸认证-钉钉刷脸","${ENV(accountId)}","DING_APPROVAL_TEMPLATE","9",0,"成功",null]
             - ["发起刷脸认证-e签宝刷脸","${ENV(accountId)}","SIGN_AUTH","10",0,"成功",null]
             - ["发起刷脸认证-智能视频认证","${ENV(accountId)}","REALNAME_OPREATOR_WILL","11",0,"成功",null]

#-
#     name: RPC发起刷脸认证并且校验刷脸结果-$CaseName
#     testcase: testcases/prodTestcase/footstone-will/createFaceAuth_rpc.yml
#     parameters:
#         - CaseName-accountId-bizType-faceAuthMode-bizId:
#             - ["发起刷脸认证-腾讯人脸","${ENV(accountId)}","SIGN","1",""]
#
#-
#     name: RPC发起刷脸认证-$CaseName
#     testcase: testcases/prodTestcase/footstone-will/createFaceAuth_rpc_single.yml
#     parameters:
#         - CaseName-accountId-bizType-faceAuthMode-VALIDATE1-VALIDATE2-VALIDATE3:
#             - ["发起刷脸认证-芝麻认证","${ENV(accountId)}","SIGN_HASH","2",0,"成功",null]
#             - ["发起刷脸认证-芝麻支付宝小程序","${ENV(accountId)}","OPEN_API","3",0,"成功",null]
#             - ["发起刷脸认证-支付宝小程序","${ENV(accountId)}","DING_APPROVAL_SEAL_AUTH","6",0,"成功",null]
#             - ["发起刷脸认证-微信小程序","${ENV(accountId)}","USER_LOGOUT","12",0,"成功",null]
#             - ["发起刷脸认证-钉钉刷脸","${ENV(accountId)}","DING_APPROVAL_TEMPLATE","9",0,"成功",null]
#             - ["发起刷脸认证-e签宝刷脸","${ENV(accountId)}","SIGN_AUTH","10",0,"成功",null]
#             - ["发起刷脸认证-智能视频认证","${ENV(accountId)}","REALNAME_OPREATOR_WILL","11",0,"成功",null]
#
#-
#     name: willauth串场景：刷脸获取地址-刷脸获取结果-意愿认证流水记录-查询意愿认证详情-查询意愿任务列表-$CaseName
#     testcase: testcases/prodTestcase/footstone-will/startFaceWillAuthByOid_scene.yml
#     parameters:
#         - CaseName-bizType-faceAuthMode-bizId-oid:
#             - ["正常场景-发起刷脸认证-腾讯人脸","SIGN","1","",""]
#
#-
#     name: willauth刷脸获取地址1-$CaseName
#     testcase: testcases/prodTestcase/footstone-will/startFaceWillAuthByOid.yml
#     parameters:
#         - CaseName-bizType-faceAuthMode:
#             - ["发起刷脸认证-芝麻认证","SIGN_HASH","2"]
#             - ["发起刷脸认证-芝麻支付宝小程序","OPEN_API","3"]
#             - ["发起刷脸认证-支付宝小程序","DING_APPROVAL_SEAL_AUTH","6"]
#             - ["发起刷脸认证-微信小程序","USER_LOGOUT","12"]
#             - ["发起刷脸认证-钉钉刷脸","DING_APPROVAL_TEMPLATE","9"]
#             - ["发起刷脸认证-e签宝刷脸","SIGN_AUTH","10"]
#             - ["发起刷脸认证-智能视频认证","REALNAME_OPREATOR_WILL","11"]
