config:
    name: singpass企业实名流程：授权人信息不正确，与发起人不一致
    variables:
        accountId: ${ENV(singpass_org_oid_notrealnamed)}
        agentAccountId: ${ENV(singpass_psn_oid_realname)}
        name: "singpass org ${create_randomIdStr(3)}"
        certNo: "220322TE02"
        legalRepName: "mogui"
        operatorName: "singpass test2"
        operatorName_incorrect: "singpass incorrect"
        operatorCertNo: "S8300002I"
        mobile: "***********"
        mobile2: "***********"

teststeps:
    -   name: 重置实名
        api: api/public/resetIdentity.yml
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]

    -   name: 发起企业实名
        api: api/rpc/singpassOrgApply.yml

        extract:
            -   url: content.data.url
            -   stateCode: state=(.*?)&
            -   code: code=(.*?)&
            -   serviceId: serviceId=(.*?)&
            -   flowId: content.data.flowId
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]
            -   len_gt: [content.data.flowId, 0]

    -   name: 查询企业实名流程：result=0
        api: api/rpc/singpassOrgResult.yml
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]
            -   eq: [content.data.flowId, $flowId]
            -   eq: [content.data.accountId, $accountId]
            -   eq: [content.data.result, 0]

    -   name: mock singpass登录认证
        api: api/realname/mockSingpassOrgLogin.yml
        variables:
            state: 1
            operatorMobile: $mobile
        validate:
            -   eq: [status_code,200]
            -   eq: [content.success, True]


#    -   name: 发起singpass回调
#        api: api/public/singpassCallback.yml
#        validate:
#            -   eq: [status_code,200]
#            -   eq: [headers.Content-Type, 'text/html']
#            -   contains: [url, "${ENV(singpass_org_callback)}"]

    -   name: 查询企业实名流程：result=1
        api: api/rpc/singpassOrgResult.yml
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, 0]
            -   eq: [content.message, "成功"]
            -   eq: [content.data.flowId, $flowId]
            -   eq: [content.data.accountId, $accountId]
            -   eq: [content.data.result, 4]
            -   eq: [content.data.info.name, $name]
            -   eq: [content.data.info.certNo, $certNo]
            -   eq: [content.data.info.operatorMobile, $mobile]


    -   name: 确认singpass认证信息
        api: api/rpc/singpassOrgConfirm.yml
        variables:
            confirm_state: 1
            operatorMobile: $mobile2
        validate:
            -   eq: [status_code,200]
            -   eq: [content.code, ********]
            -   eq: [content.message, "确认信息失败，请重试"]
