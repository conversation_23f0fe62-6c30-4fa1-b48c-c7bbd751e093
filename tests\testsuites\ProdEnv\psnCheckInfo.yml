config:
     name: 个人信息比对

testcases:
-
     name: 个人二要素比对
     testcase: testcases/prodTestcase/psn2.yml
     parameters:
         - CaseName-idNo-name-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","330522199009256911","吴越欣",0,"成功","-"]

-
     name: 个人运营商三要素比对
     testcase: testcases/prodTestcase/telecom3.yml
     parameters:
         - CaseName-dynamicAppid-name-idNo-mobileNo-VALIDATE1-VALIDATE2-VALIDATE3:
            - ["正常场景-> name正确","${ENV(appid)}","吴越欣","330522199009256911","***********",0,"成功","-"]

-
     name: 个人银行卡三要素比对
     testcase: testcases/prodTestcase/bank3.yml
     parameters:
         - CaseName-idNo-name-cardNo-VALIDATE1-VALIDATE2-VALIDATE3:
             - ["正常场景-> 信息都正确","330522199009256911","吴越欣","****************",0,"成功","-"]

-
     name: 个人银行卡四要素比对
     testcase: testcases/prodTestcase/bank4.yml
     parameters:
         - CaseName-idNo-name-cardNo-mobileNo-VALIDATE1-VALIDATE2-VALIDATE3:
#             - ["正常场景-> 信息都正确","330522199009256911","吴越欣","****************","***********",0,"成功","-"]
             - ["正常场景-> 信息都正确","342622198905262396","许华建","****************","***********",0,"成功","-"]
