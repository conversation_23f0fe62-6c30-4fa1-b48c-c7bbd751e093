name: 获取组织机构实名认证地址
request:
    url: /v2/identity/auth/web/$accountId/orgIdentityUrl
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
      agentAccountId: $agentAccountId
      contextInfo:
        contextId: "测试"
        origin: "BROWSER"
        redirectUrl: "http://baidu.com"
        showResultPage: "true"
      orgEntity:
        certNo: $certNo2
        organizationType: $organizationType2
        name: $name2
        legalRepCertType: $legalRepCertType2
        legalRepCertNo: $legalRepCertNo2
        legalRepName: $legalRepName2
        agentName: $agentName2
        agentIdNo: $agentIdNo2
        operatorType: $operatorType
        certType: $certType
      configParams:
        orgEditableInfo: ["certNo","name","legalRepCertType","legalRepCertNo","legalRepName","agentName","agentIdNo","certType"]