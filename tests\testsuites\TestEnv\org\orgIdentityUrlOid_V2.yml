config:
    name: 企业V2页面集测用例

testcases:
-
    name: 企业实名V2页面版对公打款--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV2_randomAmount.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType:
            - ["正常用例-常规企业统信码","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",""]
            - ["正常用例-常规企业15位工商注册码","${ENV(appId)}","${ENV(creatorOid)}","杭州天谷信息科技有限公司","CRED_ORG_REGCODE","330108000003512","330722197904110013","金宏洲","INDIVIDUAL_CH_IDCARD"]
            - ["正常用例-外籍法人-护照","${ENV(appId)}","${ENV(creatorOid)}","百达晶心精密机械（无锡）有限公司","CRED_ORG_USCC","91320214733305872J","E6409934C","黄亮茳（WEE LIANG KIANG）","INDIVIDUAL_PASSPORT"]
            - ["正常用例-外籍法人-港澳通行证","${ENV(appId)}","${ENV(creatorOid)}","深圳市锐玲商贸有限公司","CRED_ORG_USCC","91440300060276033X","H60159767","高英娣","INDIVIDUAL_CH_HONGKONG_MACAO"]

-
    name: 企业实名V2页面版反向打款--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV2_reverseRandomAmount.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType:
            - ["正常用例-常规企业统信码","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",""]
            - ["正常用例-常规企业15位工商注册码","${ENV(appId)}","${ENV(creatorOid)}","杭州天谷信息科技有限公司","CRED_ORG_REGCODE","330108000003512","330722197904110013","金宏洲","INDIVIDUAL_CH_IDCARD"]
            - ["正常用例-外籍法人-护照","${ENV(appId)}","${ENV(creatorOid)}","百达晶心精密机械（无锡）有限公司","CRED_ORG_USCC","91320214733305872J","E6409934C","黄亮茳（WEE LIANG KIANG）","INDIVIDUAL_PASSPORT"]
            - ["正常用例-外籍法人-港澳通行证","${ENV(appId)}","${ENV(creatorOid)}","深圳市锐玲商贸有限公司","CRED_ORG_USCC","91440300060276033X","H60159767","高英娣","INDIVIDUAL_CH_HONGKONG_MACAO"]

-
    name: 企业实名V2页面版企业支付宝一键实名--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV2_alipayOneclickConfirm.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType-token-authCodeAlipay:
            - ["正常用例-常规企业统信码","${ENV(appId)}","${ENV(creatorOid)}","杭州易签宝网络科技有限公司","CRED_ORG_USCC","91330108MA2GKFYB1X","330821198202051817","程亮","","xingchenAccessTokenAO001","xingchenAuthcodeZbf001"]
 #           - ["正常用例-外籍法人-护照","${ENV(appId)}","${ENV(creatorOid)}","百达晶心精密机械（无锡）有限公司","CRED_ORG_USCC","91320214733305872J","E6409934C","黄亮茳（WEE LIANG KIANG）","INDIVIDUAL_PASSPORT","xingchenAccessTokenAO006","xingchenAuthcodeZbf006"]
            - ["正常用例-特殊字符都支持，全半角括号兼容","${ENV(appId)}","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","330702198409120432","张晋","INDIVIDUAL_CH_IDCARD","xingchenAccessTokenAO005","xingchenAuthcodeZbf005"]

-
    name: 企业实名V2页面版法人授权书认证--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV2_frsign.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType-mobileNo:
            - ["正常用例-常规企业统信码","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋","","13888888888"]
            - ["正常用例-常规企业15位工商注册码","${ENV(appId)}","${ENV(creatorOid)}","杭州天谷信息科技有限公司","CRED_ORG_REGCODE","330108000003512","330722197904110013","金宏洲","INDIVIDUAL_CH_IDCARD","13888888888"]
#发起授权书有bug待修复            - ["正常用例-外籍法人-护照","${ENV(appId)}","${ENV(creatorOid)}","百达晶心精密机械（无锡）有限公司","CRED_ORG_USCC","91320214733305872J","E6409934C","黄亮茳（WEE LIANG KIANG）","INDIVIDUAL_PASSPORT","13888888888"]
            - ["正常用例-外籍法人-港澳通行证","${ENV(appId)}","${ENV(creatorOid)}","深圳市锐玲商贸有限公司","CRED_ORG_USCC","91440300060276033X","H60159767","高英娣","INDIVIDUAL_CH_HONGKONG_MACAO","13888888888"]

-
    name: 企业实名V2页面版法人本人认证--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV2_legalAuth.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType:
            - ["正常用例-常规企业统信码","${ENV(appId)}","${ENV(creatorOid)}","esigntest集测法人认证","CRED_ORG_USCC","9100000081924692H9","******************","武玉华",""]

-
    name: 企业实名V2页面版展示党政机关和事业单位--$CaseName
    testcase: testcases/org/orgIdentityUrlOidV2_governmentManual.yml
    parameters:
       - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-legalRepCertType-orgType-VALIDATE:
            - ["正常用例-党政机关","${ENV(appId)}","${ENV(creatorOid)}","禄劝彝族苗族自治县茂山镇人民政府","CRED_ORG_USCC","11530128015128752H","","李启昱","INDIVIDUAL_CH_IDCARD",4,"ZFJG"]
            - ["正常用例-事业单位","${ENV(appId)}","${ENV(creatorOid)}","台州市黄岩区南城街道社区卫生服务中心","CRED_ORG_USCC","12331003777244821X","","金军","INDIVIDUAL_CH_IDCARD",3,"SYDW"]

