name: 发起运营商三要素核身认证,核身意愿前置
request:
    url: /v2/identity/auth/api/individual/telecom3Factors
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        name: $name
        idNo: $idNo
        mobileNo: $mobileNo
        contextId: "1"
        notifyUrl: ${ENV(notifyUrl)}
        grade: "1"
        source: $source  #指定运营商3要素信息比对来源，默认0
#0 - 权威库比对，每次信息比对收取运营商3要素信息比对和短信费用
#1 - 可信数据比对，可信核身数据有效期内，仅收取短信费用，若无可信数据，接口拦截
#2 - 优先策略比对，优先进行可信数据源比对，若无可信数据，则进行权威库比对