- config:
    name: 查询商品搭售活动信息-正常场景
    base_url: ${ENV(base_url)}
    variables:
        - personal_gid: ${ENV(personal_gid)}
        - enterprise_gid: ${ENV(enterprise_gid)}
        - tenant_id: ${ENV(tenant_id)}
        - sale_schema_id: ${ENV(sale_schema_id)}

- test:
    name: 个人账号-支付宝小程序-查询搭售信息
    variables:
        - gid: ${personal_gid}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    extract:
        - tyingProducts: content.data.tyingProducts
        - canOrder: content.data.canOrder
        - firstProductId: content.data.tyingProducts.0.productId
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.message", "success"]
        - eq: ["content.success", true]
        - type_match: ["content.data.tyingProducts", list]
        - eq: ["content.data.canOrder", true]
        - contains: ["content.data.tyingProducts.0", "productName"]
        - contains: ["content.data.tyingProducts.0", "currentPrice"]
        - contains: ["content.data.tyingProducts.0", "originalPrice"]

- test:
    name: 个人账号-微信小程序-查询搭售信息
    variables:
        - gid: ${personal_gid}
        - tenant_id: ${tenant_id}
        - client_id: "WE_CHAT"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.data.canOrder", true]

- test:
    name: 企业账号-支付宝小程序-查询搭售信息
    variables:
        - gid: ${enterprise_gid}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    extract:
        - enterpriseCanOrder: content.data.canOrder
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - eq: ["content.data.canOrder", true]
        - type_match: ["content.data.tyingProducts", list]

- test:
    name: 验证搭售商品字段完整性
    variables:
        - gid: ${personal_gid}
        - tenant_id: ${tenant_id}
        - client_id: "ALI_PAY_MINI"
        - params: "saleSchemaId=${sale_schema_id}"
    api: api/billing/tying-scene/sale-info.yml
    validate:
        - eq: ["status_code", 200]
        - eq: ["content.code", "0"]
        - type_match: ["content.data.tyingProducts.0.productName", str]
        - type_match: ["content.data.tyingProducts.0.currentPrice", float]
        - type_match: ["content.data.tyingProducts.0.originalPrice", float]
        - type_match: ["content.data.tyingProducts.0.discountFlag", bool]
        - type_match: ["content.data.tyingProducts.0.specialOfferFlag", bool]
