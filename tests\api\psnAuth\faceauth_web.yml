name: 创建个人实名刷脸认证web
request:
    url: /v2/identity/auth/web/$flowId/faceauth
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
    json:
        faceAuthMode: $faceAuthMode
        subjectType: $subjectType
        isPC: $isPC
        callbackUrl: "https://testh5.tsign.cn/identity/person/result?id=d7fb4f356c9675a2a72e820bfaead87c&appId=1111563841&tsignversion&status=FACE_CALLBACK_PC&lang=zh-CN"
