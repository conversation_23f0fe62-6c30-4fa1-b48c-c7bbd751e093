name: 发起个人刷脸实名认证api
request:
    url: /v2/identity/auth/api/individual/$accountId/face
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: $appId
        X-Tsign-Open-Auth-Mode: simple
    json:
        faceauthMode: $faceauthMode
        repetition: $repetition
        callbackUrl: $callbackUrl
        contextId: "123456"
        notifyUrl: ${ENV(notifyUrl)}