config:
    name: 发起企业实名认证4要素校验

testcases:
-
    name: 发起企业实名认证4要素校验-法人授权书认证-$CaseName
    testcase: testcases/org/fourFactorsOid_legalRepSign.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-mobileNo-legalRepIdNo-VALIDATE1:
            - ["企业信息都正确(常规企业)","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",True,"",true,"***********","330702198409120432","INDIVIDUAL_CH_IDCARD"]

-
    name: 发起企业实名认证4要素校验-随机金额打款认证-$CaseName
    testcase: testcases/org/fourFactorsOid_transferRandomAmount.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-cardNo-subbranch-amount-VALIDATE1-VALIDATE2-bank:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",True,"",true,"**************","平安银行杭州高新支行","0.01","INDIVIDUAL_CH_IDCARD","CRED_PSN_CH_IDCARD","平安银行"]

-
    name: 发起企业实名认证4要素校验-反向打款认证-$CaseName
    testcase: testcases/org/fourFactorsOid_reversePayment.yml
    parameters:
        - CaseName-appId-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-VALIDATE1:
            - ["企业信息都正确(常规企业)","${ENV(appId)}","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",True,"",true,"INDIVIDUAL_CH_IDCARD"]

-
    name: 发起企业实名认证4要素校验-企业支付宝一键实名认证-$CaseName
    testcase: testcases/org/fourFactorsOid_alipayOneclickConfirm.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-device-token-authCodeAlipay-orgCode-VALIDATE1-VALIDATE2:
            - ["企业信息都正确(常规企业)","${ENV(creatorOid)}","杭州易签宝网络科技有限公司","CRED_ORG_USCC","91330108MA2GKFYB1X","330821198202051817","程亮",True,"",true,"PC","xingchenAccessTokenAO001","xingchenAuthcodeZbf001","91330108MA2GKFYB1X","INDIVIDUAL_CH_IDCARD","CRED_PSN_CH_IDCARD"]

-
    name: 发起企业实名认证4要素校验-法定代表人认证-$CaseName
    testcase: testcases/org/fourFactorsOid_legalRepAuth.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-mobileNo-legalRepIdNo-VALIDATE1-VALIDATE2:
            - ["企业信息都正确","${ENV(creatorOid)}","esigntest杭z天－＋～。（技有） 限 e ee 11 11","CRED_ORG_USCC","91330108MA2G042101","******************","武玉华",True,"INDIVIDUAL_CH_IDCARD",true,"***********","******************","INDIVIDUAL_CH_IDCARD","CRED_PSN_CH_IDCARD"]

-
    name: 发起企业实名认证4要素校验-人工审核-$CaseName
    testcase: testcases/org/fourFactorsOid_manual.yml
    parameters:
        - CaseName-creatorOid-name-idType-idNumber-orgLegalIdNumber-orgLegalName-frAuthEnable-legalCertType-repetition-email-serviceTypeName:
            - ["企业信息都正确(常规企业)","${ENV(creatorOid)}","深圳天谷信息科技有限公司","CRED_ORG_USCC","91440300MA5ERJGK30","330702198409120432","张晋",False,"INDIVIDUAL_CH_IDCARD",true,"<EMAIL>","ORG_ARTIFICIAL"]
