config:
    name: 个人银行四要素(实名)
testcases:
-
    name: 个人银行四要素-$CaseName
    testcase: testcases/psn/bank4_oid.yml
    parameters:
        - CaseName-accountId-mobileNo-bankCardNo-authcode-VALIDATE1-VALIDATE2-VALIDATE3:
            - ["正常场景-> 验证码正确","8f70b48cb6cf4f36b757606f0f823904","***********","****************","123456",0,"成功","成功"]
            - ["异常场景-> 验证码不正确","8f70b48cb6cf4f36b757606f0f823904","***********","****************","123455",0,"成功","验证码校验失败"]
            
            

