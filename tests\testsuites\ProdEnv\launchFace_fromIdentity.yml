config:
    name: 发起个人刷脸实名/核身认证并且查询认证结果

testcases:
-
    name: 发起个人刷脸核身认证-$CaseName
    testcase: testcases/prodTestcase/footstone-identity/launchFace.yml
    parameters:
        - CaseName-name-idNo-faceauthMode-callbackUrl-VALIDATE1:
            - ["芝麻刷脸","武玉华","******************","ZHIMACREDIT","http://www.baidu.com","FACEAUTH_ZMXY"]
            - ["微众刷脸","武玉华","******************","TENCENT","http://www.baidu.com","FACEAUTH_TECENT_CLOUD"]
            - ["自研刷脸","武玉华","******************","ESIGN","http://www.baidu.com","FACEAUTH_ESIGN"]

-
    name: 发起个人刷脸实名认证-$CaseName
    testcase: testcases/prodTestcase/footstone-identity/launchFaceOid.yml
    parameters:
        - CaseName-accountId-faceauthMode-repetition-callbackUrl-VALIDATE1-VALIDATE2:
            - ["芝麻刷脸","${ENV(creatorOid)}","ZHIMACREDIT",true,"http://www.baidu.com","FACEAUTH_ZMXY","武玉华"]
            - ["微众刷脸","${ENV(creatorOid)}","TENCENT",true,"http://www.baidu.com","FACEAUTH_TECENT_CLOUD","武玉华"]
            - ["自研刷脸","${ENV(creatorOid)}","ESIGN",true,"http://www.baidu.com","FACEAUTH_ESIGN","武玉华"]
