config:
    name: 获取个人核身认证地址2-查询应用配置信息
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取个人核身认证地址2
    api: api/psnAuth/indivAuthUrl2.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 查询降级信息
    api: api/psnAuth/getDowngradeInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.needDowngrade, $VALIDATE2]
