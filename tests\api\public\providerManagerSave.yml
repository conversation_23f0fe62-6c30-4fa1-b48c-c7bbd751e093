name: 编辑供应商配置
request:
    url: ${ENV(test_manage_url)}/provider/manager/save
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: ${ENV(appId)}
        X-Tsign-Open-Auth-Mode: simple
        Cookie: $Cookie
    json:
        id: $id
        name: $name
        type: $type
        desc: $desc
        url: $url
        account: $account
        signKey: $signKey
        verifyKey: null
        connectTimeout: 2000
        readTimeout: 5000
        tokenTtl: null
        mockUrl: null
        weight: $weight
        notFound: null
        errCode: $errCode
        extendJson: $extendJson
        operator: $operator
        qualityTag: $qualityTag


