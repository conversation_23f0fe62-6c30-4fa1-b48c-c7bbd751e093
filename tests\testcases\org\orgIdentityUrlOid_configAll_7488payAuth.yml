config:
    name: 获取组织机构实名认证地址-查询应用配置信息
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取组织机构实名认证地址
    api: api/orgAuth/orgIdentityUrlOid5.yml
    variables:
      - authType: ""
      - availableAuthTypes: []
      - agentAccountId: ${ENV(creatorOid)}
      - defaultAgentAuthType: ""
      - agentAvailableAuthTypes: []
    extract:
      - flowIdTmp: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 失效flowid
    api: api/public/loseEfficacyByFlowId.yml
    variables:
      - flowId: $flowIdTmp
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 获取组织机构实名认证地址2
    api: api/orgAuth/orgIdentityUrlOid5.yml
    variables:
      - authType: ""
      - availableAuthTypes: []
      - agentAccountId: ${ENV(creatorOid)}
      - defaultAgentAuthType: ""
      - agentAvailableAuthTypes: []
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 查询应用配置信息
    api: api/orgAuth/configAll.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.display.payAuthorization, $VALIDATE]
