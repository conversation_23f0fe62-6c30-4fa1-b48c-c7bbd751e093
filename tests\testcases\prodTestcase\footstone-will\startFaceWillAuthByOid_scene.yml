- config:
    name: 串场景：刷脸获取地址-刷脸获取结果-意愿认证流水记录-查询意愿认证详情-查询意愿任务列表
    base_url: ${ENV(willauth_url)}

- test:
    name: 刷脸获取地址
    api: api/prodApi/footstone-will/startFaceWillAuthByOid.yml
    extract:
      - willAuthId: content.data.willAuthId
    validate:
        - eq: [status_code, 200]
        - eq: [content.success, true]
        - contains: [content.message, "成功"]
        - ne: [content.data.faceValue, None]

- test:
    name: 刷脸获取结果
    api: api/prodApi/footstone-will/queryFaceWillAuthResult.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.success, true]
      - contains: [content.message, "成功"]
      - eq: [content.data.passed, false]
      - contains: [content.data.msg, "刷脸认证未完成"]

- test:
    name: 意愿认证流水记录
    api: api/prodApi/footstone-will/getWillBusinessFlowList.yml
    validate:
      - eq: [status_code, 200]
      - eq: [content.flowResultList.0.sysCode, "WILL_AUTH"]
      - ne: [content.flowResultList, None]

- test:
    name: 查询意愿认证详情
    api: api/prodApi/footstone-will/getWillDetail.yml
    validate:
        - eq: [status_code, 200]
        - eq: [content.status, 0]
        - contains: [content.downBizDesc, "人脸识别"]

- test:
    name: 查询意愿任务列表
    api: api/prodApi/footstone-will/getWillListByQuery.yml
    validate:
        - eq: [status_code, 200]
        - eq: [content.list.0.status, 0]
        - contains: [content.list.0.downBizDesc, "人脸识别"]
