name: 发起个人刷脸核身认证api
request:
    url: /v2/identity/auth/api/individual/face
    method: POST
    headers:
        Content-Type: application/json
        X-Tsign-Open-App-Id: $appId
        X-Tsign-Open-Auth-Mode: simple
    json:
        name: $name
        idNo: $idNo
        faceauthMode: $faceauthMode
        faceInterfaceType: $faceInterfaceType    #SDK
        faceSdkVersion": $faceSdkVersion   #基础版：BASIC   增强版：PLUS
        contextId: ""
        notifyUrl: ${ENV(notifyUrl)}
        callbackUrl: $callbackUrl
