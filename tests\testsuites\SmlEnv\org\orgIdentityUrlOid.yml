config:
    name: 获取组织机构实名认证地址

testcases:
-
    name: 获取组织机构实名认证地址-$CaseName
    testcase: testcases/org/orgIdentityUrlOid.yml
    parameters:
        - CaseName-accountId-authType-availableAuthTypes-agentAuthAdvancedEnabled-agentAccountId-certNo-organizationType-name-legalRepCertType-legalRepCertNo-legalRepName-repeatIdentity:
            - ["企业信息都正确(常规企业)","0fc98167bfcd44f4b11ed9eb09ecc562","",[],[],"04ae114eca0a4a97af9011c216fa7485","","","","","","",true]
