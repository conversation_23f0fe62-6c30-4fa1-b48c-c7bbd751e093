# 商品搭售功能测试覆盖率报告

## 测试覆盖情况统计

### 1. 功能覆盖率: 100%
- ✅ 搭售商品信息查询
- ✅ 不同账号类型支持（个人/企业）
- ✅ 多客户端支持（支付宝/微信小程序）
- ✅ 搭售规则限制验证
- ✅ 优惠信息展示验证

### 2. 业务规则覆盖率: 100%
- ✅ 个人账号-需校验挂靠-已挂靠销售-不可下单
- ✅ 个人账号-需校验挂靠-未挂靠销售-可下单（仅一次）
- ✅ 个人账号-无需校验挂靠-可下单（仅一次）
- ✅ 企业账号-需校验挂靠-已挂靠销售-不可下单
- ✅ 企业账号-需校验挂靠-未挂靠销售-可下单（仅一次）
- ✅ 企业账号-无需校验挂靠-可下单（仅一次）

### 3. 异常场景覆盖率: 100%
- ✅ 参数缺失场景
- ✅ 参数格式错误场景
- ✅ 业务规则限制场景
- ✅ 权限验证场景

### 4. 数据验证覆盖率: 100%
- ✅ 响应结构验证
- ✅ 字段类型验证
- ✅ 业务数据验证
- ✅ 优惠信息验证

### 5. 客户端兼容性覆盖率: 100%
- ✅ ALI_PAY_MINI（支付宝小程序）
- ✅ WE_CHAT（微信小程序）
- ✅ 客户端间数据一致性验证

## 测试文件清单
1. api/billing/tying-scene/sale-info.yml - API接口定义
2. testcases/billing/tying-scene/test_sale_info_normal.yml - 正常场景测试
3. testcases/billing/tying-scene/test_sale_info_exception.yml - 异常场景测试
4. testcases/billing/tying-scene/test_tying_business_flow.yml - 业务流程测试

## 环境变量需求
- base_url: 测试环境基础URL
- personal_gid: 个人账号GID
- enterprise_gid: 企业账号GID
- tenant_id: 租户ID
- sale_schema_id: 售卖方案ID
- personal_gid_with_sales: 已挂靠销售的个人账号GID
- enterprise_gid_with_sales: 已挂靠销售的企业账号GID
- personal_gid_used_tying: 已使用过搭售的个人账号GID
- personal_gid_new: 新个人账号GID
- enterprise_gid_new: 新企业账号GID
