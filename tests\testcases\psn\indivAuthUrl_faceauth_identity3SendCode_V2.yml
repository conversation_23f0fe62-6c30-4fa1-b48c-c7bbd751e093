config:
    name: 获取个人核身认证地址+页面版选择刷脸核身(刷脸流程未终结)
    base_url: ${ENV(base_url)}

teststeps:
-
    name: 获取个人核身认证地址
    api: api/psnAuth/indivAuthUrl3.yml
    extract:
      - flowId: content.data.flowId
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.flowId, null]

-
    name: 神策埋点信息采集
    api: api/public/recordAuthInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版查询个人认证信息
    api: api/psnAuth/getIndividualInfo.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.bankCardNo, $bankCardNo]
      - eq: [content.data.name, $name]
      - eq: [content.data.certNo, $certNo]

-
    name: 查询个人认证流程状态1
    api: api/psnAuth/queryIndivFlowidStatus.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowStatus, "INIT"]

-
    name: 用户授权记录
    api: api/public/authorizationRecord.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]

-
    name: 页面版刷脸前发起运营商三要素
    api: api/psnAuth/faceauthTelecom3FactorInfoAuth.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版刷脸前发起运营商三要素后发送验证码
    api: api/psnAuth/faceauthTelecom3FactorSendCode.yml
    variables:
      - sendType: ""
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版刷脸前发起运营商三要素后回填验证码
    api: api/psnAuth/faceauthTelecom3FactorCheckCode.yml
    variables:
      - authcode: 123456
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - eq: [content.data.pass, True]
      - eq: [content.data.flowId, $flowId]

-
    name: 页面版发起刷脸
    api: api/psnAuth/faceauth_web.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - ne: [content.data.authUrl, null]

-
    name: 以轮询的方式查询个人实名刷脸认证结果
    api: api/psnAuth/queryFaceauthPolling.yml
    validate:
      - eq: [status_code,200]
      - eq: [content.code, 0]
      - contains: [content.message, "成功"]
      - eq: [content.data.livingScore, null]
